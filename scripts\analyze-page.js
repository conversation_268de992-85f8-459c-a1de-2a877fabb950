const { chromium } = require('playwright');

(async () => {
    const browser = await chromium.launch();
    const page = await browser.newPage();

    try {
        // Use test environment URL and test data
        const baseUrl = process.env.BASE_URL || 'https://aeonstest.info';
        const productPath = 'products/aeons-ancient-roots-olive-oil';
        const url = `${baseUrl}/${productPath}`;
        
        console.log(`Analyzing page: ${url}`);
        
        await page.goto(url, {
            waitUntil: 'networkidle',
            timeout: 30000
        });

        // Wait for product form
        await page.waitForSelector('#sylius-product-adding-to-cart', {
            timeout: 30000
        });

        // Analyze page structure
        const analysis = await page.evaluate(() => {
            const structure = {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                purchaseOptions: {
                    containers: [],
                    options: [],
                    labels: []
                }
            };

            // Find all possible purchase option containers
            ['#sylius-product-adding-to-cart', '.purchase-options', '.product-options'].forEach(selector => {
                const container = document.querySelector(selector);
                if (container) {
                    structure.purchaseOptions.containers.push({
                        selector,
                        exists: true,
                        html: container.outerHTML
                    });
                }
            });

            // Find all possible purchase options
            [
                '.purchase-option',
                '[data-purchase-type]',
                '[data-variant-option-subscription]',
                'input[name="purchaseType"]',
                'button[data-purchase-type]'
            ].forEach(selector => {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    structure.purchaseOptions.options.push({
                        selector,
                        count: elements.length,
                        elements: Array.from(elements).map(el => ({
                            tagName: el.tagName.toLowerCase(),
                            type: el.getAttribute('data-purchase-type') || el.getAttribute('data-variant-option-subscription'),
                            classes: Array.from(el.classList),
                            attributes: Array.from(el.attributes).map(attr => ({
                                name: attr.name,
                                value: attr.value
                            })),
                            html: el.outerHTML
                        }))
                    });
                }
            });

            // Find all possible label elements
            [
                '.product-variant-label-info',
                '.purchase-option-label',
                '.ratio-title',
                '.variant-label'
            ].forEach(selector => {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    structure.purchaseOptions.labels.push({
                        selector,
                        count: elements.length,
                        elements: Array.from(elements).map(el => ({
                            text: el.textContent.trim(),
                            parentElement: el.parentElement ? {
                                tagName: el.parentElement.tagName.toLowerCase(),
                                classes: Array.from(el.parentElement.classList),
                                type: el.parentElement.getAttribute('data-purchase-type') || el.parentElement.getAttribute('data-variant-option-subscription')
                            } : null,
                            html: el.outerHTML
                        }))
                    });
                }
            });

            return structure;
        });

        // Output analysis
        console.log('\nPage Structure Analysis:');
        console.log(JSON.stringify(analysis, null, 2));

        // Provide recommendations
        console.log('\nRecommended Selectors:');
        
        // Analyze purchase option containers
        const containers = analysis.purchaseOptions.containers;
        if (containers.length > 0) {
            console.log('\nPurchase Option Container:');
            containers.forEach(container => {
                console.log(`- ${container.selector} (Found)`);
            });
        }

        // Analyze purchase options
        const options = analysis.purchaseOptions.options;
        if (options.length > 0) {
            console.log('\nPurchase Options:');
            options.forEach(option => {
                console.log(`- ${option.selector} (Found ${option.count} elements)`);
                option.elements.forEach((el, index) => {
                    console.log(`  Element ${index + 1}:`);
                    console.log(`    Type: ${el.type || 'none'}`);
                    console.log(`    Classes: ${el.classes.join(', ')}`);
                });
            });
        }

        // Analyze labels
        const labels = analysis.purchaseOptions.labels;
        if (labels.length > 0) {
            console.log('\nPurchase Option Labels:');
            labels.forEach(label => {
                console.log(`- ${label.selector} (Found ${label.count} elements)`);
                label.elements.forEach((el, index) => {
                    console.log(`  Label ${index + 1}: "${el.text}"`);
                    if (el.parentElement) {
                        console.log(`    Parent: ${el.parentElement.tagName} (${el.parentElement.classes.join(', ')})`);
                    }
                });
            });
        }

        await browser.close();
    } catch (error) {
        console.error('Analysis failed:', error);
        await browser.close();
        process.exit(1);
    }
})();