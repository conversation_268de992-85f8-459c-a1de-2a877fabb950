const ApiClient = require('../ApiClient');

class OrderApi {
    constructor() {
        this.client = new ApiClient({ useAuth: true });
    }

    async getOrder(orderNumber) {
        return this.client.get(`/api/v2/admin/orders/${orderNumber}`);
    }

    async updateOrderStatus(orderNumber, status) {
        return this.client.put(`/api/v2/admin/orders/${orderNumber}/state`, {
            state: status
        });
    }

    async getOrdersByCustomerEmail(email) {
        return this.client.get('/api/v2/admin/orders', {
            customer: email
        });
    }
}

module.exports = OrderApi;