/**
 * @fileoverview Purchase Flow Migration Test
 * 
 * Test to validate the new purchase flow fixture by migrating the main purchase test.
 * This demonstrates the DRY improvements and validates backward compatibility.
 * 
 * @tags @validation @purchase_flow @migration
 */

const { test, expect } = require('../fixtures/workflows/purchase-flow-fixture');

test.describe('Purchase Flow Migration Validation', () => {
    test.describe('Enhanced Purchase Flow Tests', () => {
        test('TC-001: Enhanced one-time purchase with Stripe @enhanced_purchase', async ({ 
            purchaseFlow, 
            testDataHelper, 
            browserStackHelper,
            deviceHelper 
        }) => {
            // Set timeout for purchase flow
            test.setTimeout(120000);

            console.log('[Migration Test] Starting enhanced purchase flow test');

            // Step 1: Get standardized test data
            await test.step('Prepare test data', async () => {
                const testData = testDataHelper.getPurchaseTestData('ancient_roots_olive_oil');
                
                // Validate test data
                expect(testData.product).toBeTruthy();
                expect(testData.user).toBeTruthy();
                expect(testData.paymentMethod).toBeTruthy();
                expect(testData.baseUrl).toBeTruthy();
                
                console.log(`Test data prepared for product: ${testData.product.name}`);
            });

            // Step 2: Execute complete purchase flow
            await test.step('Execute standard purchase flow', async () => {
                const testData = testDataHelper.getPurchaseTestData('ancient_roots_olive_oil');
                
                const results = await purchaseFlow.executeStandardPurchase(testData, {
                    purchaseType: 'oneTime',
                    paymentMethod: 'stripe',
                    verifyEmail: false, // Skip email verification for validation test
                    takeScreenshots: true,
                    mobileOptimized: deviceHelper.isMobile()
                });

                // Verify all steps completed successfully
                expect(results.navigation).toBeTruthy();
                expect(results.navigation.productName).toBe(testData.product.name);
                
                expect(results.productSelection).toBeTruthy();
                expect(results.productSelection.purchaseType).toBe('oneTime');
                
                expect(results.cartProcessing).toBeTruthy();
                expect(results.cartProcessing.cartProcessed).toBe(true);
                
                expect(results.shippingInfo).toBeTruthy();
                expect(results.shippingInfo.shippingInfoFilled).toBe(true);
                
                expect(results.payment).toBeTruthy();
                expect(results.payment.paymentMethod).toBe('stripe');
                expect(results.payment.paymentProcessed).toBe(true);
                
                expect(results.orderCompletion).toBeTruthy();
                expect(results.orderCompletion.orderNumber).toBeTruthy();
                
                console.log(`Purchase completed successfully with order number: ${results.orderCompletion.orderNumber}`);
            });
        });

        test('TC-002: Enhanced PayPal purchase flow @enhanced_paypal', async ({ 
            purchaseFlow, 
            testDataHelper,
            deviceHelper 
        }) => {
            test.setTimeout(180000); // Longer timeout for PayPal

            console.log('[Migration Test] Starting enhanced PayPal purchase flow test');

            await test.step('Execute PayPal purchase flow', async () => {
                const testData = testDataHelper.getPayPalTestData('ancient_roots_olive_oil');
                
                try {
                    const results = await purchaseFlow.executeStandardPurchase(testData, {
                        purchaseType: 'oneTime',
                        paymentMethod: 'paypal',
                        verifyEmail: false,
                        takeScreenshots: true,
                        mobileOptimized: deviceHelper.isMobile()
                    });

                    // Verify PayPal-specific results
                    expect(results.payment.paymentMethod).toBe('paypal');
                    expect(results.orderCompletion.orderNumber).toBeTruthy();
                    
                    console.log(`PayPal purchase completed: ${results.orderCompletion.orderNumber}`);
                } catch (error) {
                    // PayPal tests might fail due to sandbox limitations
                    console.warn(`PayPal test failed (expected in some environments): ${error.message}`);
                    
                    // Don't fail the test if it's a PayPal-specific issue
                    if (error.message.includes('PayPal') || error.message.includes('sandbox')) {
                        console.log('PayPal test skipped due to sandbox limitations');
                        test.skip();
                    } else {
                        throw error;
                    }
                }
            });
        });

        test('TC-003: Enhanced subscription purchase flow @enhanced_subscription', async ({ 
            purchaseFlow, 
            testDataHelper,
            deviceHelper 
        }) => {
            test.setTimeout(120000);

            console.log('[Migration Test] Starting enhanced subscription purchase flow test');

            await test.step('Execute subscription purchase flow', async () => {
                const testData = testDataHelper.getSubscriptionTestData('ancient_roots_olive_oil');
                
                const results = await purchaseFlow.executeStandardPurchase(testData, {
                    purchaseType: 'subscription',
                    paymentMethod: 'stripe',
                    verifyEmail: false,
                    takeScreenshots: true,
                    mobileOptimized: deviceHelper.isMobile()
                });

                // Verify subscription-specific results
                expect(results.productSelection.purchaseType).toBe('subscription');
                expect(results.orderCompletion.orderNumber).toBeTruthy();
                
                console.log(`Subscription purchase completed: ${results.orderCompletion.orderNumber}`);
            });
        });
    });

    test.describe('Backward Compatibility Validation', () => {
        test('Legacy test data patterns should work with enhanced fixture', async ({ 
            testDataManager, 
            pageObjectFactory,
            purchaseFlow 
        }) => {
            console.log('[Migration Test] Testing backward compatibility');

            await test.step('Validate legacy data access patterns', async () => {
                // Test legacy data access patterns
                const product = testDataManager.getProduct('ancient_roots_olive_oil');
                const user = testDataManager.getUser('default');
                const paymentMethod = testDataManager.getPaymentMethod('stripe_valid');
                
                expect(product).toBeTruthy();
                expect(user).toBeTruthy();
                expect(paymentMethod).toBeTruthy();
                
                // Test legacy page object access
                const allPageObjects = pageObjectFactory.getAll();
                expect(allPageObjects.productPage).toBeTruthy();
                expect(allPageObjects.cartPage).toBeTruthy();
                expect(allPageObjects.checkoutPage).toBeTruthy();
                
                console.log('Legacy patterns work correctly with enhanced fixture');
            });
        });

        test('Enhanced helpers should provide additional functionality', async ({ 
            testDataHelper,
            deviceHelper,
            browserStackHelper,
            emailHelper 
        }) => {
            console.log('[Migration Test] Testing enhanced helper functionality');

            await test.step('Validate enhanced helper methods', async () => {
                // Test enhanced test data helper
                const purchaseData = testDataHelper.getPurchaseTestData();
                const brandData = testDataHelper.getBrandSpecificData();
                
                expect(purchaseData.baseUrl).toBeTruthy();
                expect(purchaseData.brand).toBeTruthy();
                expect(brandData.emailDomain).toBeTruthy();
                
                // Test device helper
                const platformInfo = deviceHelper.getPlatformInfo();
                const timeoutMultiplier = deviceHelper.getTimeoutMultiplier();
                
                expect(platformInfo.deviceType).toBeTruthy();
                expect(timeoutMultiplier).toBeGreaterThan(0);
                
                // Test BrowserStack helper
                const sessionInfo = browserStackHelper.getSessionInfo();
                expect(sessionInfo).toBeTruthy();
                
                // Test email helper
                const orderSubject = emailHelper.getBrandOrderConfirmationSubject();
                expect(orderSubject).toBeTruthy();
                
                console.log('Enhanced helpers provide additional functionality correctly');
            });
        });
    });

    test.describe('Performance and Efficiency Validation', () => {
        test('Enhanced fixture should reduce code duplication', async ({ 
            purchaseFlow,
            testDataHelper 
        }) => {
            console.log('[Migration Test] Testing code efficiency improvements');

            await test.step('Compare code efficiency', async () => {
                // Measure time for standardized data preparation
                const startTime = Date.now();
                
                const testData1 = testDataHelper.getPurchaseTestData('ancient_roots_olive_oil');
                const testData2 = testDataHelper.getPayPalTestData('ancient_roots_olive_oil');
                const testData3 = testDataHelper.getSubscriptionTestData('ancient_roots_olive_oil');
                
                const dataPreparationTime = Date.now() - startTime;
                
                // Verify all data sets are properly prepared
                expect(testData1.product.name).toBe(testData2.product.name);
                expect(testData2.product.name).toBe(testData3.product.name);
                
                // Verify different configurations
                expect(testData2.paymentFlow).toBe('paypal');
                expect(testData3.subscriptionFrequency).toBe('monthly');
                
                console.log(`Data preparation completed in ${dataPreparationTime}ms`);
                console.log('Code duplication successfully eliminated through standardized helpers');
            });
        });

        test('Mobile optimization should work correctly', async ({ 
            deviceHelper,
            testDataHelper 
        }) => {
            console.log('[Migration Test] Testing mobile optimization');

            await test.step('Validate mobile-specific optimizations', async () => {
                const mobileTestData = testDataHelper.getMobileTestData();
                
                expect(mobileTestData.isMobile).toBe(true);
                expect(mobileTestData.extendedTimeouts).toBe(true);
                expect(mobileTestData.expectedTimeout).toBe(300000);
                
                // Test device-specific configurations
                const interactionOptions = deviceHelper.getInteractionOptions();
                expect(interactionOptions).toBeTruthy();
                
                const recommendedTimeout = deviceHelper.getRecommendedTimeout(30000);
                expect(recommendedTimeout).toBeGreaterThanOrEqual(30000);
                
                console.log('Mobile optimization features work correctly');
            });
        });
    });

    test.describe('Error Handling and Resilience', () => {
        test('Enhanced fixture should handle errors gracefully', async ({ 
            purchaseFlow,
            testDataHelper 
        }) => {
            console.log('[Migration Test] Testing error handling');

            await test.step('Test graceful error handling', async () => {
                // Test with invalid product key
                try {
                    const invalidTestData = testDataHelper.getPurchaseTestData('nonexistent_product');
                    console.log('Unexpected: Invalid product data was accepted');
                } catch (error) {
                    expect(error.message).toContain('not found');
                    console.log('Error handling works correctly for invalid product data');
                }
                
                // Test email verification graceful degradation
                const testData = testDataHelper.getPurchaseTestData();
                const emailResult = await purchaseFlow.verifyOrderConfirmationEmail(testData, {
                    timeout: 1000 // Very short timeout to trigger graceful failure
                });
                
                expect(emailResult.emailVerified).toBe(false);
                console.log('Email verification graceful degradation works correctly');
            });
        });
    });
});

test.describe('Migration Success Metrics', () => {
    test('Validate DRY implementation success', async ({ 
        testDataHelper,
        pageObjectFactory 
    }) => {
        console.log('[Migration Test] Validating DRY implementation success');

        await test.step('Measure code reuse and efficiency', async () => {
            // Test multiple data preparation calls to validate caching
            const startTime = Date.now();
            
            for (let i = 0; i < 5; i++) {
                testDataHelper.getPurchaseTestData('ancient_roots_olive_oil');
                pageObjectFactory.shop; // Access cached shop objects
            }
            
            const totalTime = Date.now() - startTime;
            
            // Should be very fast due to caching
            expect(totalTime).toBeLessThan(100); // Less than 100ms for 5 iterations
            
            // Test cache statistics
            const cacheStats = pageObjectFactory.getCacheStats();
            expect(cacheStats.size).toBeGreaterThan(0);
            
            console.log(`DRY implementation successful - 5 iterations completed in ${totalTime}ms`);
            console.log(`Page object cache efficiency: ${cacheStats.totalPageObjects} objects in ${cacheStats.size} groups`);
        });
    });
});
