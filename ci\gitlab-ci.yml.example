# Example GitLab CI/CD configuration for Playwright tests
# Save this file as .gitlab-ci.yml in your repository root

stages:
  - test

# Base job template for Playwright tests
.playwright_test_template:
  image: mcr.microsoft.com/playwright:v1.40.0-focal
  variables:
    # Use these variables to configure your tests
    CI: "true"
  before_script:
    - npm ci
    # Install required dependencies
    - npx playwright install --with-deps chromium firefox webkit
  artifacts:
    when: always
    paths:
      - test-results/
      - playwright-report/
    reports:
      junit: test-results/results.xml
    expire_in: 1 week

# Dev environment tests
test:dev:
  extends: .playwright_test_template
  stage: test
  script:
    - npm run ci:test
  variables:
    TEST_ENV: "dev"
  except:
    - tags
    - main
    - production

# Staging environment tests
test:staging:
  extends: .playwright_test_template
  stage: test
  script:
    - npm run ci:test:staging
  variables:
    TEST_ENV: "stage"
  only:
    - main
  except:
    - tags
    - production

# Mobile tests - Dev environment
test:mobile:dev:
  extends: .playwright_test_template
  stage: test
  script:
    - npm run ci:test:s23
  variables:
    TEST_ENV: "dev"
  except:
    - tags
    - main
    - production
  when: manual

# Mobile tests - Staging environment
test:mobile:staging:
  extends: .playwright_test_template
  stage: test
  script:
    - npm run ci:test:staging:s23
  variables:
    TEST_ENV: "stage"
  only:
    - main
  except:
    - tags
    - production
  when: manual

# Run all browsers in staging
test:staging:all-browsers:
  extends: .playwright_test_template
  stage: test
  script:
    - npm run ci:test:staging:all-browsers
  variables:
    TEST_ENV: "stage"
  only:
    - tags
  when: manual

# Run all devices in staging (browsers + mobile)
test:staging:all-devices:
  extends: .playwright_test_template
  stage: test
  script:
    - npm run ci:test:staging:all-devices
  variables:
    TEST_ENV: "stage"
  only:
    - tags
  when: manual

# Production smoke test (Chromium only)
test:production:
  extends: .playwright_test_template
  stage: test
  script:
    - cross-env TEST_TYPE=smoke TEST_DATA_SET=production TEST_ENV=prod npx playwright test --project=chromium --grep @smoke
  variables:
    TEST_ENV: "prod"
  only:
    - production
  when: manual  # Production tests are manual to avoid unexpected runs 