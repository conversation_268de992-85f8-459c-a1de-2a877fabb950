/**
 * @file Product Content Tests
 */

const { test, expect } = require('@playwright/test');
const { PRODUCT_CONTENT_RULES } = require('../../common/fixtures/content-rules');

test.describe('Product Content Verification @dss', () => {
    let productPage;

    test('Content verification - retinol-serum', async ({ page }) => {
        productPage = new ProductPage(page);
        await productPage.gotoProductPage('retinol-serum');
        const content = await productPage.getProductContent();
        // Add assertions to verify content based on your requirements
    });

    test('Text content quality - retinol-serum', async ({ page }) => {
        productPage = new ProductPage(page);
        await productPage.gotoProductPage('retinol-serum');
        const textContent = await productPage.getProductTextContent();
        // Add assertions to verify text content quality
    });

    test('Content verification - vitamin-c-serum', async ({ page }) => {
        productPage = new ProductPage(page);
        await productPage.gotoProductPage('vitamin-c-serum');
        const content = await productPage.getProductContent();
        // Add assertions to verify content
    });

    test('Text content quality2 - vitamin-c-serum', async ({ page }) => {
        productPage = new ProductPage(page);
        await productPage.gotoProductPage('vitamin-c-serum');
        const textContent = await productPage.getProductTextContent();
        // Add assertions to verify text content
    });

    for (const [productHandle, rules] of Object.entries(PRODUCT_CONTENT_RULES)) {
        test(`Content verification1 - ${productHandle}`, async ({ page }) => {
            productPage = new ProductPage(page);
            await productPage.gotoProductPage(productHandle);

            // Verify basic product information
            const productInfo = await productPage.getProductInfo();
            expect(productInfo.title, 'Product title should not be empty').toBeTruthy();
            expect(productInfo.price, 'Product price should not be empty').toBeTruthy();
            expect(productInfo.description, 'Product description should not be empty').toBeTruthy();

            // Verify product images
            const images = await productPage.getProductImages();
            expect(images.length, 'Should have multiple product images').toBeGreaterThan(1);

            // Capture gallery state for visual analysis
            await testHelper.captureAndAnalyze(page, `${productHandle}_product_gallery`, {
                testName: testInfo.title,
                section: 'gallery'
            });

            // Verify image alt text
            for (const image of images) {
                expect(image.alt, 'Images should have descriptive alt text').toBeTruthy();
                expect(image.alt.length, 'Alt text should be descriptive').toBeGreaterThan(10);
            }

            // Verify required sections
            for (const section of rules.requiredSections) {
                const hasSection = await productPage.hasSection(section);
                expect(hasSection, `${section} section should exist`).toBeTruthy();

                const content = await productPage.getSectionContent(section);
                expect(content.length, `${section} should have content`).toBeGreaterThan(0);

                // Scroll to section and capture for analysis
                await productPage.scrollToSection(section);
                await testHelper.captureAndAnalyze(page, `${productHandle}_${section}`, {
                    testName: testInfo.title,
                    section
                });
            }

            // Verify content rules
            for (const rule of rules.contentRules) {
                const content = await productPage.getSectionContent(rule.section);
                const normalizedContent = content.trim().toLowerCase();

                // Perform content checks
                if (rule.minLength) {
                    expect(content.length, `${rule.section} content should meet minimum length`)
                        .toBeGreaterThanOrEqual(rule.minLength);
                }
                if (rule.maxLength) {
                    expect(content.length, `${rule.section} content should not exceed maximum length`)
                        .toBeLessThanOrEqual(rule.maxLength);
                }
                for (const word of rule.mustInclude || []) {
                    expect(normalizedContent, `${rule.section} should include "${word}"`)
                        .toContain(word.toLowerCase());
                }
                for (const word of rule.cannotInclude || []) {
                    expect(normalizedContent, `${rule.section} should not include "${word}"`)
                        .not.toContain(word.toLowerCase());
                }
            }

            // Verify meta tags
            const metaTags = await productPage.getMetaTags();
            for (const [tag, content] of Object.entries(metaTags)) {
                expect(content, `Meta tag ${tag} should not be empty`).toBeTruthy();
                if (rules.metaTags?.[tag]?.minLength) {
                    expect(content.length, `Meta tag ${tag} should meet minimum length`)
                        .toBeGreaterThanOrEqual(rules.metaTags[tag].minLength);
                }
            }
        });

        test(`Text content quality1 - ${productHandle}`, async ({ page }) => {
            productPage = new ProductPage(page);
            await productPage.gotoProductPage(productHandle);

            for (const rule of rules.textQualityRules || []) {
                // Scroll to section and capture for analysis
                await productPage.scrollToSection(rule.section);
                await testHelper.captureAndAnalyze(page, `${productHandle}_${rule.section}_quality`, {
                    testName: testInfo.title,
                    section: rule.section,
                    type: 'text-quality'
                });

                const content = await productPage.getSectionContent(rule.section);
                const normalizedContent = content.trim().toLowerCase();

                // Verify text quality rules
                if (rule.sentenceCount) {
                    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
                    expect(sentences.length, `${rule.section} should have appropriate number of sentences`)
                        .toBeGreaterThanOrEqual(rule.sentenceCount.min || 0);
                    if (rule.sentenceCount.max) {
                        expect(sentences.length).toBeLessThanOrEqual(rule.sentenceCount.max);
                    }
                }

                if (rule.paragraphCount) {
                    const paragraphs = content.split(/\n\n+/).filter(p => p.trim().length > 0);
                    expect(paragraphs.length, `${rule.section} should have appropriate number of paragraphs`)
                        .toBeGreaterThanOrEqual(rule.paragraphCount.min || 0);
                    if (rule.paragraphCount.max) {
                        expect(paragraphs.length).toBeLessThanOrEqual(rule.paragraphCount.max);
                    }
                }

                // Check for prohibited phrases
                for (const phrase of rule.prohibitedPhrases || []) {
                    expect(normalizedContent, `${rule.section} should not contain "${phrase}"`)
                        .not.toContain(phrase.toLowerCase());
                }

                // Check for required phrases
                for (const phrase of rule.requiredPhrases || []) {
                    expect(normalizedContent, `${rule.section} should contain "${phrase}"`)
                        .toContain(phrase.toLowerCase());
                }
            }
        });
    }
});
