/**
 * @fileoverview Orchestrates the analysis process using Gemini AI
 */

const { GeminiService } = require('../service/GeminiService');
const { ImageProcessor } = require('../processors/ImageProcessor');
const { ResultProcessor } = require('../processors/ResultProcessor');
const { VisualAnalyzer } = require('../analyzers/VisualAnalyzer');
const { ContentAnalyzer } = require('../analyzers/ContentAnalyzer');
const { PerformanceAnalyzer } = require('../analyzers/PerformanceAnalyzer');
const { ErrorAnalyzer } = require('../analyzers/ErrorAnalyzer');
const { BrowserStackHelper } = require('../../browserstack/browserstack-helper');

class AnalysisOrchestrator {
    /**
     * Constructor for AnalysisOrchestrator.
     * @param {Object} options - Options for the analysis.
     */
    constructor(options = {}) {
        this.geminiService = new GeminiService();
        this.imageProcessor = new ImageProcessor();
        this.resultProcessor = new ResultProcessor();
        this.visualAnalyzer = new VisualAnalyzer(this.geminiService);
        this.contentAnalyzer = new ContentAnalyzer(this.geminiService);
        this.performanceAnalyzer = new PerformanceAnalyzer(this.geminiService);
        this.errorAnalyzer = new ErrorAnalyzer(this.geminiService);
        this.options = options; // Options such as 'includeVisualAnalysis'
        this.browserStackHelper = new BrowserStackHelper();
    }

    /**
     * Analyze test results and generate a comprehensive report.
     * @param {Object} testInfo - Playwright test info object.
     * @param {Object} artifacts - Test artifacts, including screenshots.
     * @returns {Promise<Object>} - Analysis report.
     */
    async analyze(testInfo, artifacts) {
        try {
            // Ensure testInfo and artifacts have expected structure
            testInfo = testInfo || {};
            artifacts = artifacts || {};
            
            const analysisResults = {};

            // Get BrowserStack artifacts if sessionId is available
            if (artifacts.browserStackSessionId) {
                try {
                    const bsArtifacts = await this.browserStackHelper.getSessionArtifacts(
                        artifacts.browserStackSessionId
                    );
                    
                    if (bsArtifacts) {
                        analysisResults.browserstack = bsArtifacts;
                        
                        // Get latest screenshot URL for visual analysis
                        const latestScreenshot = await this.browserStackHelper.getLatestScreenshotUrl(
                            artifacts.browserStackSessionId
                        );
                        
                        if (latestScreenshot) {
                            artifacts.screenshots = artifacts.screenshots || [];
                            artifacts.screenshots.push(latestScreenshot);
                        }
                    }
                } catch (bsError) {
                    console.warn('Error retrieving BrowserStack artifacts:', bsError);
                }
            }

            // Check if we have Cloudinary URLs in screenshots
            const hasCloudinaryUrls = Array.isArray(artifacts.screenshots) && 
                artifacts.screenshots.some(screenshot => 
                    typeof screenshot === 'string' && screenshot.includes('cloudinary.com')
                );

            // Functional Analysis (always included)
            try {
                analysisResults.functional = await this.analyzeFunctionalResults(testInfo);
            } catch (funcError) {
                console.error('Error during functional analysis:', funcError);
                analysisResults.functional = { error: 'Functional analysis failed' };
            }

            // Visual Analysis (conditional)
            if (this.options.includeVisualAnalysis && artifacts.screenshots && artifacts.screenshots.length > 0) {
                try {
                    if (hasCloudinaryUrls) {
                        // Use direct Cloudinary URL analysis
                        analysisResults.visual = await this.analyzeVisualElementsFromUrls(artifacts.screenshots);
                    } else {
                        // Use traditional approach with local files
                        analysisResults.visual = await this.analyzeVisualElements(artifacts.screenshots);
                    }
                } catch (visualError) {
                    console.error('Error during visual analysis:', visualError);
                    analysisResults.visual = [{ error: 'Visual analysis failed', details: visualError.message }];
                }
            }

            // Performance Analysis (conditional)
            if (this.options.includePerformanceAnalysis && artifacts.performanceData) {
                try {
                    analysisResults.performance = await this.analyzePerformanceData(artifacts.performanceData);
                } catch (perfError) {
                    console.error('Error during performance analysis:', perfError);
                    analysisResults.performance = [{ error: 'Performance analysis failed', details: perfError.message }];
                }
            }

            // Error Analysis
            if (testInfo.status === 'failed' || (testInfo.errors && testInfo.errors.length > 0)) {
                try {
                    analysisResults.errors = await this.analyzeErrors(testInfo, artifacts);
                } catch (errorAnalysisError) {
                    console.error('Error during error analysis:', errorAnalysisError);
                    analysisResults.errors = [{ error: 'Error analysis failed', details: errorAnalysisError.message }];
                }
            }

            // Aggregate results using ResultProcessor
            try {
                analysisResults.visual?.forEach(result => this.resultProcessor.addVisualAnalysis(result.screenshot, result.analysis));
                analysisResults.performance?.forEach(result => this.resultProcessor.addPerformanceData(result.data, result.analysis));
                analysisResults.errors?.forEach(result => this.resultProcessor.addError(result.error, result.analysis));
            } catch (processingError) {
                console.error('Error during result processing:', processingError);
            }

            // Add BrowserStack artifacts to the report
            if (analysisResults.browserstack) {
                try {
                    this.resultProcessor.addBrowserStackArtifacts(analysisResults.browserstack);
                } catch (bsProcessingError) {
                    console.error('Error adding BrowserStack artifacts:', bsProcessingError);
                }
            }

            return this.resultProcessor.generateReport();
        } catch (error) {
            console.error('Error in analysis orchestration:', error);
            return { 
                error: 'Analysis failed',
                details: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Analyze functional aspects of the test.
     * @param {Object} testInfo - Playwright test info object.
     * @returns {Promise<Object>} - Functional analysis results.
     */
    async analyzeFunctionalResults(testInfo) {
        // Basic functional analysis - extend as needed
        return {
            status: testInfo.status,
            duration: testInfo.duration,
            errors: testInfo.errors,
            retries: testInfo.retry
        };
    }

    /**
     * Analyze visual elements using screenshots.
     * @param {string[]} screenshots - Array of screenshot paths.
     * @returns {Promise<Object[]>} - Array of visual analysis results.
     */
    async analyzeVisualElements(screenshots) {
        const processedImages = await this.imageProcessor.processMultipleImages(screenshots);
        return this.visualAnalyzer.analyze(processedImages);
    }

    /**
     * Analyze visual elements using Cloudinary URLs.
     * @param {string[]} screenshotUrls - Array of Cloudinary screenshot URLs.
     * @returns {Promise<Object[]>} - Array of visual analysis results.
     */
    async analyzeVisualElementsFromUrls(screenshotUrls) {
        const { prompts } = require('../config/prompts');
        const results = [];

        for (const url of screenshotUrls) {
            try {
                const analysis = await this.geminiService.analyzeImageFromUrl(url, prompts.visualAnalysis);
                results.push({
                    screenshot: url,
                    analysis
                });
            } catch (error) {
                console.error(`Error analyzing screenshot URL ${url}:`, error);
                results.push({
                    screenshot: url,
                    error: error.message
                });
            }
        }

        return results;
    }

    /**
     * Analyze performance data.
     * @param {Object[]} performanceData - Array of performance data objects.
     * @returns {Promise<Object[]>} - Array of performance analysis results.
     */
    async analyzePerformanceData(performanceData) {
        return this.performanceAnalyzer.analyze(performanceData);
    }

    /**
     * Analyze errors from the test.
     * @param {Object} testInfo - Playwright test info object.
     *  @param {Object} artifacts - Test artifacts, including error logs if available.
     * @returns {Promise<Object[]>} - Array of error analysis results.
     */
    async analyzeErrors(testInfo, artifacts) {
        const errorData = {
            errors: testInfo.errors,
            logs: artifacts.logs,
            screenshots: artifacts.screenshots,
        };
        return this.errorAnalyzer.analyze(errorData);
    }

    async analyzeTestResults(data) {
        const {
            testInfo,
            screenshots,
            browserLogs,
            testResult
        } = data;

        const analysisResults = {
            testId: testInfo.testId,
            timestamp: new Date().toISOString(),
            testName: testInfo.title,
            status: testResult,
            analyses: {}
        };

        try {
            // Process screenshots if available
            if (screenshots && screenshots.length > 0) {
                // Check if screenshots are Cloudinary URLs
                const hasCloudinaryUrls = screenshots.some(screenshot => 
                    typeof screenshot === 'string' && screenshot.includes('cloudinary.com')
                );

                if (hasCloudinaryUrls) {
                    // Use direct URL analysis
                    analysisResults.analyses.visual = await this.analyzeVisualElementsFromUrls(screenshots);
                } else {
                    // Use traditional approach with local files
                    const processedImages = await this.imageProcessor.processImages(screenshots);
                    analysisResults.analyses.visual = await this.visualAnalyzer.analyze(processedImages);
                }
            }

            // Analyze content if applicable
            if (testInfo.annotations.includes('content')) {
                analysisResults.analyses.content = await this.contentAnalyzer.analyze(data);
            }

            // Analyze errors if test failed
            if (testResult === 'failed') {
                analysisResults.analyses.error = await this.errorAnalyzer.analyze({
                    error: testInfo.error,
                    browserLogs,
                    screenshots
                });
            }

            // Process and format results
            return await this.resultProcessor.processResults(analysisResults);
        } catch (error) {
            console.error('Analysis orchestration failed:', error);
            return {
                ...analysisResults,
                error: error.message,
                status: 'analysis_failed'
            };
        }
    }

    async analyzeResponsiveDesign(data) {
        const { screenshots, viewports, testName } = data;
        try {
            // Check if screenshots are Cloudinary URLs
            const hasCloudinaryUrls = screenshots.some(screenshot => 
                typeof screenshot === 'string' && screenshot.includes('cloudinary.com')
            );

            if (hasCloudinaryUrls) {
                return await this.analyzeResponsiveDesignFromUrls(screenshots, viewports);
            } else {
                const processedImages = await this.imageProcessor.processImages(screenshots);
                return await this.visualAnalyzer.analyzeResponsive(processedImages, viewports);
            }
        } catch (error) {
            console.error('Responsive design analysis failed:', error);
            throw error;
        }
    }

    /**
     * Analyze responsive design from Cloudinary URLs.
     * @param {string[]} screenshotUrls - Array of screenshot URLs.
     * @param {Object[]} viewports - Array of viewport objects.
     * @returns {Promise<Object>} - Analysis results.
     */
    async analyzeResponsiveDesignFromUrls(screenshotUrls, viewports) {
        const { prompts } = require('../config/prompts');
        
        try {
            // Prepare data for analysis: pair screenshots with viewports
            const screenshots = screenshotUrls.map((url, index) => ({
                url,
                viewport: viewports[index] || 'unknown'
            }));

            // Create a custom prompt with viewport details
            const customPrompt = `
                ${prompts.responsiveAnalysis}
                
                Analyze these screenshots taken at different viewports:
                ${screenshots.map(s => `- ${s.viewport}: ${s.url}`).join('\n')}
                
                Provide a comprehensive analysis of responsive design implementation.
            `;

            // Analyze each screenshot individually
            const individualAnalyses = await Promise.all(
                screenshots.map(async (screenshot) => {
                    const analysis = await this.geminiService.analyzeImageFromUrl(
                        screenshot.url,
                        `Analyze this screenshot for viewport ${screenshot.viewport}. ${prompts.responsiveAnalysis}`
                    );
                    return {
                        viewport: screenshot.viewport,
                        url: screenshot.url,
                        analysis
                    };
                })
            );

            // Generate a summary analysis
            const summaryAnalysis = await this.geminiService.analyzeContent(
                { screenshots, individualAnalyses },
                prompts.responsiveAnalysis
            );

            return {
                screenshots,
                individualAnalyses,
                summaryAnalysis
            };
        } catch (error) {
            console.error('Error analyzing responsive design from URLs:', error);
            throw error;
        }
    }

    /**
     * Analyzes a single test, including screenshots and logs.
     * @param {Array<string>} screenshots - Array of screenshot URLs.
     * @param {Array<string>} logs - Array of log URLs.
     * @param {object} testResult - Test result object (status, error message).
     * @returns {Promise<object>} Analysis results.
     */
    async analyzeTest(screenshots, logs, testResult) {
        // Check if screenshots are Cloudinary URLs
        const hasCloudinaryUrls = screenshots.some(screenshot => 
            typeof screenshot === 'string' && screenshot.includes('cloudinary.com')
        );

        let screenshotAnalysis;
        if (hasCloudinaryUrls && screenshots.length > 0) {
            screenshotAnalysis = await this.geminiService.analyzeImageFromUrl(screenshots[0], 'Analyze this screenshot for visual issues and UI elements');
        } else if (screenshots.length > 0) {
            screenshotAnalysis = await this.geminiService.analyzeImage(screenshots[0]);
        } else {
            screenshotAnalysis = "No screenshots available for analysis";
        }

        const logAnalysis = logs && logs.length > 0 
            ? await this.geminiService.analyzeText(logs[0]) 
            : "No logs available for analysis";

        return {
            screenshotAnalysis,
            logAnalysis,
            overall: 'Analysis based on available screenshots and logs'
        };
    }

    /**
     * Performs a visual comparison between baseline and Shopify screenshots.
     * @param {Array<string>} baselineScreenshots - Array of baseline screenshot URLs.
     * @param {Array<string>} shopifyScreenshots - Array of Shopify screenshot URLs.
     * @param {Array<string>} logs - Array of log URLs.
     * @param {object} testInfo - Test info object.
     * @returns {Promise<object>} Analysis results.
     */
    async analyzeShopifyComparison(baselineScreenshots, shopifyScreenshots, logs, testInfo) {
        try {
            const analysisResults = {};
            const { prompts } = require('../config/prompts');

            // Check if screenshots are Cloudinary URLs
            const hasCloudinaryUrls = 
                (baselineScreenshots && baselineScreenshots.some(url => typeof url === 'string' && url.includes('cloudinary.com'))) ||
                (shopifyScreenshots && shopifyScreenshots.some(url => typeof url === 'string' && url.includes('cloudinary.com')));

            if (hasCloudinaryUrls) {
                // Process screenshots directly from Cloudinary URLs
                analysisResults.visualComparison = await this.compareScreenshotsFromUrls(
                    baselineScreenshots,
                    shopifyScreenshots,
                    prompts.shopifyComparison
                );
            } else {
                // Process screenshots from local files
                const processedBaselineImages = await this.imageProcessor.processMultipleImages(baselineScreenshots);
                const processedShopifyImages = await this.imageProcessor.processMultipleImages(shopifyScreenshots);

                analysisResults.visualComparison = await this.visualAnalyzer.analyze(
                    processedBaselineImages,
                    processedShopifyImages,
                    prompts.shopifyComparison
                );
            }

            // Analyze logs if available
            if (logs && logs.length > 0) {
                analysisResults.logAnalysis = await this.geminiService.analyzeText(
                    logs.join('\n'), 
                    'Analyze these logs for any issues related to the Shopify implementation.'
                );
            }

            // Save results and return
            const reportPath = await this.geminiService.saveAnalysisResult(
                analysisResults,
                { title: testInfo.title || 'shopify-comparison' }
            );

            return {
                ...analysisResults,
                reportPath
            };
        } catch (error) {
            console.error('Shopify comparison analysis failed:', error);
            return {
                error: error.message,
                status: 'analysis_failed'
            };
        }
    }

    /**
     * Compare screenshots from Cloudinary URLs
     * @param {string[]} baselineUrls Array of baseline screenshot URLs
     * @param {string[]} shopifyUrls Array of Shopify screenshot URLs
     * @param {string} prompt Custom prompt for the comparison
     * @returns {Promise<Object[]>} Array of comparison results
     */
    async compareScreenshotsFromUrls(baselineUrls, shopifyUrls, prompt) {
        if (!baselineUrls || !shopifyUrls || baselineUrls.length === 0 || shopifyUrls.length === 0) {
            throw new Error('Both baseline and Shopify screenshots are required for comparison');
        }

        const results = [];

        // Pair screenshots with matching indices for comparison
        const maxLength = Math.min(baselineUrls.length, shopifyUrls.length);

        for (let i = 0; i < maxLength; i++) {
            try {
                const analysis = await this.geminiService.compareImagesFromUrls(
                    baselineUrls[i],
                    shopifyUrls[i],
                    prompt
                );

                results.push({
                    baseline: baselineUrls[i],
                    shopify: shopifyUrls[i],
                    analysis
                });
            } catch (error) {
                console.error(`Error comparing screenshots at index ${i}:`, error);
                results.push({
                    baseline: baselineUrls[i],
                    shopify: shopifyUrls[i],
                    error: error.message
                });
            }
        }

        // Generate a summary comparison if multiple screenshots
        if (results.length > 1) {
            try {
                const summaryAnalysis = await this.geminiService.analyzeContent(
                    { results },
                    'Provide a summary of the comparisons between baseline and Shopify screenshots.'
                );
                
                results.push({
                    type: 'summary',
                    analysis: summaryAnalysis
                });
            } catch (error) {
                console.error('Error generating summary comparison:', error);
            }
        }

        return results;
    }
}

module.exports = { AnalysisOrchestrator }; 