/**
 * @fileoverview Critical test flow: Force complete a sales funnel order using backend API
 */
const { test, expect } = require('../fixtures/critical-flow-fixture');
const { AdminLoginPage } = require('../../src/pages/admin/AdminLoginPage.js');
const { AdminSalesFunnelPage } = require('../../src/pages/admin/AdminSalesFunnelPage.js');
const SalesFunnelApi = require('../../src/api/endpoints/SalesFunnelApi.js');

test.describe('Critical Flow: Sales Funnel with Backend API', () => {
    test('Force complete a sales funnel order using backend API', async ({
        page, context, pageObjects, dbUtils, emailUtils, testData
    }) => {
        // Create API client
        const salesFunnelApi = new SalesFunnelApi();

        // Customize customer email for this test
        const customerEmail = testData.customer.email.replace('test', '0917-close-success');

        // 1. Admin login
        await test.step('Login to admin panel', async () => {
            const adminLoginPage = new AdminLoginPage(page);
            await adminLoginPage.login(process.env.ADMIN_USER, process.env.ADMIN_PASSWORD);
        });

        // 2. Navigate to sales funnel items
        await test.step('Navigate to sales funnel items', async () => {
            const adminSalesFunnelPage = new AdminSalesFunnelPage(page);
            await adminSalesFunnelPage.navigate();

            // Get sales funnel link
            const funnelLink = await adminSalesFunnelPage.getFunnelLink('demo-dsv-1');
            expect(funnelLink).toBeTruthy();

            // Store for checkout
            testData.funnelLink = funnelLink;
        });

        // 3. Open sales funnel in new incognito window
        await test.step('Open sales funnel in new context', async () => {
            // Create new incognito context
            const checkoutContext = await context.browser().newContext();
            const checkoutPage = await checkoutContext.newPage();

            // Navigate to funnel
            await checkoutPage.goto(testData.funnelLink);

            // Create checkout page object with the new page
            const { checkoutPage: checkoutPageObj } = pageObjects;
            checkoutPageObj.page = checkoutPage;

            // Store page for later steps
            testData.checkoutPage = checkoutPage;
            testData.checkoutPageObj = checkoutPageObj;
        });

        // 4. Fill checkout form and attempt purchase
        await test.step('Fill checkout form', async () => {
            // Verify initial product
            const productDetails = await testData.checkoutPageObj.getSalesFunnelProductDetails();
            expect(productDetails.name).toContain('Dark Spot Vanish');

            // Fill customer information
            await testData.checkoutPageObj.fillCustomerInfo({
                email: customerEmail
            });

            await testData.checkoutPageObj.fillAddress('billing', {
                firstName: testData.customer.firstName,
                lastName: testData.customer.lastName,
                phone: testData.customer.phone,
                address1: testData.customer.address1,
                address2: testData.customer.address2,
                city: testData.customer.city,
                postcode: testData.customer.postcode,
                country: testData.customer.country
            });

            await testData.checkoutPageObj.useSameAddressForBilling();
            await testData.checkoutPageObj.continueToShipping();

            // Select shipping method
            await testData.checkoutPageObj.selectShippingMethod('domestic_tracked');
            const shippingCost = await testData.checkoutPageObj.getShippingCost();
            expect(shippingCost).toBe(2.95);

            // Select payment method
            await testData.checkoutPageObj.selectPaymentMethod('credit_card');
            await testData.checkoutPageObj.fillCreditCardInfo(testData.creditCard);

            // Start purchase (will timeout/fail on upsell page)
            try {
                await testData.checkoutPageObj.completeOrder();
            } catch (error) {
                console.log('Expected timeout/failure on upsell page:', error.message);
            }
        });

        // 5. Use API to force complete the payment
        await test.step('Force complete payment using API', async () => {
            const completionResult = await salesFunnelApi.completeSalesFunnelPayments('DSS', '-1second');

            expect(completionResult.success).toBeTruthy();
            expect(completionResult.message).toContain('payment state marked as: paid');

            // Extract order number
            const orderNumberMatch = completionResult.message.match(/Order number #(\d+)/);
            testData.orderNumber = orderNumberMatch ? orderNumberMatch[1] : null;
            expect(testData.orderNumber).toBeTruthy();

            console.log(`Created order #${testData.orderNumber}`);
        });

        // 6. Verify order in database
        await test.step('Verify order in database', async () => {
            const order = await dbUtils.getOrderByNumber(testData.orderNumber);
            expect(order).toBeTruthy();
            expect(order.state).toBe('new');
            expect(order.payment_state).toBe('paid');
            expect(order.shipping_state).toBe('ready');

            // Verify order total
            expect(parseFloat(order.total)).toBeCloseTo(91.95, 1);
        });

        // 7. Verify confirmation email
        await test.step('Verify confirmation email', async () => {
            const email = await emailUtils.waitForEmail({
                recipient: customerEmail,
                subject: `Order #${testData.orderNumber} Is Confirmed`,
                search_after: new Date(Date.now() - 10 * 60 * 1000).toISOString()
            });

            expect(email).toBeTruthy();

            const emailContent = await emailUtils.getEmailContent(email.id);
            expect(emailContent).toContain('Dark Spot Vanish');
            expect(emailContent).toContain('£91.95');
        });

        // 8. Verify welcome email
        await test.step('Verify welcome email', async () => {
            const welcomeEmail = await emailUtils.waitForEmail({
                recipient: customerEmail,
                subject: 'Welcome To Dr. Sister Skincare!',
                search_after: new Date(Date.now() - 10 * 60 * 1000).toISOString()
            });

            expect(welcomeEmail).toBeTruthy();
        });
    });
});
