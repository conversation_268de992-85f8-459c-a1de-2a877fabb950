/**
 * @fileoverview Example test demonstrating Cloudinary and Gemini integration for visual testing
 */

const { test, expect } = require('@playwright/test');
const { AnalysisOrchestrator } = require('../../src/utils/gemini');
const { VisualAnalysisHelper } = require('../../src/utils/visual-analisys-helper');
const cloudinary = require('cloudinary').v2;
const path = require('path');
const fs = require('fs').promises;

// Initialize Cloudinary with credentials from environment variables
cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME || 'dovykzbn9',
    api_key: process.env.CLOUDINARY_API_KEY || '819558736635999',
    api_secret: process.env.CLOUDINARY_API_SECRET
});

test.describe('Cloudinary and Gemini integration for visual testing', () => {
    
    test('should analyze a single page using Gemini AI', async ({ page }) => {
        // Navigate to the test URL
        await page.goto(process.env.BASE_URL || 'https://promo.drsisterskincare.com');
        
        // Wait for page to stabilize
        await page.waitForTimeout(2000);
        
        // Capture and upload screenshot to Cloudinary
        const metadata = await VisualAnalysisHelper.captureAndUploadScreenshot(
            page, 
            'gemini-analysis-test', 
            'homepage'
        );
        
        console.log('Screenshot uploaded to:', metadata.cloudinaryUrl);
        
        // Analyze screenshot using Gemini
        const orchestrator = new AnalysisOrchestrator({
            includeVisualAnalysis: true
        });
        
        // Perform the analysis
        const analysisResults = await orchestrator.analyze(
            { 
                title: 'Homepage Analysis', 
                status: 'passed',
                errors: [] // Explicitly provide empty errors array
            },
            { screenshots: [metadata.cloudinaryUrl] }
        );
        
        // Save analysis results
        const resultsPath = path.join(process.cwd(), 'test-results', 'gemini-analysis', 'analysis-results.json');
        await fs.mkdir(path.dirname(resultsPath), { recursive: true });
        await fs.writeFile(resultsPath, JSON.stringify(analysisResults, null, 2));
        
        console.log('Analysis results saved to:', resultsPath);
        
        // Verify analysis was generated
        expect(analysisResults).toBeTruthy();
        // Handle potential errors more gracefully
        if (analysisResults.error) {
            console.warn(`Analysis warning: ${analysisResults.error}`, analysisResults.details || '');
        }
    });
    
    test('should compare baseline with Shopify website using Gemini AI', async ({ browser }) => {
        // Create two browser contexts for the two different sites
        const baselineContext = await browser.newContext();
        const shopifyContext = await browser.newContext();
        
        // Create pages for both contexts
        const baselinePage = await baselineContext.newPage();
        const shopifyPage = await shopifyContext.newPage();
        
        // Navigate to the baseline and Shopify URLs
        await baselinePage.goto(process.env.BASE_URL || 'https://promo.drsisterskincare.com');
        await shopifyPage.goto(process.env.SHOPIFY_URL || 'https://drsisterskincare.com');
        
        // Wait for pages to stabilize
        await baselinePage.waitForTimeout(2000);
        await shopifyPage.waitForTimeout(2000);
        
        // Capture and upload screenshots to Cloudinary
        const baselineMetadata = await VisualAnalysisHelper.captureAndUploadScreenshot(
            baselinePage, 
            'shopify-comparison-test', 
            'baseline-homepage'
        );
        
        const shopifyMetadata = await VisualAnalysisHelper.captureAndUploadScreenshot(
            shopifyPage, 
            'shopify-comparison-test', 
            'shopify-homepage'
        );
        
        console.log('Baseline screenshot uploaded to:', baselineMetadata.cloudinaryUrl);
        console.log('Shopify screenshot uploaded to:', shopifyMetadata.cloudinaryUrl);
        
        // Initialize the analysis orchestrator
        const orchestrator = new AnalysisOrchestrator();
        
        // Perform the comparison analysis
        const comparisonResults = await orchestrator.analyzeShopifyComparison(
            [baselineMetadata.cloudinaryUrl],
            [shopifyMetadata.cloudinaryUrl],
            [], // logs
            { title: 'Shopify Comparison Test' }
        );
        
        // Save comparison results
        const resultsPath = path.join(process.cwd(), 'test-results', 'gemini-analysis', 'shopify-comparison-results.json');
        await fs.mkdir(path.dirname(resultsPath), { recursive: true });
        await fs.writeFile(resultsPath, JSON.stringify(comparisonResults, null, 2));
        
        console.log('Comparison results saved to:', resultsPath);
        
        // Verify comparison was generated
        expect(comparisonResults).toBeTruthy();
        expect(comparisonResults.error).toBeUndefined();
        expect(comparisonResults.visualComparison).toBeTruthy();
        
        // Close contexts
        await baselineContext.close();
        await shopifyContext.close();
    });
    
    test('should analyze responsive design using multiple viewports', async ({ browser }) => {
        // Define viewports to test
        const viewports = [
            { width: 375, height: 667, name: 'mobile' },
            { width: 768, height: 1024, name: 'tablet' },
            { width: 1440, height: 900, name: 'desktop' }
        ];
        
        const screenshotUrls = [];
        
        // Capture screenshots for each viewport
        for (const viewport of viewports) {
            // Create a new context with the viewport
            const context = await browser.newContext({
                viewport: { width: viewport.width, height: viewport.height }
            });
            
            const page = await context.newPage();
            
            // Navigate to the test URL
            await page.goto(process.env.BASE_URL || 'https://promo.drsisterskincare.com');
            
            // Wait for page to stabilize
            await page.waitForTimeout(2000);
            
            // Capture and upload screenshot to Cloudinary
            const metadata = await VisualAnalysisHelper.captureAndUploadScreenshot(
                page, 
                'responsive-analysis-test', 
                `viewport-${viewport.name}`
            );
            
            console.log(`${viewport.name} screenshot uploaded to:`, metadata.cloudinaryUrl);
            
            screenshotUrls.push(metadata.cloudinaryUrl);
            
            // Close the context
            await context.close();
        }
        
        // Initialize the analysis orchestrator
        const orchestrator = new AnalysisOrchestrator();
        
        // Perform the responsive design analysis
        const responsiveAnalysis = await orchestrator.analyzeResponsiveDesign({
            screenshots: screenshotUrls,
            viewports: viewports.map(v => v.name),
            testName: 'Responsive Design Analysis'
        });
        
        // Save analysis results
        const resultsPath = path.join(process.cwd(), 'test-results', 'gemini-analysis', 'responsive-analysis-results.json');
        await fs.mkdir(path.dirname(resultsPath), { recursive: true });
        await fs.writeFile(resultsPath, JSON.stringify(responsiveAnalysis, null, 2));
        
        console.log('Responsive analysis results saved to:', resultsPath);
        
        // Verify analysis was generated
        expect(responsiveAnalysis).toBeTruthy();
        expect(responsiveAnalysis.summaryAnalysis).toBeTruthy();
    });
}); 