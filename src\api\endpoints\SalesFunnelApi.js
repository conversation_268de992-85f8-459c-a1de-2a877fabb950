const ApiClient = require('../ApiClient');

class SalesFunnelApi {
    constructor() {
        this.client = new ApiClient({ useAuth: true });
    }

    // Based on actual app commands:
    // app:sales-funnel:complete-payments DSS --last-updated-before=-1second -e prod -v
    async completeSalesFunnelPayments(brand, lastUpdatedBefore = '-1second') {
        return this.client.post('/api/v2/admin/sales-funnel/complete-payments', {
            brand,
            lastUpdatedBefore
        });
    }

    // Based on actual app command:
    // app:abandon-resta:call-center DSS --last-updated-before=-1minute -e prod -v
    async processAbandonedCarts(brand, lastUpdatedBefore = '-1minute') {
        return this.client.post('/api/v2/admin/abandoned-cart/process', {
            brand,
            lastUpdatedBefore
        });
    }

    async getFunnelItems() {
        return this.client.get('/api/v2/admin/sales-funnel-items');
    }

    async getFunnelItemByCode(code) {
        const items = await this.getFunnelItems();
        return items.find(item => item.code === code);
    }
}

module.exports = SalesFunnelApi;