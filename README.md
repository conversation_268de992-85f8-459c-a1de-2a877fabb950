# BrowserStack Playwright Integration

This project demonstrates how to run Playwright tests on BrowserStack real devices across multiple platforms (Android, iOS, Windows, macOS).

## Project Structure

```
browserstack-playwright/
├── browserstack.yml         # BrowserStack configuration
├── package.json             # Project dependencies
├── playwright.config.js     # Playwright configuration
├── src/
│   └── utils/               # Utility functions
│       ├── browserstack/    # BrowserStack integration utilities
│       │   └── direct-cdp-connection.js  # Direct CDP connection helper
│       ├── email/           # Email verification utilities
│       │   └── mailtrap-helper.js        # Mailtrap integration for email testing
│       ├── gemini/          # Google Gemini integration
│       └── visual-analisys-helper.js  # Screenshot and visual analysis helper
└── tests/
    └── examples/            # Example tests
        └── direct-cdp-demo.spec.js  # Demo test using direct CDP
```

## Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Set up environment variables:
   ```bash
   export BROWSERSTACK_USERNAME="iakovvolfkovich_F75ojQ"
   export BROWSERSTACK_ACCESS_KEY="********************"
   export CLOUDINARY_API_SECRET="your_cloudinary_secret"
   export MAILTRAP_TOKEN="your_mailtrap_token"
   export MAILTRAP_AEONS_INBOX_ID="your_inbox_id"
   export MAILTRAP_ACCOUNT_ID="your_account_id"
   ```

## Running Tests

### Run tests on a specific platform

```bash
# Run on Windows Chrome (default)
PLATFORM=windows npx playwright test tests/examples/direct-cdp-demo.spec.js

# Run on Android
PLATFORM=android npx playwright test tests/examples/direct-cdp-demo.spec.js

# Run on iOS
PLATFORM=ios npx playwright test tests/examples/direct-cdp-demo.spec.js
```

## Platform Handling

This project provides two approaches for running tests on different platforms:

1. **BrowserStack SDK** (recommended for full platform support)
2. **Direct CDP Connection** (with fallbacks for mobile devices)

### Platform Limitations

- **iOS**: Direct CDP connections are not supported by BrowserStack. Tests will automatically fall back to Windows Chrome while preserving iOS metadata.
- **Android**: Direct CDP connections may have issues with BrowserStack. Tests will handle connection failures gracefully.

## Screenshot Management

The `VisualAnalysisHelper` class provides utilities for:

1. Capturing screenshots during tests
2. Uploading screenshots to Cloudinary
3. Generating metadata with platform information
4. Creating consistent filenames based on the requested platform

Screenshots are saved to:
```
test-results/{testName}/{timestamp}/{screenshotName}_{platform}_{deviceName}_{width}x{height}.png
```

## Email Verification

The project includes integration with Mailtrap for email verification:

1. **Email Confirmation Testing**: Verify that confirmation emails are sent after purchase
2. **Content Validation**: Ensure emails contain correct order details, shipping info, etc.
3. **Link Extraction**: Extract and validate links within emails for further testing

To enable email verification:

1. Set up Mailtrap environment variables in `.env` file
2. Update test data with expected email content patterns
3. Use the `mailtrapHelper` fixture in your tests

See the [Email Verification Integration](docs/email-verification-integration.md) document for detailed implementation.

## Google Gemini Integration

The project includes integration with Google Gemini for AI-powered visual analysis of screenshots.

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| BROWSERSTACK_USERNAME | BrowserStack username | |
| BROWSERSTACK_ACCESS_KEY | BrowserStack access key | |
| PLATFORM | Platform to test on (windows, android, ios) | desktop |
| FORCE_MOBILE_CDP | Force direct CDP for mobile platforms | false |
| CLOUDINARY_API_SECRET | Cloudinary API secret | |
| MAILTRAP_TOKEN | Mailtrap API token | |
| MAILTRAP_AEONS_INBOX_ID | Mailtrap inbox ID for Aeons | |
| MAILTRAP_ACCOUNT_ID | Mailtrap account ID | |

## Known Issues and Workarounds

1. **iOS Direct CDP**: Not supported by BrowserStack. The code will automatically fall back to Windows Chrome while maintaining iOS identifiers for screenshots and metadata.

2. **Android Direct CDP**: May encounter "Malformed endpoint" errors. The code includes error handling to handle this gracefully.

3. **Screenshots on Mobile Devices**: When using fallbacks, screenshots will be taken using Chrome on Windows but will maintain the correct platform identifier for consistency.

## Advanced Configuration

For advanced use cases, you can:

1. Modify `browserstack.yml` to add more platforms or customize capabilities
2. Update the CDP connection logic in `direct-cdp-connection.js`
3. Enhance screenshot capturing in `visual-analisys-helper.js`
4. Customize email verification in `mailtrap-helper.js`

## Best Practices

1. Use the BrowserStack SDK for the most reliable cross-platform testing
2. For iOS testing, always use the BrowserStack SDK instead of direct CDP
3. For visual regression testing, use the `VisualAnalysisHelper` class to ensure consistent screenshot naming
4. For email testing, verify both the receipt of the email and its content
