/**
 * @fileoverview Visual analysis utilities for Gemini
 */

const { prompts } = require('../config/prompts');

class VisualAnalyzer {
    /**
     * Constructor for VisualAnalyzer
     * @param {Object} geminiService Gemini service instance
     */
    constructor(geminiService) {
        this.geminiService = geminiService;
    }

    /**
     * Analyze visual elements in images
     * @param {Array<string>} images Array of base64 encoded images
     * @param {Object} options Analysis options
     * @returns {Promise<Array>} Analysis results
     */
    async analyze(images, options = {}) {
        const results = [];
        const prompt = options.comparisonType === 'baselineVsShopify' 
            ? prompts.shopifyComparison 
            : prompts.visualAnalysis;

        for (const image of images) {
            try {
                const analysis = await this.geminiService.analyzeImage(image, prompt);
                results.push({
                    image,
                    analysis,
                    timestamp: new Date().toISOString()
                });
            } catch (error) {
                console.error('Error analyzing image:', error);
                results.push({
                    image,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        }

        return results;
    }

    /**
     * Compare two sets of images
     * @param {Array<string>} baselineImages Array of base64 encoded baseline images
     * @param {Array<string>} comparisonImages Array of base64 encoded comparison images
     * @returns {Promise<Array>} Comparison results
     */
    async compareImages(baselineImages, comparisonImages) {
        const results = [];

        for (let i = 0; i < baselineImages.length; i++) {
            try {
                const baseline = baselineImages[i];
                const comparison = comparisonImages[i];

                if (!baseline || !comparison) {
                    throw new Error('Mismatched number of baseline and comparison images');
                }

                const analysis = await this.geminiService.compareImages(baseline, comparison);
                results.push({
                    baseline,
                    comparison,
                    analysis,
                    timestamp: new Date().toISOString()
                });
            } catch (error) {
                console.error('Error comparing images:', error);
                results.push({
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        }

        return results;
    }
}

module.exports = { VisualAnalyzer }; 