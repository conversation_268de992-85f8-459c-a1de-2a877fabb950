/**
 * @fileoverview Enhanced Test Data Manager for YAML-based test data
 */
const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const urlManager = require('../../src/utils/url-manager');

class TestDataManager {
    constructor() {
        this.productsData = null;
        this.testData = null;
        this.contentMapping = null;
        this.brand = null;
        this.environment = null;
        this.dataSet = null;
        this.orderData = null;
        this.brandConfigs = null;
    }

    /**
     * Initialize test data from YAML files
     * @param {string} dataSet - Data set name (e.g., 'default', 'staging')
     * @param {string} brand - Brand name (e.g., 'aeons', 'dss')
     * @param {string} environment - Environment (e.g., 'dev', 'stage', 'prod')
     */
    initialize(dataSet = 'default', brand = null, environment = null) {
        this.dataSet = dataSet || process.env.TEST_DATA_SET || 'default';
        this.brand = brand || process.env.BRAND || 'aeons';
        this.environment = environment || process.env.TEST_ENV || 'stage';
        
        console.log(`Initializing TestDataManager with dataset: ${this.dataSet}, brand: ${this.brand}, environment: ${this.environment}`);
        
        // Initialize URL manager
        urlManager.initialize(this.brand, this.environment);
        
        try {
            // Load products data
            const productsPath = path.join(
                process.cwd(), 
                'tests', 
                'data', 
                'brands', 
                this.brand, 
                'products.yml'
            );
            
            if (fs.existsSync(productsPath)) {
                const productsContent = fs.readFileSync(productsPath, 'utf8');
                this.productsData = yaml.load(productsContent);
                console.log(`Loaded products data for ${this.brand}`);
            } else {
                console.warn(`Products file not found: ${productsPath}`);
            }
            
            // Load test data
            const testDataPath = path.join(
                process.cwd(), 
                'tests', 
                'data', 
                'brands', 
                this.brand, 
                'test_data.yml'
            );
            
            if (fs.existsSync(testDataPath)) {
                const testDataContent = fs.readFileSync(testDataPath, 'utf8');
                this.testData = yaml.load(testDataContent);
                console.log(`Loaded test data for ${this.brand}`);
            } else {
                console.warn(`Test data file not found: ${testDataPath}`);
            }
            
            // Load dataset-specific overrides if available
            this.loadDataSetOverrides();
            
        } catch (error) {
            console.error(`Error initializing test data: ${error.message}`);
            throw new Error(`Failed to initialize test data: ${error.message}`);
        }
    }
    
    /**
     * Load dataset-specific overrides
     */
    loadDataSetOverrides() {
        if (this.dataSet === 'default') return;
        
        try {
            const overridesPath = path.join(
                process.cwd(), 
                'tests', 
                'data', 
                'brands', 
                this.brand, 
                'datasets', 
                `${this.dataSet}.yml`
            );
            
            if (fs.existsSync(overridesPath)) {
                const overridesContent = fs.readFileSync(overridesPath, 'utf8');
                const overrides = yaml.load(overridesContent);
                
                // Merge overrides into main data
                this.mergeOverrides(overrides);
                console.log(`Loaded dataset overrides for ${this.dataSet}`);
            } else {
                console.log(`No dataset overrides found for ${this.dataSet}`);
            }
        } catch (error) {
            console.warn(`Error loading dataset overrides: ${error.message}`);
        }
    }
    
    /**
     * Merge overrides into main data
     * @param {Object} overrides - Overrides to merge
     */
    mergeOverrides(overrides) {
        if (!overrides) return;
        
        // Merge product overrides
        if (overrides.products && this.productsData) {
            Object.keys(overrides.products).forEach(productKey => {
                if (this.productsData[productKey]) {
                    this.productsData[productKey] = this.deepMerge(
                        this.productsData[productKey],
                        overrides.products[productKey]
                    );
                }
            });
        }
        
        // Merge test data overrides
        if (overrides.test_data && this.testData) {
            Object.keys(overrides.test_data).forEach(section => {
                if (this.testData[section]) {
                    this.testData[section] = this.deepMerge(
                        this.testData[section],
                        overrides.test_data[section]
                    );
                }
            });
        }
    }
    
    /**
     * Deep merge two objects
     * @param {Object} target - Target object
     * @param {Object} source - Source object
     * @returns {Object} Merged object
     */
    deepMerge(target, source) {
        const output = Object.assign({}, target);
        
        if (this.isObject(target) && this.isObject(source)) {
            Object.keys(source).forEach(key => {
                if (this.isObject(source[key])) {
                    if (!(key in target)) {
                        Object.assign(output, { [key]: source[key] });
                    } else {
                        output[key] = this.deepMerge(target[key], source[key]);
                    }
                } else {
                    Object.assign(output, { [key]: source[key] });
                }
            });
        }
        
        return output;
    }
    
    /**
     * Check if value is an object
     * @param {*} item - Value to check
     * @returns {boolean} True if object
     */
    isObject(item) {
        return (item && typeof item === 'object' && !Array.isArray(item));
    }

    /**
     * Get base URL for current brand and environment
     * @returns {string} Base URL
     */
    getBaseUrl() {
        return urlManager.getBaseUrl(this.brand, this.environment);
    }

    /**
     * Get product URL for a specific product
     * @param {string} productSlug - Product slug
     * @returns {string} Full product URL
     */
    getProductUrl(productSlug) {
        return urlManager.getProductUrl(productSlug, this.brand, this.environment);
    }

    /**
     * Get product data by slug
     * @param {string} productSlug - Product key or slug in YAML
     * @returns {Object} Product data
     */
    getProduct(productSlug) {
        if (!this.productsData) {
            throw new Error('Test data not initialized. Call initialize() first.');
        }

        console.log('Looking for product with key:', productSlug);
        
        // Try different variations of the product key
        const possibleKeys = [
            productSlug,                       // Original
            productSlug.replace(/-/g, '_'),    // kebab-case to snake_case
            productSlug.replace(/_/g, '-'),    // snake_case to kebab-case
            productSlug.replace(/^aeons[-_]/, ''), // Remove aeons- prefix
            productSlug.replace(/^aeons[-_]/, '').replace(/-/g, '_'), // Remove prefix and convert to snake_case
        ];
        
        let product = null;
        
        for (const key of possibleKeys) {
            if (this.productsData[key]) {
                console.log(`Found product with key: ${key}`);
                product = this.productsData[key];
                break;
            }
        }
        
        // If product wasn't found, check if it's a slug in any product
        if (!product) {
            for (const [key, data] of Object.entries(this.productsData)) {
                if (data.slug === productSlug) {
                    console.log(`Found product with slug: ${productSlug}, key: ${key}`);
                    product = data;
                    break;
                }
            }
        }
        
        // If we still haven't found the product, throw an error with helpful information
        if (!product) {
            console.error('Available products:', Object.keys(this.productsData));
            throw new Error(`Product with key ${productSlug} not found in test data. Available keys: ${Object.keys(this.productsData).join(', ')}`);
        }

        // Add full URL to product data
        product = { ...product };
        product.fullUrl = this.getProductUrl(product.slug || productSlug);
        
        return product;
    }

    /**
     * Get price for specific flavor, purchase type and quantity
     * @param {string} productSlug - Product slug/handle
     * @param {string} flavor - Flavor code (e.g., 'classic', 'lemon')
     * @param {string} purchaseType - 'oneTime' or 'subscription'
     * @param {string} quantity - 'minimum', 'medium', or 'maximum'
     * @returns {number} Price value
     */
    getPrice(productSlug, flavor, purchaseType, quantity) {
        const product = this.getProduct(productSlug);
        if (!product.flavors?.[flavor]?.prices?.[purchaseType]?.[quantity]) {
            throw new Error(`Price not found for product "${productSlug}", flavor "${flavor}", type "${purchaseType}", quantity "${quantity}"`);
        }
        return product.flavors[flavor].prices[purchaseType][quantity].price;
    }

    /**
     * Get user data
     * @param {string} userType - Type of user (e.g., 'default', 'international')
     * @returns {Object} User data
     */
    getUser(userType = 'default') {
        if (!this.testData || !this.testData.test_users) {
            throw new Error('Test data not initialized or missing test_users section');
        }
        
        const user = this.testData.test_users[userType];
        if (!user) {
            throw new Error(`User type ${userType} not found in test data`);
        }
        
        return user;
    }

    /**
     * Get payment method data
     * @param {string} method - Payment method (e.g., 'stripe_valid')
     * @returns {Object} Payment method data
     */
    getPaymentMethod(method) {
        if (!this.testData || !this.testData.payment_methods) {
            throw new Error('Test data not initialized or missing payment_methods section');
        }
        
        const paymentMethod = this.testData.payment_methods[method];
        if (!paymentMethod) {
            throw new Error(`Payment method ${method} not found in test data`);
        }
        
        return paymentMethod;
    }

    /**
     * Get shipping method data
     * @param {string} region - Shipping region (e.g., 'UK', 'US')
     * @returns {Object} Shipping method data
     */
    getShippingMethod(region) {
        if (!this.testData || !this.testData.shipping_methods) {
            throw new Error('Test data not initialized or missing shipping_methods section');
        }
        
        const shippingMethod = this.testData.shipping_methods[region];
        if (!shippingMethod) {
            throw new Error(`Shipping method for region ${region} not found in test data`);
        }
        
        return shippingMethod;
    }

    /**
     * Get funnel configuration
     * @param {string} funnelKey - Funnel configuration key
     * @returns {Object} Funnel configuration
     */
    getFunnelConfig(funnelKey) {
        if (!this.testData || !this.testData.funnel_configs) {
            throw new Error('Test data not initialized or missing funnel_configs section');
        }
        
        const funnelConfig = this.testData.funnel_configs[funnelKey];
        if (!funnelConfig) {
            throw new Error(`Funnel config ${funnelKey} not found in test data`);
        }
        
        return funnelConfig;
    }

    /**
     * Load content mapping data for comparison tests
     * @returns {Object} Content mapping data
     */
    loadContentMapping() {
        if (this.contentMapping) {
            return this.contentMapping;
        }

        try {
            const contentMappingPath = path.join(
                process.cwd(),
                'tests',
                'data',
                'brands',
                this.brand,
                'content-mapping.yml'
            );

            if (fs.existsSync(contentMappingPath)) {
                const contentMappingContent = fs.readFileSync(contentMappingPath, 'utf8');
                this.contentMapping = yaml.load(contentMappingContent);
                console.log(`Loaded content mapping data for ${this.brand}`);
                return this.contentMapping;
            } else {
                console.warn(`Content mapping file not found: ${contentMappingPath}`);
                return null;
            }
        } catch (error) {
            console.error(`Error loading content mapping: ${error.message}`);
            return null;
        }
    }

    /**
     * Get content mapping data with proper casing for JS constants
     * @returns {Object} Content mapping data with proper casing for constants
     */
    getContentMapping() {
        const mapping = this.loadContentMapping();
        if (!mapping) {
            return null;
        }

        // Transform YAML keys to match the original JS constants
        return {
            BASE_URLS: mapping.baseUrls,
            PRODUCT_URLS: mapping.productUrls,
            CONTENT_SELECTORS: mapping.contentSelectors,
            PRODUCT_CONTENT: mapping.productContent,
            TEXT_NORMALIZATION: mapping.textNormalization
        };
    }

    /**
     * Get all test data for current brand and environment
     * @returns {Object} Complete test data object
     */
    getTestData() {
        return {
            ...this.testData,
            product: this.productsData?.[Object.keys(this.productsData)[0]]
        };
    }

    /**
     * Get brand configuration for current brand and environment
     * @returns {Object} Brand configuration
     */
    getBrandConfig() {
        const configKey = `${this.brand}_${this.environment}`;
        return this.brandConfigs[configKey] || urlManager.getBaseUrl(this.brand, this.environment);
    }

    /**
     * Get email template data
     * @param {string} templateName - Template name (e.g., 'order_confirmation')
     * @returns {Object} Email template data
     */
    getEmailTemplate(templateName) {
        if (!this.testData || !this.testData.email_templates) {
            throw new Error('Test data not initialized or missing email_templates section');
        }
        
        const template = this.testData.email_templates[templateName];
        if (!template) {
            throw new Error(`Email template ${templateName} not found in test data`);
        }
        
        return template;
    }

    /**
     * Get order confirmation email subject for current brand
     * @returns {string} Email subject
     */
    getOrderConfirmationEmailSubject() {
        try {
            return this.getEmailTemplate('order_confirmation').subject;
        } catch (error) {
            // Default subject if not found in test data
            return `${this.brand.toUpperCase()} Order Confirmation`;
        }
    }

    /**
     * Get brand-specific sender email address
     * @returns {string} Sender email address
     */
    getBrandSenderEmail() {
        try {
            return this.getEmailTemplate('sender').email;
        } catch (error) {
            // Default sender email based on brand
            const brandDomain = {
                'aeons': 'aeons.co',
                'dss': 'drsisterskincare.com',
                'ypn': 'yourpetnutrition.com'
            }[this.brand] || 'example.com';
            
            return `orders@${brandDomain}`;
        }
    }

    /**
     * Store order data for use in future tests
     * @param {string} orderNumber - Order number/ID
     * @param {number} orderTotal - Total order amount
     * @param {string} customerEmail - Customer email address
     * @param {Object} additionalData - Additional order data to store
     * @returns {Object} The stored order data
     */
    setOrderData(orderNumber, orderTotal, customerEmail, additionalData = {}) {
        // Initialize order storage if it doesn't exist
        if (!this.orderData) {
            this.orderData = [];
        }
        
        // Create order data object
        const orderData = {
            orderNumber,
            orderTotal,
            customerEmail,
            timestamp: new Date().toISOString(),
            brand: this.brand,
            environment: this.environment,
            ...additionalData
        };
        
        // Add to orders collection
        this.orderData.push(orderData);
        
        console.log(`Order #${orderNumber} stored in TestDataManager`);
        return orderData;
    }
    
    /**
     * Get latest stored order data
     * @returns {Object|null} The most recently stored order data or null if none exists
     */
    getLatestOrderData() {
        if (!this.orderData || this.orderData.length === 0) {
            return null;
        }
        return this.orderData[this.orderData.length - 1];
    }
    
    /**
     * Get order data by order number
     * @param {string} orderNumber - Order number to find
     * @returns {Object|null} Order data or null if not found
     */
    getOrderDataByNumber(orderNumber) {
        if (!this.orderData || this.orderData.length === 0) {
            return null;
        }
        return this.orderData.find(order => order.orderNumber === orderNumber) || null;
    }
    
    /**
     * Get all stored order data
     * @returns {Array} Array of all stored order data
     */
    getAllOrderData() {
        return this.orderData || [];
    }
}

// Export singleton instance
module.exports = new TestDataManager();