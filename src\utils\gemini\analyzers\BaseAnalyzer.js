/**
 * @fileoverview Base analyzer class for Gemini AI analysis
 */

class BaseAnalyzer {
    constructor(geminiService) {
        if (new.target === BaseAnalyzer) {
            throw new TypeError('Cannot construct BaseAnalyzer instances directly');
        }
        this.geminiService = geminiService;
    }

    async analyze(data, context = {}) {
        throw new Error('analyze() must be implemented by derived classes');
    }

    async safeAnalyze(data, context = {}) {
        try {
            return await this.analyze(data, context);
        } catch (error) {
            console.error(`Analysis failed in ${this.constructor.name}:`, error);
            return {
                error: error.message,
                timestamp: new Date().toISOString(),
                analyzer: this.constructor.name
            };
        }
    }

    async validateInput(data) {
        if (!data) {
            throw new Error('No data provided for analysis');
        }
    }

    formatAnalysisResult(result, metadata = {}) {
        return {
            timestamp: new Date().toISOString(),
            analyzer: this.constructor.name,
            result,
            ...metadata
        };
    }

    async retryAnalysis(fn, retries = 3, delay = 1000) {
        let lastError;
        for (let i = 0; i < retries; i++) {
            try {
                return await fn();
            } catch (error) {
                lastError = error;
                if (i < retries - 1) {
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        throw lastError;
    }
}

module.exports = { BaseAnalyzer }; 