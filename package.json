{"name": "browserstack-playwright", "version": "1.0.0", "description": "BrowserStack integration with Playwright for cross-browser testing", "main": "index.js", "scripts": {"build:config": "tsc config/index.ts --outDir dist", "pretest": "npm run build:config", "// BASIC COMMANDS": "Basic test execution commands", "test": "playwright test", "test:local": "node run-test.js", "report": "playwright show-report", "report:bs": "browserstack-node-sdk playwright show-report", "// LOCAL BROWSER TESTING": "Execute tests on local browsers", "test:chrome": "node run-test.js --platform=windows-chrome", "test:safari": "node run-test.js --platform=mac-safari", "test:android": "node run-test.js --platform=samsung-galaxy-s23", "test:ios": "node run-test.js --platform=iphone-14", "// BROWSERSTACK TESTING": "Execute tests on BrowserStack", "test:bs": "browserstack-node-sdk node run-test.js", "test:chrome:bs": "browserstack-node-sdk node run-test.js --platform=windows-chrome", "test:safari:bs": "browserstack-node-sdk node run-test.js --platform=mac-safari", "test:android:bs": "browserstack-node-sdk node run-test.js --platform=samsung-galaxy-s23", "test:ios:bs": "browserstack-node-sdk node run-test.js --platform=iphone-14", "// TEST TYPES": "Run specific test types", "test:smoke": "node run-test.js --tag=@smoke", "test:smoke:bs": "browserstack-node-sdk node run-test.js --tag=@smoke", "test:regression": "node run-test.js --tag=@regression", "test:regression:bs": "browserstack-node-sdk node run-test.js --tag=@regression", "test:visual": "node run-test.js --tag=@visual", "test:visual:bs": "browserstack-node-sdk node run-test.js --tag=@visual", "// ENVIRONMENTS": "Run tests in specific environments", "test:dev": "node run-test.js --env=dev", "test:dev:bs": "browserstack-node-sdk node run-test.js --env=dev", "test:stage": "node run-test.js --env=stage", "test:stage:bs": "browserstack-node-sdk node run-test.js --env=stage", "test:prod": "node run-test.js --env=prod --dataset=production", "test:prod:bs": "browserstack-node-sdk node run-test.js --env=prod --dataset=production", "// CI COMMANDS": "CI/CD integration commands", "ci:test": "node ./ci/run-env-tests.js", "ci:test:bs": "node ./ci/run-env-tests.js --use-browserstack", "// CI DESKTOP BROWSERS": "CI/CD commands for desktop browsers", "ci:test:chrome": "node ./ci/run-env-tests.js stage windows-chrome --no-browserstack", "ci:test:chrome:bs": "node ./ci/run-env-tests.js dev windows-chrome --use-browserstack", "ci:test:safari": "node ./ci/run-env-tests.js dev mac-safari", "ci:test:safari:bs": "node ./ci/run-env-tests.js dev mac-safari --use-browserstack", "// CI MOBILE DEVICES": "CI/CD commands for mobile devices", "ci:test:android": "node ./ci/run-env-tests.js dev samsung-galaxy-s23", "ci:test:android:bs": "node ./ci/run-env-tests.js dev samsung-galaxy-s23 --use-browserstack", "ci:test:ios": "node ./ci/run-env-tests.js dev iphone-14", "ci:test:ios:bs": "node ./ci/run-env-tests.js dev iphone-14 --use-browserstack", "// CI STAGING": "CI/CD commands for staging environment", "ci:test:stage:chrome": "node ./ci/run-env-tests.js stage windows-chrome  --no-browserstack", "ci:test:dev:chrome": "node ./ci/run-env-tests.js dev windows-chrome --no-browserstack", "ci:test:stage:chrome:bs": "node ./ci/run-env-tests.js stage windows-chrome --use-browserstack", "ci:test:stage:safari": "node ./ci/run-env-tests.js stage mac-safari", "ci:test:stage:safari:bs": "node ./ci/run-env-tests.js stage mac-safari --use-browserstack", "ci:test:stage:android": "node ./ci/run-env-tests.js stage samsung-galaxy-s23", "ci:test:stage:android:bs": "node ./ci/run-env-tests.js stage samsung-galaxy-s23 --use-browserstack", "ci:test:stage:ios": "node ./ci/run-env-tests.js stage iphone-14", "ci:test:stage:ios:bs": "node ./ci/run-env-tests.js stage iphone-14 --use-browserstack", "// SEQUENTIAL TESTING": "Run tests on multiple platforms sequentially", "ci:test:sequential": "node ./ci/run-sequential-tests.js", "ci:test:smoke:sequential": "node ./ci/run-sequential-tests.js stage tests/regression/main-purchase.spec.js @stage_one_time_smoke windows-chrome"}, "dependencies": {"@google/generative-ai": "^0.1.0", "@playwright/test": "^1.51.0", "axios": "^1.8.1", "cloudinary": "^2.5.1", "mailtrap": "^4.0.0", "playwright": "^1.51.0", "ssh2": "^1.16.0", "tunnel-ssh": "^5.2.0", "yaml": "^2.7.0"}, "devDependencies": {"@browserstack/mcp-server": "^1.0.13", "@types/node": "^20.11.0", "browserstack-node-sdk": "^1.34.44", "cross-env": "^7.0.3", "cursor-tools": "latest", "dotenv": "^16.4.7", "dotenv-cli": "^8.0.0", "eslint": "^8.56.0", "eslint-plugin-playwright": "^0.22.0", "js-yaml": "^4.1.0", "magnitude-test": "^0.0.16", "mysql2": "^3.0.0", "node-fetch": "^3.3.2", "prettier": "^3.2.0", "typescript": "^5.3.3"}, "engines": {"node": ">=16.0.0"}, "keywords": ["browserstack", "playwright", "testing", "automation", "cross-browser", "visual-testing"], "author": "", "license": "ISC"}