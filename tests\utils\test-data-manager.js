// @ts-check
const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

class TestDataManager {
    constructor() {
        this.productsData = null;
    }

    /**
     * Initialize test data from YAML file
     * @param {string} brand - Brand name (e.g., 'aeons')
     */
    initialize(brand) {
        const dataPath = path.join(process.cwd(), 'tests', 'data', 'brands', brand, 'products.yml');
        const fileContents = fs.readFileSync(dataPath, 'utf8');
        this.productsData = yaml.load(fileContents);
    }

    /**
     * Get product data by slug
     * @param {string} productSlug - Product key in YAML
     * @returns {Object} Product data
     */
    getProductBySlug(productSlug) {
        if (!this.productsData) {
            throw new Error('Test data not initialized. Call initialize() first.');
        }

        console.log('Looking for product with key:', productSlug);
        console.log('Available products:', Object.keys(this.productsData));

        // Get product directly by key
        const product = this.productsData[productSlug];
        if (!product) {
            throw new Error(`Product with key ${productSlug} not found in test data`);
        }

        return product;
    }

    /**
     * Get price for specific purchase type and quantity
     * @param {string} productSlug - Product slug/handle
     * @param {string} purchaseType - 'one_time' or 'subscription'
     * @param {string} quantity - 'minimum', 'medium', or 'maximum'
     * @returns {number} Price value
     */
    getPrice(productSlug, purchaseType, quantity) {
        const product = this.getProductBySlug(productSlug);
        return product.prices[purchaseType][quantity];
    }

    /**
     * Get available flavors for a product
     * @param {string} productSlug - Product slug/handle
     * @returns {Object|null} Flavors object or null if product doesn't have flavors
     */
    getFlavors(productSlug) {
        const product = this.getProductBySlug(productSlug);
        return product.options?.flavors || null;
    }

    /**
     * Get quantity options for a product
     * @param {string} productSlug - Product slug/handle
     * @returns {Object} Quantity options
     */
    getQuantityOptions(productSlug) {
        const product = this.getProductBySlug(productSlug);
        return product.options.quantities;
    }

    /**
     * Check if product supports flavors
     * @param {string} productSlug - Product slug/handle
     * @returns {boolean} True if product has flavor options
     */
    hasFlavorOptions(productSlug) {
        const product = this.getProductBySlug(productSlug);
        return !!product.options?.flavors;
    }
}

module.exports = { TestDataManager };