/**
 * @fileoverview Enhanced Unified Test Fixture
 *
 * A DRY-optimized fixture that eliminates code duplication while maintaining
 * full compatibility with existing TestDataManager patterns and integrations.
 *
 * This fixture extends the existing unified-fixture with enhanced capabilities:
 * - Page Object Factory with lazy loading and caching
 * - Test Data Helper with standardized patterns
 * - Enhanced BrowserStack integration with context awareness
 * - Improved email verification utilities
 * - Device and platform helpers
 *
 * Backward Compatibility: All existing fixture patterns continue to work
 */

const base = require('@playwright/test');
const { BrowserStackService } = require('../../src/utils/browserstack/browserstack-service');
const testDataManager = require('../data/test-data-manager');
const { MailtrapHelper } = require('../../src/utils/email/mailtrap-helper');
const { SessionManager } = require('../../src/utils/session-manager');

/**
 * Enhanced unified fixture with DRY optimizations
 */
const enhancedUnifiedFixture = base.test.extend({
    // Enhanced TestDataManager with improved initialization
    testDataManager: async ({}, use) => {
        const dataSet = process.env.TEST_DATA_SET || 'default';
        const brand = process.env.BRAND || 'aeons';
        const environment = process.env.TEST_ENV || 'stage';

        console.log(`[Enhanced Fixture] Initializing TestDataManager for brand: ${brand}, environment: ${environment}, dataSet: ${dataSet}`);

        // Ensure we're working from the correct directory for TestDataManager
        const originalCwd = process.cwd();
        const projectRoot = originalCwd.includes('tests') ?
            originalCwd.split('tests')[0] : originalCwd;

        if (originalCwd !== projectRoot) {
            process.chdir(projectRoot);
            console.log(`[Enhanced Fixture] Changed working directory to project root: ${projectRoot}`);
        }

        try {
            // Use existing TestDataManager - maintain full compatibility
            testDataManager.initialize(dataSet, brand, environment);
        } finally {
            // Restore original working directory
            if (originalCwd !== projectRoot) {
                process.chdir(originalCwd);
            }
        }

        // Add enhanced debugging capabilities
        testDataManager.debug = {
            getCurrentDataSet: () => dataSet,
            getCurrentBrand: () => brand,
            getCurrentEnvironment: () => environment,
            dumpCurrentConfig: () => ({
                dataSet,
                brand,
                environment,
                baseUrl: testDataManager.getBaseUrl()
            })
        };

        await use(testDataManager);
    },

    // Page Object Factory - lazy loading with caching
    pageObjectFactory: async ({ page }, use) => {
        const { PageObjectFactory } = require('./factories/page-object-factory');
        const factory = new PageObjectFactory(page);
        await use(factory);
    },

    // Test Data Helper - standardized patterns
    testDataHelper: async ({ testDataManager }, use) => {
        const { TestDataHelper } = require('./helpers/test-data-helper');
        const helper = new TestDataHelper(testDataManager);
        await use(helper);
    },

    // Enhanced BrowserStack helper with context awareness
    browserStackHelper: async ({}, use) => {
        const { BrowserStackEnhancedHelper } = require('./integrations/browserstack-enhanced-helper');
        const helper = new BrowserStackEnhancedHelper();
        await use(helper);
    },

    // Enhanced Mailtrap helper with better error handling
    mailtrapHelper: async ({}, use) => {
        console.log('[Enhanced Fixture] Initializing enhanced Mailtrap helper');

        // Force reload environment variables - maintain existing pattern
        try {
            require('dotenv').config();
            console.log('[Enhanced Fixture] Reloaded environment variables from .env file');
        } catch (error) {
            console.warn('[Enhanced Fixture] Could not reload .env file:', error.message);
        }

        // Use existing configuration pattern with fallbacks
        const config = {
            apiToken: process.env.MAILTRAP_API_TOKEN || '5a221153388388882ce72db80cd8ac23',
            inboxId: process.env.MAILTRAP_INBOX_ID || '3136083'
        };

        const mailtrapHelper = new MailtrapHelper(config);
        await use(mailtrapHelper);
    },

    // Device and platform helper
    deviceHelper: async ({}, use) => {
        const { DeviceHelper } = require('./helpers/device-helper');
        const helper = new DeviceHelper();
        await use(helper);
    },

    // Email verification helper
    emailHelper: async ({ mailtrapHelper, testDataManager }, use) => {
        const { EmailVerificationHelper } = require('./helpers/email-verification-helper');
        const helper = new EmailVerificationHelper(mailtrapHelper, testDataManager);
        await use(helper);
    },

    // Enhanced timeout management based on platform
    timeout: [({ }) => {
        const platform = process.env.PLATFORM || '';
        // Maintain existing timeout logic
        if (platform.includes('android') || platform.includes('ios') ||
            platform.includes('galaxy') || platform.includes('iphone') ||
            process.env.IS_MOBILE === 'true') {
            return 300000; // 5 minutes for mobile
        }
        return 120000; // 2 minutes for desktop
    }, { option: true }],

    // Enhanced context management - maintain existing patterns
    context: async ({ context }, use) => {
        const sessionManager = SessionManager.getInstance();
        const testId = process.env.TEST_ID || `test_${Date.now()}`;
        const isBrowserStackSdkMode = process.env.BROWSERSTACK_SDK_ENABLED === 'true';

        // Maintain existing context tracking logic
        if (isBrowserStackSdkMode) {
            console.log('[Enhanced Fixture] BrowserStack SDK mode enabled - context will be managed by SDK');
        } else {
            const platform = process.env.PLATFORM || 'unknown';
            const isAndroid = platform.includes('android') || platform.includes('galaxy');

            if (!isAndroid) {
                await sessionManager.registerSession(testId, context, {
                    metadata: {
                        platform: platform,
                        browser: process.env.BROWSER || 'unknown',
                        testEnv: process.env.TEST_ENV || 'stage',
                        brand: process.env.BRAND || 'aeons'
                    }
                });
            } else {
                console.log('[Enhanced Fixture] Android platform detected, skipping session registration to prevent race conditions');
            }
        }

        await use(context);
    },

    // Enhanced page management with additional utilities
    page: async ({ page, browserStackHelper, deviceHelper }, use) => {
        const isBrowserStackSdkMode = process.env.BROWSERSTACK_SDK_ENABLED === 'true';

        // Maintain existing BrowserStack session registration
        if (process.env.BROWSERSTACK_SESSION_ID && !isBrowserStackSdkMode) {
            const bsService = BrowserStackService.getInstance();
            await bsService.registerSession(process.env.BROWSERSTACK_SESSION_ID);
            console.log('[Enhanced Fixture] BrowserStack session registered');
        }

        // Enhanced screenshot method with context awareness
        page.takeScreenshotWithContext = async (name, testContext = {}) => {
            return await browserStackHelper.takeScreenshotWithContext(page, name, testContext);
        };

        // Maintain existing screenshot method for backward compatibility
        page.takeScreenshotForBrowserStack = async (name, options = {}) => {
            const { VisualAnalysisHelper } = require('../../src/utils/visual-analisys-helper');
            const testName = options.testName || base.test.info().title || 'unknown';

            const enhancedOptions = {
                ...options,
                cdpMeta: {
                    _requestedPlatform: process.env.PLATFORM || 'unknown',
                    _actualPlatform: process.env.PLATFORM || 'unknown',
                    _requestedDeviceType: null,
                    browserStackSession: process.env.BROWSERSTACK_SESSION_ID
                }
            };

            return await VisualAnalysisHelper.captureAndUploadScreenshot(
                page,
                testName,
                name,
                enhancedOptions
            );
        };

        // Enhanced visual stability method
        page.waitForVisualStability = async (options = {}) => {
            const bsService = BrowserStackService.getInstance();
            return await bsService.waitForVisualStability(page, options);
        };

        // Enhanced mobile viewport setup
        await deviceHelper.setupMobileViewportIfNeeded(page);

        await use(page);

        // Maintain existing test status reporting
        if (process.env.BROWSERSTACK_SESSION_ID && !isBrowserStackSdkMode) {
            try {
                const testInfo = base.test.info();
                const status = testInfo.status === 'passed' ? 'passed' : 'failed';
                const reason = testInfo.error ? testInfo.error.message : 'Test completed';

                const bsService = BrowserStackService.getInstance();
                await bsService.setTestStatus(status, reason);
            } catch (error) {
                console.warn('[Enhanced Fixture] Could not update BrowserStack test status:', error.message);
            }
        }
    },

    // Enhanced device info with more comprehensive platform detection
    deviceInfo: async ({}, use) => {
        const platform = process.env.PLATFORM || '';
        const isMobile = platform.includes('galaxy') ||
                       platform.includes('iphone') ||
                       platform.includes('android') ||
                       platform.includes('ios') ||
                       process.env.IS_MOBILE === 'true';

        // Enhanced platform mapping
        const getPlatformName = (platformId) => {
            const platformMap = {
                'windows-chrome': 'Chrome on Windows',
                'mac-safari': 'Safari on Mac',
                'firefox': 'Firefox',
                'samsung-galaxy-s23': 'Samsung Galaxy S23',
                'iphone-14': 'iPhone 14',
                'iphone14': 'iPhone 14',
                'galaxy': 'Samsung Galaxy',
                'android': 'Android Device',
                'ios': 'iOS Device'
            };
            return platformMap[platformId] || (platformId || 'Local Desktop');
        };

        const info = {
            platform,
            isMobile,
            deviceName: getPlatformName(platform),
            isRealDevice: process.env.BROWSERSTACK_REAL_DEVICE === 'true',
            isBrowserStackSdk: process.env.BROWSERSTACK_SDK_ENABLED === 'true'
        };

        await use(info);
    },

    // Backward compatibility aliases for existing tests
    pageObjects: async ({ pageObjectFactory }, use) => {
        // Provide the legacy pageObjects structure for backward compatibility
        await use(pageObjectFactory.getAll());
    },

    // Base URL helper for convenience
    baseUrl: async ({ testDataManager }, use) => {
        const baseUrl = testDataManager.getBaseUrl();
        await use(baseUrl);
    }
});

// Export the enhanced test fixture with expect
module.exports = {
    test: enhancedUnifiedFixture,
    expect: base.expect
};
