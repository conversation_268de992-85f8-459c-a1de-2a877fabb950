/**
 * Sequential test runner for BrowserStack
 * Runs tests on multiple platforms one after another
 *
 * Usage:
 * node run-sequential-tests.js <env> <test-file> <test-tag> <platform1> <platform2> ...
 *
 * Example:
 * node run-sequential-tests.js stage tests/regression/aeons/smoke/main-purchase.spec.js @stage_one_time_smoke windows-chrome iphone-14
 */

const { execSync } = require('child_process');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Load environment variables from .env file if present
try {
  require('dotenv').config();
} catch (error) {
  console.warn('Warning: Could not load .env file');
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length < 4) {
  console.error('Error: Not enough arguments');
  console.log('Usage: node run-sequential-tests.js <env> <test-file> <test-tag> <platform1> <platform2> ...');
  console.log('Example: node run-sequential-tests.js stage tests/regression/aeons/smoke/main-purchase.spec.js @stage_one_time_smoke windows-chrome iphone-14');
  process.exit(1);
}

const env = args[0];
const testFile = args[1];
const testTag = args[2];
const platforms = args.slice(3);

// Supported platforms
const supportedPlatforms = [
  'windows-chrome', 'mac-safari', 'firefox', 'samsung-galaxy-s23', 'iphone-14'
];

// Validate platforms
for (const platform of platforms) {
  if (!supportedPlatforms.includes(platform)) {
    console.error(`Error: Invalid platform: ${platform}`);
    console.log('Available platforms:');
    console.log('Desktop:', supportedPlatforms.filter(p => !p.includes('galaxy') && !p.includes('iphone')).join(', '));
    console.log('Mobile:', supportedPlatforms.filter(p => p.includes('galaxy') || p.includes('iphone')).join(', '));
    process.exit(1);
  }
}

// Verify BrowserStack credentials
if (!process.env.BROWSERSTACK_USERNAME || !process.env.BROWSERSTACK_ACCESS_KEY) {
  console.error('Error: BrowserStack credentials not found in environment variables');
  console.log('Please set BROWSERSTACK_USERNAME and BROWSERSTACK_ACCESS_KEY environment variables');
  process.exit(1);
}

// Function to run tests on a single platform
async function runTestOnPlatform(platform) {
  return new Promise((resolve, reject) => {
    console.log(`\n==================================================`);
    console.log(`Running tests on platform: ${platform}`);
    console.log(`==================================================\n`);

    // Set build name with timestamp to group related runs
    const timestamp = new Date().toISOString().replace(/[:.]/g, '_');
    const buildName = `sequential_${env}_${timestamp}`;

    // Construct the command
    const command = [
      'node',
      './ci/run-env-tests.js',
      env,
      platform,
      `--test-file=${testFile}`,
      `--tag=${testTag}`,
      '--use-browserstack',
      '--workers=1'
    ];

    console.log(`Command: ${command.join(' ')}`);

    // Set environment variables
    const isMobile = platform.includes('galaxy') || platform.includes('iphone');
    const envVars = {
      ...process.env,
      BUILD_NAME: buildName,
      // Explicitly set real mobile flag for mobile devices
      BROWSERSTACK_REAL_MOBILE: isMobile ? 'true' : 'false',
      // Force real device testing for mobile platforms
      FORCE_REAL_MOBILE: isMobile ? 'true' : 'false',
      // Set device type flag
      IS_MOBILE: isMobile ? 'true' : 'false'
    };

    // Run the command
    const child = spawn('node', command.slice(1), {
      env: envVars,
      stdio: 'inherit'
    });

    child.on('close', (code) => {
      if (code === 0) {
        console.log(`\n✅ Tests completed successfully on ${platform}`);
        resolve();
      } else {
        console.error(`\n❌ Tests failed on ${platform} with exit code ${code}`);
        // We'll still resolve to continue with the next platform
        resolve();
      }
    });

    child.on('error', (error) => {
      console.error(`\n❌ Error running tests on ${platform}: ${error.message}`);
      reject(error);
    });
  });
}

// Run tests on each platform sequentially
async function runSequentialTests() {
  console.log('Sequential Test Runner Configuration:');
  console.log('--------------------------------------------------');
  console.log(`Environment:    ${env}`);
  console.log(`Test File:      ${testFile}`);
  console.log(`Test Tag:       ${testTag}`);
  console.log(`Platforms:      ${platforms.join(', ')}`);
  console.log('--------------------------------------------------');

  // Check BrowserStack plan to confirm parallel session limit
  try {
    const response = execSync(`curl -s -u "${process.env.BROWSERSTACK_USERNAME}:${process.env.BROWSERSTACK_ACCESS_KEY}" https://api.browserstack.com/automate/plan.json`, { encoding: 'utf8' });
    const plan = JSON.parse(response);
    console.log(`BrowserStack Plan: ${plan.automate_plan}`);
    console.log(`Parallel Sessions Allowed: ${plan.parallel_sessions_max_allowed}`);
    console.log(`Parallel Sessions Running: ${plan.parallel_sessions_running}`);
    console.log('--------------------------------------------------');

    if (plan.parallel_sessions_max_allowed < platforms.length && platforms.length > 1) {
      console.warn(`⚠️ Warning: Your BrowserStack plan allows ${plan.parallel_sessions_max_allowed} parallel sessions, but you're trying to run tests on ${platforms.length} platforms.`);
      console.log('Tests will be run sequentially to avoid exceeding your plan limits.');
    }
  } catch (error) {
    console.warn(`⚠️ Warning: Could not check BrowserStack plan: ${error.message}`);
  }

  // Run tests on each platform sequentially
  for (const platform of platforms) {
    try {
      await runTestOnPlatform(platform);
    } catch (error) {
      console.error(`Error running tests on ${platform}: ${error.message}`);
    }
  }

  console.log('\n==================================================');
  console.log('All tests completed');
  console.log('==================================================');
}

// Run the tests
runSequentialTests().catch(error => {
  console.error(`Error running sequential tests: ${error.message}`);
  process.exit(1);
});
