/**
 * Page object for the sales funnel confirmation page
 */
class SalesFunnelConfirmationPage {
    /**
     * @param {import('@playwright/test').Page} page
     */
    constructor(page) {
        this.page = page;
        this.selectors = {
            // Confirmation header
            header: {
                container: '.order-confirmation, .thank-you-header',
                title: 'h1, .thank-you-title',
                orderNumber: '.order-number, .thank-you-order-number, .order-reference'
            },
            
            // Order details
            orderDetails: {
                container: '.order-details, .thank-you-order-details',
                
                // Order items
                items: '.order-item, .thank-you-product, .line-item',
                itemName: '.order-item-name, .product-name, .item-name',
                itemPrice: '.order-item-price, .product-price, .item-price',
                
                // Initial product specific selectors
                initialProduct: '.initial-product, .main-product, .product-details:first-child',
                
                // Upsell product specific selectors (if accepted)
                upsellProduct: '.upsell-product, .additional-product, .product-details:not(:first-child)',
                
                // Order totals
                subtotal: '.order-subtotal, .subtotal-value',
                shipping: '.order-shipping, .shipping-value',
                total: '.order-total, .total-value'
            },
            
            // Customer details
            customerDetails: {
                container: '.customer-details, .thank-you-customer-details',
                email: '.customer-email, .email-value',
                phone: '.customer-phone, .phone-value'
            },
            
            // Shipping details
            shippingDetails: {
                container: '.shipping-details, .thank-you-shipping-details',
                name: '.shipping-name, .shipping-recipient',
                address: '.shipping-address, .shipping-street',
                city: '.shipping-city, .shipping-city-value',
                postcode: '.shipping-postcode, .shipping-postcode-value',
                country: '.shipping-country, .shipping-country-value'
            }
        };
    }

    /**
     * Wait for the confirmation page to load
     * @returns {Promise<boolean>} True if confirmation page was detected
     */
    async waitForOrderConfirmation() {
        console.log('Waiting for order confirmation page');
        
        try {
            // Check if the URL contains confirmation or thank-you
            const currentUrl = this.page.url();
            const isConfirmationUrl = currentUrl.includes('/confirmation/') || 
                                      currentUrl.includes('/thank-you/');
            
            if (!isConfirmationUrl) {
                console.log('Not on a confirmation page based on URL');
                return false;
            }
            
            console.log('Confirmation URL detected, waiting for content to load');
            
            // Wait for page to load completely
            await this.page.waitForLoadState('networkidle');
            
            // Take a screenshot for debugging
            await this.page.screenshot({ path: `sales-funnel-confirmation-${Date.now()}.png` });
            
            // Wait for confirmation header to be visible
            const headerContainer = this.page.locator(this.selectors.header.container);
            await headerContainer.waitFor({ state: 'visible', timeout: 30000 });
            
            console.log('Confirmation page loaded successfully');
            return true;
        } catch (error) {
            console.error(`Error waiting for confirmation page: ${error.message}`);
            // Take a screenshot to help debug the issue
            await this.page.screenshot({ path: `confirmation-error-${Date.now()}.png` });
            throw new Error(`Failed to load confirmation page: ${error.message}`);
        }
    }

    /**
     * Get the order number from the confirmation page
     * @returns {Promise<string>} The order number
     */
    async getOrderNumber() {
        console.log('Getting order number');
        
        try {
            const orderNumberEl = this.page.locator(this.selectors.header.orderNumber);
            const orderNumberText = await orderNumberEl.textContent();
            
            // Extract just the number portion using regex
            const orderNumberMatch = orderNumberText?.match(/[#]?([0-9]+)/);
            const orderNumber = orderNumberMatch ? orderNumberMatch[1] : orderNumberText?.trim();
            
            console.log(`Found order number: ${orderNumber}`);
            return orderNumber || '';
        } catch (error) {
            console.error(`Error getting order number: ${error.message}`);
            return '';
        }
    }

    /**
     * Get the complete order details from the confirmation page
     * @returns {Promise<{initialProduct: {name: string, price: string}, upsellProduct: {name: string, price: string} | null, subtotal: string, shipping: string, total: string}>}
     */
    async getOrderDetails() {
        console.log('Getting order details');
        
        const orderDetails = {
            initialProduct: { name: '', price: '' },
            upsellProduct: null,
            subtotal: '',
            shipping: '',
            total: ''
        };
        
        try {
            // Get the order items
            const items = this.page.locator(this.selectors.orderDetails.items);
            const count = await items.count();
            
            // Process each item
            if (count > 0) {
                // First item is always the initial product
                const initialItem = items.first();
                const initialName = await initialItem.locator(this.selectors.orderDetails.itemName).textContent();
                const initialPrice = await initialItem.locator(this.selectors.orderDetails.itemPrice).textContent();
                
                orderDetails.initialProduct = {
                    name: initialName?.trim() || '',
                    price: initialPrice?.trim() || ''
                };
                
                // If there's a second item, it's the upsell product
                if (count > 1) {
                    const upsellItem = items.nth(1);
                    const upsellName = await upsellItem.locator(this.selectors.orderDetails.itemName).textContent();
                    const upsellPrice = await upsellItem.locator(this.selectors.orderDetails.itemPrice).textContent();
                    
                    orderDetails.upsellProduct = {
                        name: upsellName?.trim() || '',
                        price: upsellPrice?.trim() || ''
                    };
                }
            } else {
                // Try specific selectors for initial and upsell products
                try {
                    const initialProduct = this.page.locator(this.selectors.orderDetails.initialProduct);
                    if (await initialProduct.isVisible()) {
                        const initialName = await this.page.locator(`${this.selectors.orderDetails.initialProduct} ${this.selectors.orderDetails.itemName}`).textContent();
                        const initialPrice = await this.page.locator(`${this.selectors.orderDetails.initialProduct} ${this.selectors.orderDetails.itemPrice}`).textContent();
                        
                        orderDetails.initialProduct = {
                            name: initialName?.trim() || '',
                            price: initialPrice?.trim() || ''
                        };
                    }
                    
                    const upsellProduct = this.page.locator(this.selectors.orderDetails.upsellProduct);
                    if (await upsellProduct.isVisible()) {
                        const upsellName = await this.page.locator(`${this.selectors.orderDetails.upsellProduct} ${this.selectors.orderDetails.itemName}`).textContent();
                        const upsellPrice = await this.page.locator(`${this.selectors.orderDetails.upsellProduct} ${this.selectors.orderDetails.itemPrice}`).textContent();
                        
                        orderDetails.upsellProduct = {
                            name: upsellName?.trim() || '',
                            price: upsellPrice?.trim() || ''
                        };
                    }
                } catch (specificError) {
                    console.warn(`Error finding products with specific selectors: ${specificError.message}`);
                }
            }
            
            // Try to fall back to any product names/prices on the page if initial product not found
            if (!orderDetails.initialProduct.name) {
                const allProductNames = this.page.locator(this.selectors.orderDetails.itemName);
                const allProductPrices = this.page.locator(this.selectors.orderDetails.itemPrice);
                
                if (await allProductNames.count() > 0) {
                    orderDetails.initialProduct.name = await allProductNames.first().textContent() || '';
                    
                    if (await allProductPrices.count() > 0) {
                        orderDetails.initialProduct.price = await allProductPrices.first().textContent() || '';
                    }
                }
            }
            
            // Get the order summary values
            const subtotalEl = this.page.locator(this.selectors.orderDetails.subtotal);
            const shippingEl = this.page.locator(this.selectors.orderDetails.shipping);
            const totalEl = this.page.locator(this.selectors.orderDetails.total);
            
            if (await subtotalEl.isVisible()) {
                orderDetails.subtotal = await subtotalEl.textContent() || '';
            }
            
            if (await shippingEl.isVisible()) {
                orderDetails.shipping = await shippingEl.textContent() || '';
            }
            
            if (await totalEl.isVisible()) {
                orderDetails.total = await totalEl.textContent() || '';
            }
            
            console.log('Order details:', orderDetails);
            return orderDetails;
        } catch (error) {
            console.error(`Error getting order details: ${error.message}`);
            return orderDetails;
        }
    }

    /**
     * Get the customer details from the confirmation page
     * @returns {Promise<{email: string, phone: string}>}
     */
    async getCustomerDetails() {
        console.log('Getting customer details');
        
        try {
            const email = await this.page.locator(this.selectors.customerDetails.email).textContent();
            const phone = await this.page.locator(this.selectors.customerDetails.phone).textContent();
            
            const details = {
                email: email?.trim() || '',
                phone: phone?.trim() || ''
            };
            
            console.log('Customer details:', details);
            return details;
        } catch (error) {
            console.error(`Error getting customer details: ${error.message}`);
            return { email: '', phone: '' };
        }
    }

    /**
     * Get the shipping details from the confirmation page
     * @returns {Promise<{name: string, address: string, city: string, postcode: string, country: string}>}
     */
    async getShippingDetails() {
        console.log('Getting shipping details');
        
        try {
            const name = await this.page.locator(this.selectors.shippingDetails.name).textContent();
            const address = await this.page.locator(this.selectors.shippingDetails.address).textContent();
            const city = await this.page.locator(this.selectors.shippingDetails.city).textContent();
            const postcode = await this.page.locator(this.selectors.shippingDetails.postcode).textContent();
            const country = await this.page.locator(this.selectors.shippingDetails.country).textContent();
            
            const details = {
                name: name?.trim() || '',
                address: address?.trim() || '',
                city: city?.trim() || '',
                postcode: postcode?.trim() || '',
                country: country?.trim() || ''
            };
            
            console.log('Shipping details:', details);
            return details;
        } catch (error) {
            console.error(`Error getting shipping details: ${error.message}`);
            return { name: '', address: '', city: '', postcode: '', country: '' };
        }
    }
    
    /**
     * Verify that a product is in the order
     * @param {string} productName - The name of the product to check for
     * @returns {Promise<boolean>} True if the product is found in the order
     */
    async verifyProductInOrder(productName) {
        console.log(`Verifying product "${productName}" is in the order`);
        
        try {
            const orderDetails = await this.getOrderDetails();
            
            // Check in the initial product
            if (orderDetails.initialProduct.name.includes(productName)) {
                console.log(`Found "${productName}" as the initial product`);
                return true;
            }
            
            // Check in the upsell product if present
            if (orderDetails.upsellProduct && orderDetails.upsellProduct.name.includes(productName)) {
                console.log(`Found "${productName}" as the upsell product`);
                return true;
            }
            
            // If not found in the structured data, try a more general approach
            const allProductElements = this.page.locator(this.selectors.orderDetails.itemName);
            const count = await allProductElements.count();
            
            for (let i = 0; i < count; i++) {
                const text = await allProductElements.nth(i).textContent();
                if (text && text.includes(productName)) {
                    console.log(`Found "${productName}" in product at index ${i}`);
                    return true;
                }
            }
            
            console.log(`Product "${productName}" not found in the order`);
            return false;
        } catch (error) {
            console.error(`Error verifying product in order: ${error.message}`);
            return false;
        }
    }
}

module.exports = { SalesFunnelConfirmationPage }; 