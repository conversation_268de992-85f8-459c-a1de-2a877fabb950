/**
 * @fileoverview Content analysis utilities for Gemini
 */

const { prompts } = require('../config/prompts');

class ContentAnalyzer {
    /**
     * Constructor for ContentAnalyzer
     * @param {Object} geminiService Gemini service instance
     */
    constructor(geminiService) {
        this.geminiService = geminiService;
    }

    /**
     * Analyze content
     * @param {Object} content Content to analyze
     * @param {Object} options Analysis options
     * @returns {Promise<Object>} Analysis results
     */
    async analyze(content, options = {}) {
        try {
            const prompt = prompts.contentAnalysis;
            const analysis = await this.geminiService.analyzeContent(content, prompt);

            return {
                content,
                analysis,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error analyzing content:', error);
            return {
                content,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Analyze multiple content items
     * @param {Array<Object>} contentItems Array of content items to analyze
     * @param {Object} options Analysis options
     * @returns {Promise<Array>} Analysis results
     */
    async analyzeMultiple(contentItems, options = {}) {
        const results = [];

        for (const content of contentItems) {
            try {
                const result = await this.analyze(content, options);
                results.push(result);
            } catch (error) {
                console.error('Error analyzing content item:', error);
                results.push({
                    content,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        }

        return results;
    }
}

module.exports = { ContentAnalyzer }; 