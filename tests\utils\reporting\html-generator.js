/**
 * @fileoverview HTML Report Generator for visual test results
 */

const { REPORT_TEMPLATES } = require('./report-templates');

class HtmlReportGenerator {
    /**
     * Generate HTML report from analysis results
     * @param {Object} report - Analysis report data
     * @returns {string} HTML report
     */
    generateReport(report) {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Visual Analysis Report - ${report.session}</title>
                ${REPORT_TEMPLATES.styles}
            </head>
            <body>
                <h1>Visual Analysis Report</h1>
                ${this.generateSummarySection(report)}
                ${this.generateScreenshotSection(report)}
                ${report.details.video ? this.generateVideoSection(report) : ''}
                ${report.details.logs ? this.generateLogSection(report) : ''}
            </body>
            </html>
        `;
    }

    /**
     * Generate summary section
     * @param {Object} report - Analysis report data
     * @returns {string} HTML for summary section
     */
    generateSummarySection(report) {
        return `
            <div class="section">
                <h2>Session Summary</h2>
                <div class="metadata">
                    <p>Session: ${report.session}</p>
                    <p>Timestamp: ${report.timestamp}</p>
                </div>
                <pre>${JSON.stringify(report.summary, null, 2)}</pre>
            </div>
        `;
    }

    /**
     * Generate screenshot analysis section
     * @param {Object} report - Analysis report data
     * @returns {string} HTML for screenshot section
     */
    generateScreenshotSection(report) {
        return `
            <div class="section">
                <h2>Screenshot Analysis</h2>
                ${report.details.screenshots.map(group => `
                    <div class="subsection">
                        <h3>${group.type} - ${group.testName}</h3>
                        ${group.type === 'responsive' ? this.generateResponsiveAnalysis(group) :
                          group.type === 'content' ? this.generateContentAnalysis(group) :
                          this.generateInteractionAnalysis(group)}
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * Generate responsive analysis HTML
     * @param {Object} group - Analysis group data
     * @returns {string} HTML for responsive analysis
     */
    generateResponsiveAnalysis(group) {
        return `
            <div class="viewport-analysis">
                ${group.viewportAnalysis.map(va => `
                    <div class="subsection">
                        <h4>Viewport: ${va.viewport}</h4>
                        <div class="issues">
                            <h5>Critical Issues:</h5>
                            <pre>${JSON.stringify(va.analysis.criticalIssues, null, 2)}</pre>
                        </div>
                        <div class="improvements">
                            <h5>Improvements:</h5>
                            <pre>${JSON.stringify(va.analysis.improvements, null, 2)}</pre>
                        </div>
                    </div>
                `).join('')}
                ${group.comparisonAnalysis ? `
                    <div class="subsection">
                        <h4>Cross-viewport Analysis</h4>
                        <pre>${JSON.stringify(group.comparisonAnalysis, null, 2)}</pre>
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * Generate content analysis HTML
     * @param {Object} group - Analysis group data
     * @returns {string} HTML for content analysis
     */
    generateContentAnalysis(group) {
        return `
            <div class="content-analysis">
                ${group.individualAnalysis.map(ia => `
                    <div class="subsection">
                        <h4>${ia.screenshot}</h4>
                        <pre>${JSON.stringify(ia.analysis, null, 2)}</pre>
                    </div>
                `).join('')}
                ${group.comparisonAnalysis.map(ca => `
                    <div class="subsection">
                        <h4>Comparison: ${ca.section}</h4>
                        <pre>${JSON.stringify(ca.analysis, null, 2)}</pre>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * Generate interaction analysis HTML
     * @param {Object} group - Analysis group data
     * @returns {string} HTML for interaction analysis
     */
    generateInteractionAnalysis(group) {
        return `
            <div class="interaction-analysis">
                ${group.sequences.map(seq => `
                    <div class="subsection">
                        <h4>Interaction: ${seq.interaction}</h4>
                        <pre>${JSON.stringify(seq.analysis, null, 2)}</pre>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * Generate video analysis section
     * @param {Object} report - Analysis report data
     * @returns {string} HTML for video section
     */
    generateVideoSection(report) {
        return `
            <div class="section">
                <h2>Video Analysis</h2>
                <pre>${JSON.stringify(report.details.video, null, 2)}</pre>
            </div>
        `;
    }

    /**
     * Generate log analysis section
     * @param {Object} report - Analysis report data
     * @returns {string} HTML for log section
     */
    generateLogSection(report) {
        return `
            <div class="section">
                <h2>Log Analysis</h2>
                <pre>${JSON.stringify(report.details.logs, null, 2)}</pre>
            </div>
        `;
    }
}

module.exports = { HtmlReportGenerator }; 