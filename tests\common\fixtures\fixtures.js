/**
 * @fileoverview Common test fixtures for Playwright tests
 * Provides testDataManager and other common fixtures
 */
const base = require('@playwright/test');
const testDataManager = require('../../../tests/data/test-data-manager');
const { MailtrapHelper } = require('../../../src/utils/email/mailtrap-helper');

/**
 * Extended test with custom fixtures
 */
const test = base.test.extend({
    // Base testDataManager fixture
    testDataManager: async ({}, use) => {
        try {
            // Initialize with configuration from environment
            const dataSet = process.env.TEST_DATA_SET || 'staging';
            const brand = process.env.BRAND || 'aeons';
            const environment = process.env.TEST_ENV || 'stage';

            console.log(`Initializing TestDataManager with dataset: ${dataSet}, brand: ${brand}, environment: ${environment}`);
            
            // Initialize test data manager with all parameters
            testDataManager.initialize(dataSet, brand, environment);

            // Add helper methods for debugging
            testDataManager.debug = {
                getCurrentDataSet: () => dataSet,
                getCurrentBrand: () => brand,
                getCurrentEnvironment: () => environment,
                dumpCurrentConfig: () => ({
                    dataSet,
                    brand,
                    environment,
                    baseUrl: testDataManager.getBaseUrl()
                })
            };

            await use(testDataManager);
        } catch (error) {
            console.error('Error initializing testDataManager fixture:', error);
            throw error;
        }
    },
    
    // Add baseUrl as a fixture for convenience
    baseUrl: async ({ testDataManager }, use) => {
        const baseUrl = testDataManager.getBaseUrl();
        await use(baseUrl);
    },

    // Add Mailtrap helper fixture
    mailtrapHelper: async ({}, use) => {
        try {
            console.log('Initializing Mailtrap helper for email verification');
            const mailtrapHelper = new MailtrapHelper();
            await use(mailtrapHelper);
        } catch (error) {
            console.error('Error initializing mailtrapHelper fixture:', error);
            throw error;
        }
    }
});

const expect = base.expect;

module.exports = { test, expect };
