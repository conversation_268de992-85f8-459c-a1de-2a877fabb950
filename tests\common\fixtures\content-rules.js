/**
 * @fileoverview Content rules for product tests
 */

const PRODUCT_CONTENT_RULES = {
    'retinol-serum': {
        requiredSections: [
            'ingredients',
            'how-to-use',
            'benefits',
            'clinical-studies'
        ],
        contentRules: [
            {
                section: 'ingredients',
                minLength: 100,
                mustInclude: [
                    'retinol',
                    'hyaluronic acid',
                    'vitamin e'
                ],
                cannotInclude: [
                    'paraben',
                    'sulfate',
                    'phthalate'
                ]
            },
            {
                section: 'how-to-use',
                minLength: 150,
                mustInclude: [
                    'evening',
                    'clean skin',
                    'sunscreen'
                ]
            },
            {
                section: 'benefits',
                minLength: 200,
                mustInclude: [
                    'fine lines',
                    'wrinkles',
                    'skin texture',
                    'collagen'
                ]
            },
            {
                section: 'clinical-studies',
                minLength: 300,
                mustInclude: [
                    'study',
                    'results',
                    'weeks',
                    'improvement'
                ]
            }
        ],
        textQualityRules: [
            {
                section: 'ingredients',
                sentenceCount: {
                    min: 3,
                    max: 10
                },
                paragraphCount: {
                    min: 1,
                    max: 3
                },
                prohibitedPhrases: [
                    'miracle',
                    'cure',
                    'guaranteed'
                ],
                requiredPhrases: [
                    'key ingredients',
                    'formulated with'
                ]
            },
            {
                section: 'how-to-use',
                sentenceCount: {
                    min: 4,
                    max: 8
                },
                paragraphCount: {
                    min: 1,
                    max: 2
                },
                requiredPhrases: [
                    'apply',
                    'follow with',
                    'use sunscreen'
                ]
            }
        ],
        metaTags: {
            title: {
                minLength: 50
            },
            description: {
                minLength: 150
            },
            keywords: {
                minLength: 100
            }
        }
    },
    'vitamin-c-serum': {
        requiredSections: [
            'ingredients',
            'how-to-use',
            'benefits',
            'clinical-studies'
        ],
        contentRules: [
            {
                section: 'ingredients',
                minLength: 100,
                mustInclude: [
                    'vitamin c',
                    'ferulic acid',
                    'vitamin e'
                ],
                cannotInclude: [
                    'paraben',
                    'sulfate',
                    'artificial fragrance'
                ]
            },
            {
                section: 'how-to-use',
                minLength: 150,
                mustInclude: [
                    'morning',
                    'clean skin',
                    'sunscreen'
                ]
            },
            {
                section: 'benefits',
                minLength: 200,
                mustInclude: [
                    'brightening',
                    'antioxidant',
                    'dark spots',
                    'protection'
                ]
            },
            {
                section: 'clinical-studies',
                minLength: 300,
                mustInclude: [
                    'study',
                    'results',
                    'weeks',
                    'improvement'
                ]
            }
        ],
        textQualityRules: [
            {
                section: 'ingredients',
                sentenceCount: {
                    min: 3,
                    max: 10
                },
                paragraphCount: {
                    min: 1,
                    max: 3
                },
                prohibitedPhrases: [
                    'miracle',
                    'cure',
                    'guaranteed'
                ],
                requiredPhrases: [
                    'key ingredients',
                    'formulated with'
                ]
            },
            {
                section: 'how-to-use',
                sentenceCount: {
                    min: 4,
                    max: 8
                },
                paragraphCount: {
                    min: 1,
                    max: 2
                },
                requiredPhrases: [
                    'apply',
                    'follow with',
                    'use sunscreen'
                ]
            }
        ],
        metaTags: {
            title: {
                minLength: 50
            },
            description: {
                minLength: 150
            },
            keywords: {
                minLength: 100
            }
        }
    }
};

module.exports = { PRODUCT_CONTENT_RULES }; 