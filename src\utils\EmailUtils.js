const fetch = require('node-fetch');

class EmailUtils {
    constructor(config = {}) {
        this.apiToken = config.apiToken || process.env.MAILTRAP_API_TOKEN;
        this.inboxId = config.inboxId || process.env.MAILTRAP_INBOX_ID;
        this.baseUrl = 'https://mailtrap.io/api/v1';
        this.headers = {
            'Authorization': `Bearer ${this.apiToken}`,
            'Content-Type': 'application/json'
        };
    }

    async getEmails(params = {}) {
        const url = new URL(`${this.baseUrl}/inboxes/${this.inboxId}/messages`);
        Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

        const response = await fetch(url, {
            method: 'GET',
            headers: this.headers
        });

        return await response.json();
    }

    async findEmail(criteria = {}) {
        // Default to emails from the last hour
        const search_after = criteria.search_after ||
            new Date(Date.now() - 60 * 60 * 1000).toISOString();

        const emails = await this.getEmails({ search_after });

        // Filter by recipient if provided
        let filtered = emails;
        if (criteria.recipient) {
            filtered = filtered.filter(email =>
                email.to_email.includes(criteria.recipient)
            );
        }

        // Filter by subject if provided
        if (criteria.subject) {
            filtered = filtered.filter(email =>
                email.subject.includes(criteria.subject)
            );
        }

        // Return the most recent matching email
        return filtered.length > 0 ? filtered[0] : null;
    }

    async waitForEmail(criteria = {}, maxAttempts = 10, interval = 5000) {
        let attempts = 0;
        let email = null;

        while (attempts < maxAttempts && !email) {
            email = await this.findEmail(criteria);

            if (!email) {
                attempts++;
                console.log(`Email not found, attempt ${attempts}/${maxAttempts}. Waiting ${interval/1000} seconds...`);
                await new Promise(resolve => setTimeout(resolve, interval));
            }
        }

        return email;
    }

    async getEmailContent(messageId) {
        const response = await fetch(`${this.baseUrl}/inboxes/${this.inboxId}/messages/${messageId}/body.html`, {
            method: 'GET',
            headers: this.headers
        });

        return await response.text();
    }

    async getEmailTextContent(messageId) {
        const response = await fetch(`${this.baseUrl}/inboxes/${this.inboxId}/messages/${messageId}/body.txt`, {
            method: 'GET',
            headers: this.headers
        });

        return await response.text();
    }
}

module.exports = EmailUtils;