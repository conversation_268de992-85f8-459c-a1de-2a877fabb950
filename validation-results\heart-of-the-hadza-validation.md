# Heart of the Hadza Collection Page Selector Validation

URL: https://aeons.co.uk/ritual/sunrise (correct production URL)

## Selectors in the Test vs Actual Content on Site

### Page Accessibility
- **Test selector expects**: The page to exist and be accessible
- **Actual situation**: The page exists on the production site at a different URL structure
- **Status**: ✅ Match - The page exists but at a different URL

### Main Image Text & Other Content
- **Test selector expects**: 
  - `text=The Hadza Tribe have started their day with Baobab fruit for over 50,000 years. It's a treasure trove of powerful compounds which help your microbiome thrive.`
  - `text=With Sunrise Spark we want to bring them to you.`
  - Other description texts
- **Actual content on site**: The page contains exactly the expected text: "The Hadza Tribe have started their day with Baobab fruit for over 50,000 years. It's a treasure trove of powerful compounds which help your microbiome thrive." It also contains the text "WITH SUNRISE SPARK, WE WANT TO BRING THEM TO YOU." The page includes detailed information about the Hadza tribe, the Baobab fruit, and the benefits of Sunrise Spark.
- **Status**: ✅ Match - The content exists as expected at the corrected URL

## Recommendation
The test is trying to validate content that exists on the production site, but the URL structure has changed. The test should be updated to:
1. Use the correct URL format: `https://aeons.co.uk/ritual/sunrise` instead of `https://aeonstest.info/collections/heart-of-the-hadza`
2. Update any environment-specific URLs to match the target environment (test vs. production)

The actual content matches what the test is looking for, but the test is pointing to the wrong URL. Once the URL is updated, the test should pass successfully.
