/**
 * @fileoverview Core Gemini AI interaction helper
 */

const { GoogleGenerativeAI } = require('@google/generative-ai');

class GeminiAnalysisHelper {
    constructor() {
        if (!process.env.GEMINI_API_KEY) {
            throw new Error('GEMINI_API_KEY environment variable is required');
        }
        this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
        this.model = this.genAI.getGenerativeModel({ model: 'gemini-pro-vision' });
    }

    /**
     * Analyze a screenshot with a specific prompt
     * @param {string} imageData Base64 encoded image data
     * @param {string} prompt Analysis prompt
     * @returns {Promise<string>} Analysis result
     */
    async analyzeScreenshot(imageData, prompt) {
        try {
            const result = await this.model.generateContent([
                prompt,
                { inlineData: { data: imageData, mimeType: 'image/png' } }
            ]);
            return result.response.text();
        } catch (error) {
            console.error('Error analyzing screenshot:', error);
            return null;
        }
    }

    /**
     * Compare multiple images with a specific prompt
     * @param {string[]} imageDataArray Array of base64 encoded image data
     * @param {string} prompt Comparison prompt
     * @returns {Promise<string>} Comparison result
     */
    async compareImages(imageDataArray, prompt) {
        try {
            const parts = [
                prompt,
                ...imageDataArray.map(data => ({
                    inlineData: { data, mimeType: 'image/png' }
                }))
            ];

            const result = await this.model.generateContent(parts);
            return result.response.text();
        } catch (error) {
            console.error('Error comparing images:', error);
            return null;
        }
    }

    /**
     * Analyze text content with a specific prompt
     * @param {string} text Text content to analyze
     * @param {string} prompt Analysis prompt
     * @returns {Promise<string>} Analysis result
     */
    async analyzeText(text, prompt) {
        try {
            const result = await this.model.generateContent([prompt, text]);
            return result.response.text();
        } catch (error) {
            console.error('Error analyzing text:', error);
            return null;
        }
    }
}

module.exports = { GeminiAnalysisHelper }; 