/**
 * @fileoverview Purchase Flow Helper
 *
 * Centralized helper for purchase flow operations that eliminates
 * repetitive purchase workflow code across different test files.
 *
 * Features:
 * - Complete purchase flow automation
 * - Support for different payment methods (Stripe, PayPal)
 * - One-time and subscription purchase handling
 * - Mobile-optimized interactions
 * - Error handling and retry logic
 * - Email verification integration
 */

/**
 * Purchase Flow Helper with standardized purchase workflows
 */
class PurchaseFlowHelper {
    constructor(pageObjectFactory, testDataHelper, emailHelper, browserStackHelper, deviceHelper) {
        this.pages = pageObjectFactory;
        this.dataHelper = testDataHelper;
        this.emailHelper = emailHelper;
        this.browserStackHelper = browserStackHelper;
        this.deviceHelper = deviceHelper;

        console.log('[PurchaseFlowHelper] Initialized with all required helpers');
    }

    /**
     * Execute complete standard purchase flow
     * @param {Object} testData - Test data for purchase
     * @param {Object} options - Purchase options
     * @returns {Promise<Object>} Purchase flow results
     */
    async executeStandardPurchase(testData, options = {}) {
        console.log(`[PurchaseFlowHelper] Starting standard purchase flow for ${testData.product.name}`);

        const defaultOptions = {
            purchaseType: 'oneTime',
            paymentMethod: 'stripe',
            verifyEmail: false,
            takeScreenshots: true,
            mobileOptimized: false
        };

        const finalOptions = { ...defaultOptions, ...options };
        const results = {};

        try {
            // Step 1: Navigate to product
            await this.takeScreenshotIfEnabled('purchase-start', testData, finalOptions);
            results.navigation = await this.navigateToProduct(testData);

            // Step 2: Select purchase type and add to cart
            results.productSelection = await this.selectPurchaseTypeAndAddToCart(testData, finalOptions);

            // Step 3: Proceed to checkout
            results.cartProcessing = await this.proceedToCheckout(testData, finalOptions);

            // Step 4: Fill shipping information
            results.shippingInfo = await this.fillShippingInformation(testData, finalOptions);

            // Step 5: Handle payment
            if (finalOptions.paymentMethod === 'paypal') {
                results.payment = await this.handlePayPalPayment(testData, finalOptions);
            } else {
                results.payment = await this.handleStripePayment(testData, finalOptions);
            }

            // Step 6: Complete order and verify
            results.orderCompletion = await this.completeOrderAndVerify(testData, finalOptions);

            // Step 7: Email verification (if enabled)
            if (finalOptions.verifyEmail) {
                results.emailVerification = await this.verifyOrderConfirmationEmail(testData, finalOptions);
            }

            await this.takeScreenshotIfEnabled('purchase-complete', testData, finalOptions);

            console.log(`[PurchaseFlowHelper] Standard purchase flow completed successfully`);
            return results;

        } catch (error) {
            await this.takeScreenshotIfEnabled('purchase-error', testData, finalOptions);
            console.error(`[PurchaseFlowHelper] Purchase flow failed: ${error.message}`);
            throw error;
        }
    }

    /**
     * Navigate to product page
     * @param {Object} testData - Test data
     * @returns {Promise<Object>} Navigation results
     */
    async navigateToProduct(testData) {
        console.log(`[PurchaseFlowHelper] Navigating to product: ${testData.product.name}`);

        const { product: productPage } = this.pages.shop;
        const productUrl = `${testData.baseUrl}${testData.product.urlPath}`;

        await productPage.page.goto(productUrl);
        await this.deviceHelper.waitForDeviceStability(productPage.page);

        // Verify product page loaded correctly
        await this.verifyProductPageLoaded(productPage.page, testData.product.name);

        return {
            url: productUrl,
            productName: testData.product.name,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Select purchase type and add to cart
     * @param {Object} testData - Test data
     * @param {Object} options - Purchase options
     * @returns {Promise<Object>} Product selection results
     */
    async selectPurchaseTypeAndAddToCart(testData, options) {
        console.log(`[PurchaseFlowHelper] Selecting purchase type: ${options.purchaseType}`);

        const { product: productPage } = this.pages.shop;

        // Select purchase type
        await productPage.selectPurchaseType(options.purchaseType);

        // Add to cart with device-optimized interaction
        if (this.deviceHelper.isMobile()) {
            await this.deviceHelper.deviceOptimizedClick(productPage.page, productPage.addToCartSelector);
        } else {
            await productPage.addToCart();
        }

        // Wait for cart update
        await this.deviceHelper.waitForDeviceStability(productPage.page);

        return {
            purchaseType: options.purchaseType,
            productAdded: testData.product.name,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Proceed to checkout
     * @param {Object} testData - Test data
     * @param {Object} options - Purchase options
     * @returns {Promise<Object>} Cart processing results
     */
    async proceedToCheckout(testData, options) {
        console.log(`[PurchaseFlowHelper] Proceeding to checkout`);

        const { cart: cartPage } = this.pages.shop;

        // Navigate to cart if not already there
        if (!cartPage.page.url().includes('/cart')) {
            await cartPage.page.goto(`${testData.baseUrl}/cart`);
        }

        // Proceed to checkout with device optimization
        if (this.deviceHelper.isMobile()) {
            await this.deviceHelper.deviceOptimizedClick(cartPage.page, cartPage.checkoutButtonSelector);
        } else {
            await cartPage.proceedToCheckout();
        }

        await this.deviceHelper.waitForDeviceStability(cartPage.page);

        return {
            cartProcessed: true,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Fill shipping information
     * @param {Object} testData - Test data
     * @param {Object} options - Purchase options
     * @returns {Promise<Object>} Shipping info results
     */
    async fillShippingInformation(testData, options) {
        console.log(`[PurchaseFlowHelper] Filling shipping information`);

        const { checkout: checkoutPage } = this.pages.shop;

        // Fill shipping information with device-optimized typing
        if (this.deviceHelper.isMobile()) {
            await this.fillShippingInfoMobile(checkoutPage, testData);
        } else {
            await checkoutPage.fillShippingInfo(testData.user);
        }

        // Select shipping method if available
        if (testData.expectedShippingMethodValue) {
            await checkoutPage.selectShippingMethod(testData.expectedShippingMethodValue);
        }

        await this.deviceHelper.waitForDeviceStability(checkoutPage.page);

        return {
            shippingInfoFilled: true,
            shippingMethod: testData.expectedShippingMethodValue,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Handle Stripe payment
     * @param {Object} testData - Test data
     * @param {Object} options - Purchase options
     * @returns {Promise<Object>} Payment results
     */
    async handleStripePayment(testData, options) {
        console.log(`[PurchaseFlowHelper] Processing Stripe payment`);

        const { checkout: checkoutPage } = this.pages.shop;

        // Fill payment information
        if (this.deviceHelper.isMobile()) {
            await this.fillPaymentInfoMobile(checkoutPage, testData);
        } else {
            await checkoutPage.fillPaymentInfo(testData.paymentMethod);
        }

        // Complete order
        await checkoutPage.completeOrder();
        await this.deviceHelper.waitForDeviceStability(checkoutPage.page, { waitTime: 5000 });

        return {
            paymentMethod: 'stripe',
            paymentProcessed: true,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Handle PayPal payment
     * @param {Object} testData - Test data
     * @param {Object} options - Purchase options
     * @returns {Promise<Object>} Payment results
     */
    async handlePayPalPayment(testData, options) {
        console.log(`[PurchaseFlowHelper] Processing PayPal payment`);

        const { checkout: checkoutPage, paypal: paypalPage } = this.pages.shop;

        // Select PayPal payment method
        await checkoutPage.selectPaymentMethod('paypal');

        // Complete order to redirect to PayPal
        await checkoutPage.completeOrder();

        // Handle PayPal flow
        await paypalPage.completePayPalPayment(testData.paymentMethod);

        await this.deviceHelper.waitForDeviceStability(paypalPage.page, { waitTime: 5000 });

        return {
            paymentMethod: 'paypal',
            paymentProcessed: true,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Complete order and verify confirmation
     * @param {Object} testData - Test data
     * @param {Object} options - Purchase options
     * @returns {Promise<Object>} Order completion results
     */
    async completeOrderAndVerify(testData, options) {
        console.log(`[PurchaseFlowHelper] Verifying order completion`);

        const { confirmation: confirmationPage } = this.pages.shop;

        // Wait for confirmation page
        await confirmationPage.page.waitForURL('**/confirmation**', {
            timeout: this.deviceHelper.getRecommendedTimeout(30000)
        });

        // Get order details
        const orderNumber = await confirmationPage.getOrderNumber();
        const orderTotal = await confirmationPage.getOrderTotal();

        // Verify order details
        if (!orderNumber) {
            throw new Error('Order number not found on confirmation page');
        }

        console.log(`[PurchaseFlowHelper] Order completed successfully: ${orderNumber}`);

        return {
            orderNumber,
            orderTotal,
            confirmationUrl: confirmationPage.page.url(),
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Verify order confirmation email
     * @param {Object} testData - Test data
     * @param {Object} options - Purchase options
     * @returns {Promise<Object>} Email verification results
     */
    async verifyOrderConfirmationEmail(testData, options) {
        console.log(`[PurchaseFlowHelper] Verifying order confirmation email`);

        try {
            const emailFound = await this.emailHelper.verifyEmailWithGracefulDegradation(
                'order_confirmation',
                testData.user.email,
                {
                    timeout: 120000, // 2 minutes
                    orderNumber: options.orderNumber
                }
            );

            return {
                emailVerified: emailFound,
                customerEmail: testData.user.email,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.warn(`[PurchaseFlowHelper] Email verification failed: ${error.message}`);
            return {
                emailVerified: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Fill shipping info optimized for mobile
     * @param {Object} checkoutPage - Checkout page object
     * @param {Object} testData - Test data
     * @private
     */
    async fillShippingInfoMobile(checkoutPage, testData) {
        const user = testData.user;

        await this.deviceHelper.deviceOptimizedType(checkoutPage.page, '#firstName', user.firstName);
        await this.deviceHelper.deviceOptimizedType(checkoutPage.page, '#lastName', user.lastName);
        await this.deviceHelper.deviceOptimizedType(checkoutPage.page, '#email', user.email);
        await this.deviceHelper.deviceOptimizedType(checkoutPage.page, '#address1', user.address1);
        await this.deviceHelper.deviceOptimizedType(checkoutPage.page, '#city', user.city);
        await this.deviceHelper.deviceOptimizedType(checkoutPage.page, '#postalCode', user.postalCode);
    }

    /**
     * Fill payment info optimized for mobile
     * @param {Object} checkoutPage - Checkout page object
     * @param {Object} testData - Test data
     * @private
     */
    async fillPaymentInfoMobile(checkoutPage, testData) {
        const payment = testData.paymentMethod;

        await this.deviceHelper.deviceOptimizedType(checkoutPage.page, '#cardNumber', payment.cardNumber);
        await this.deviceHelper.deviceOptimizedType(checkoutPage.page, '#expiryDate', payment.expiryDate);
        await this.deviceHelper.deviceOptimizedType(checkoutPage.page, '#cvv', payment.cvv);
    }

    /**
     * Verify product page loaded correctly
     * @param {Object} page - Playwright page object
     * @param {string} productName - Expected product name
     * @private
     */
    async verifyProductPageLoaded(page, productName) {
        try {
            // Check if we're on a product page by URL pattern
            const currentUrl = page.url();
            const isProductUrl = currentUrl.includes('/products/');

            if (!isProductUrl) {
                throw new Error(`Not on a product page. Current URL: ${currentUrl}`);
            }

            // Try to find product title using common selectors
            const titleSelectors = [
                '.main p.title',
                '.product-title',
                'h1',
                '.product-name',
                '[data-test="product-title"]'
            ];

            let titleFound = false;
            for (const selector of titleSelectors) {
                const titleElement = page.locator(selector);
                if (await titleElement.count() > 0) {
                    const titleText = await titleElement.textContent();
                    if (titleText && titleText.trim()) {
                        console.log(`[PurchaseFlowHelper] Found product title: ${titleText.trim()}`);
                        titleFound = true;
                        break;
                    }
                }
            }

            if (!titleFound) {
                console.warn(`[PurchaseFlowHelper] Product title not found, but URL indicates product page`);
            }

            console.log(`[PurchaseFlowHelper] Product page verification completed for: ${productName}`);
        } catch (error) {
            throw new Error(`Product page verification failed: ${error.message}`);
        }
    }

    /**
     * Take screenshot if enabled
     * @param {string} name - Screenshot name
     * @param {Object} testData - Test data
     * @param {Object} options - Options
     * @private
     */
    async takeScreenshotIfEnabled(name, testData, options) {
        if (options.takeScreenshots) {
            try {
                await this.browserStackHelper.takeScreenshotWithContext(
                    this.pages.shop.product.page,
                    name,
                    {
                        testName: 'purchase-flow',
                        brand: testData.brand,
                        environment: testData.environment,
                        step: name
                    }
                );
            } catch (error) {
                console.warn(`[PurchaseFlowHelper] Screenshot failed: ${error.message}`);
            }
        }
    }
}

module.exports = { PurchaseFlowHelper };
