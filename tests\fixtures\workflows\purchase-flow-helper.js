/**
 * @fileoverview Purchase Flow Helper
 *
 * Centralized helper for purchase flow operations that eliminates
 * repetitive purchase workflow code across different test files.
 *
 * Features:
 * - Complete purchase flow automation
 * - Support for different payment methods (Stripe, PayPal)
 * - One-time and subscription purchase handling
 * - Mobile-optimized interactions
 * - Error handling and retry logic
 * - Email verification integration
 */

/**
 * Purchase Flow Helper with standardized purchase workflows
 */
class PurchaseFlowHelper {
    constructor(pageObjectFactory, testDataHelper, emailHelper, browserStackHelper, deviceHelper) {
        this.pages = pageObjectFactory;
        this.dataHelper = testDataHelper;
        this.emailHelper = emailHelper;
        this.browserStackHelper = browserStackHelper;
        this.deviceHelper = deviceHelper;

        console.log('[PurchaseFlowHelper] Initialized with all required helpers');
    }

    /**
     * Execute complete standard purchase flow
     * @param {Object} testData - Test data for purchase
     * @param {Object} options - Purchase options
     * @returns {Promise<Object>} Purchase flow results
     */
    async executeStandardPurchase(testData, options = {}) {
        console.log(`[PurchaseFlowHelper] Starting standard purchase flow for ${testData.product.name}`);

        const defaultOptions = {
            purchaseType: 'oneTime',
            paymentMethod: 'stripe',
            verifyEmail: false,
            takeScreenshots: true,
            mobileOptimized: false
        };

        const finalOptions = { ...defaultOptions, ...options };
        const results = {};

        try {
            // Step 1: Navigate to product
            await this.takeScreenshotIfEnabled('purchase-start', testData, finalOptions);
            results.navigation = await this.navigateToProduct(testData);

            // Step 2: Select purchase type and add to cart
            results.productSelection = await this.selectPurchaseTypeAndAddToCart(testData, finalOptions);

            // Step 3: Proceed to checkout
            results.cartProcessing = await this.proceedToCheckout(testData, finalOptions);

            // Step 4: Fill shipping information
            results.shippingInfo = await this.fillShippingInformation(testData, finalOptions);

            // Step 5: Handle payment
            if (finalOptions.paymentMethod === 'paypal') {
                results.payment = await this.handlePayPalPayment(testData, finalOptions);
            } else {
                results.payment = await this.handleStripePayment(testData, finalOptions);
            }

            // Step 6: Complete order and verify
            results.orderCompletion = await this.completeOrderAndVerify(testData, finalOptions);

            // Step 7: Email verification (if enabled)
            if (finalOptions.verifyEmail) {
                results.emailVerification = await this.verifyOrderConfirmationEmail(testData, finalOptions);
            }

            await this.takeScreenshotIfEnabled('purchase-complete', testData, finalOptions);

            console.log(`[PurchaseFlowHelper] Standard purchase flow completed successfully`);
            return results;

        } catch (error) {
            await this.takeScreenshotIfEnabled('purchase-error', testData, finalOptions);
            console.error(`[PurchaseFlowHelper] Purchase flow failed: ${error.message}`);
            throw error;
        }
    }

    /**
     * Navigate to product page
     * @param {Object} testData - Test data
     * @returns {Promise<Object>} Navigation results
     */
    async navigateToProduct(testData) {
        console.log(`[PurchaseFlowHelper] Navigating to product: ${testData.product.name}`);

        const { product: productPage } = this.pages.shop;
        const productUrl = `${testData.baseUrl}${testData.product.urlPath}`;

        await productPage.page.goto(productUrl);
        await this.deviceHelper.waitForDeviceStability(productPage.page);

        // Verify product page loaded correctly
        await this.verifyProductPageLoaded(productPage.page, testData.product.name);

        return {
            url: productUrl,
            productName: testData.product.name,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Select purchase type and add to cart
     * @param {Object} testData - Test data
     * @param {Object} options - Purchase options
     * @returns {Promise<Object>} Product selection results
     */
    async selectPurchaseTypeAndAddToCart(testData, options) {
        console.log(`[PurchaseFlowHelper] Selecting purchase type: ${options.purchaseType}`);

        const { product: productPage } = this.pages.shop;

        // Select purchase type
        await productPage.selectPurchaseType(options.purchaseType);

        // Add to cart with device-optimized interaction
        if (this.deviceHelper.isMobile()) {
            await this.deviceHelper.deviceOptimizedClick(productPage.page, productPage.addToCartSelector);
        } else {
            await productPage.addToCart();
        }

        // Wait for cart update
        await this.deviceHelper.waitForDeviceStability(productPage.page);

        return {
            purchaseType: options.purchaseType,
            productAdded: testData.product.name,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Proceed to checkout
     * @param {Object} testData - Test data
     * @param {Object} options - Purchase options
     * @returns {Promise<Object>} Cart processing results
     */
    async proceedToCheckout(testData, options) {
        console.log(`[PurchaseFlowHelper] Proceeding to checkout`);

        const { cart: cartPage } = this.pages.shop;

        // Navigate to cart if not already there
        if (!cartPage.page.url().includes('/cart')) {
            await cartPage.page.goto(`${testData.baseUrl}/cart`);
        }

        // Proceed to checkout with device optimization
        if (this.deviceHelper.isMobile()) {
            await this.deviceHelper.deviceOptimizedClick(cartPage.page, cartPage.checkoutButtonSelector);
        } else {
            await cartPage.proceedToCheckout();
        }

        await this.deviceHelper.waitForDeviceStability(cartPage.page);

        return {
            cartProcessed: true,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Fill shipping information
     * @param {Object} testData - Test data
     * @param {Object} options - Purchase options
     * @returns {Promise<Object>} Shipping info results
     */
    async fillShippingInformation(testData, options) {
        console.log(`[PurchaseFlowHelper] Filling shipping information`);

        const { checkout: checkoutPage } = this.pages.shop;

        // Fill shipping information with device-optimized typing
        if (this.deviceHelper.isMobile()) {
            await this.fillShippingInfoMobile(checkoutPage, testData);
        } else {
            await checkoutPage.fillShippingInfo(testData.user);
        }

        // Select shipping method if available
        if (testData.expectedShippingMethodValue) {
            await checkoutPage.selectShippingMethod(testData.expectedShippingMethodValue);
        }

        await this.deviceHelper.waitForDeviceStability(checkoutPage.page);

        return {
            shippingInfoFilled: true,
            shippingMethod: testData.expectedShippingMethodValue,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Handle Stripe payment
     * @param {Object} testData - Test data
     * @param {Object} options - Purchase options
     * @returns {Promise<Object>} Payment results
     */
    async handleStripePayment(testData, options) {
        console.log(`[PurchaseFlowHelper] Processing Stripe payment`);

        const { checkout: checkoutPage } = this.pages.shop;

        // Fill payment information
        if (this.deviceHelper.isMobile()) {
            await this.fillPaymentInfoMobile(checkoutPage, testData);
        } else {
            await checkoutPage.fillPaymentInfo(testData.paymentMethod);
        }

        // Complete order
        await checkoutPage.completeOrder();
        await this.deviceHelper.waitForDeviceStability(checkoutPage.page, { waitTime: 5000 });

        return {
            paymentMethod: 'stripe',
            paymentProcessed: true,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Handle PayPal payment
     * @param {Object} testData - Test data
     * @param {Object} options - Purchase options
     * @returns {Promise<Object>} Payment results
     */
    async handlePayPalPayment(testData, options) {
        console.log(`[PurchaseFlowHelper] Processing PayPal payment`);

        const { checkout: checkoutPage, paypal: paypalPage } = this.pages.shop;

        // Select PayPal payment method
        await checkoutPage.selectPaymentMethod('paypal');

        // Complete order to redirect to PayPal
        await checkoutPage.completeOrder();

        // Handle PayPal flow
        await paypalPage.completePayPalPayment(testData.paymentMethod);

        await this.deviceHelper.waitForDeviceStability(paypalPage.page, { waitTime: 5000 });

        return {
            paymentMethod: 'paypal',
            paymentProcessed: true,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Complete order and verify confirmation
     * @param {Object} testData - Test data
     * @param {Object} options - Purchase options
     * @returns {Promise<Object>} Order completion results
     */
    async completeOrderAndVerify(testData, options) {
        console.log(`[PurchaseFlowHelper] Verifying order completion`);

        const { confirmation: confirmationPage } = this.pages.shop;

        // Wait for confirmation page
        await confirmationPage.page.waitForURL('**/confirmation**', {
            timeout: this.deviceHelper.getRecommendedTimeout(30000)
        });

        // Get order details
        const orderNumber = await confirmationPage.getOrderNumber();
        const orderTotal = await confirmationPage.getOrderTotal();

        // Verify order details
        if (!orderNumber) {
            throw new Error('Order number not found on confirmation page');
        }

        console.log(`[PurchaseFlowHelper] Order completed successfully: ${orderNumber}`);

        return {
            orderNumber,
            orderTotal,
            confirmationUrl: confirmationPage.page.url(),
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Verify order confirmation email
     * @param {Object} testData - Test data
     * @param {Object} options - Purchase options
     * @returns {Promise<Object>} Email verification results
     */
    async verifyOrderConfirmationEmail(testData, options) {
        console.log(`[PurchaseFlowHelper] Verifying order confirmation email`);

        try {
            const emailFound = await this.emailHelper.verifyEmailWithGracefulDegradation(
                'order_confirmation',
                testData.user.email,
                {
                    timeout: 120000, // 2 minutes
                    orderNumber: options.orderNumber
                }
            );

            return {
                emailVerified: emailFound,
                customerEmail: testData.user.email,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.warn(`[PurchaseFlowHelper] Email verification failed: ${error.message}`);
            return {
                emailVerified: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Fill shipping info optimized for mobile
     * @param {Object} checkoutPage - Checkout page object
     * @param {Object} testData - Test data
     * @private
     */
    async fillShippingInfoMobile(checkoutPage, testData) {
        const user = testData.user;

        await this.deviceHelper.deviceOptimizedType(checkoutPage.page, '#firstName', user.firstName);
        await this.deviceHelper.deviceOptimizedType(checkoutPage.page, '#lastName', user.lastName);
        await this.deviceHelper.deviceOptimizedType(checkoutPage.page, '#email', user.email);
        await this.deviceHelper.deviceOptimizedType(checkoutPage.page, '#address1', user.address1);
        await this.deviceHelper.deviceOptimizedType(checkoutPage.page, '#city', user.city);
        await this.deviceHelper.deviceOptimizedType(checkoutPage.page, '#postalCode', user.postalCode);
    }

    /**
     * Fill payment info optimized for mobile
     * @param {Object} checkoutPage - Checkout page object
     * @param {Object} testData - Test data
     * @private
     */
    async fillPaymentInfoMobile(checkoutPage, testData) {
        const payment = testData.paymentMethod;

        await this.deviceHelper.deviceOptimizedType(checkoutPage.page, '#cardNumber', payment.cardNumber);
        await this.deviceHelper.deviceOptimizedType(checkoutPage.page, '#expiryDate', payment.expiryDate);
        await this.deviceHelper.deviceOptimizedType(checkoutPage.page, '#cvv', payment.cvv);
    }

    /**
     * Verify product page loaded correctly
     * @param {Object} page - Playwright page object
     * @param {string} productName - Expected product name
     * @private
     */
    async verifyProductPageLoaded(page, productName) {
        try {
            // Check if we're on a product page by URL pattern
            const currentUrl = page.url();
            const isProductUrl = currentUrl.includes('/products/');

            if (!isProductUrl) {
                throw new Error(`Not on a product page. Current URL: ${currentUrl}`);
            }

            // Try to find product title using common selectors
            const titleSelectors = [
                '.main p.title',
                '.product-title',
                'h1',
                '.product-name',
                '[data-test="product-title"]'
            ];

            let titleFound = false;
            for (const selector of titleSelectors) {
                const titleElement = page.locator(selector);
                if (await titleElement.count() > 0) {
                    const titleText = await titleElement.textContent();
                    if (titleText && titleText.trim()) {
                        console.log(`[PurchaseFlowHelper] Found product title: ${titleText.trim()}`);
                        titleFound = true;
                        break;
                    }
                }
            }

            if (!titleFound) {
                console.warn(`[PurchaseFlowHelper] Product title not found, but URL indicates product page`);
            }

            console.log(`[PurchaseFlowHelper] Product page verification completed for: ${productName}`);
        } catch (error) {
            throw new Error(`Product page verification failed: ${error.message}`);
        }
    }

    /**
     * Initialize test data (backward compatibility method)
     * @param {Object} testData - Raw test data
     * @returns {Object} Enhanced test data
     */
    initTestData(testData) {
        console.log('[PurchaseFlowHelper] Initializing test data for backward compatibility');

        // Add any missing properties for backward compatibility
        if (!testData.baseUrl && this.dataHelper) {
            testData.baseUrl = this.dataHelper.getBaseUrl();
        }

        return testData;
    }

    /**
     * Verify email confirmation (backward compatibility method)
     * @param {Object} testData - Test data
     * @param {Object} emailHelper - Email helper
     * @param {Object} options - Options
     * @returns {Promise<boolean>} Email verification result
     */
    async verifyEmailConfirmation(testData, emailHelper, options = {}) {
        console.log('[PurchaseFlowHelper] Verifying email confirmation (backward compatibility)');

        try {
            const email = options.emailAddress || testData.user?.email;
            if (!email) {
                console.warn('[PurchaseFlowHelper] No email address provided for verification');
                return true; // Don't fail test
            }

            const emailFound = await emailHelper.waitForEmail(email, 'order_confirmation', 60000);
            return !!emailFound;
        } catch (error) {
            console.warn('[PurchaseFlowHelper] Email verification failed:', error.message);
            return true; // Don't fail test for email issues
        }
    }

    /**
     * Analyze purchase options (from purchase-fixtures.js)
     * @param {Object} testData - Test data
     * @returns {Promise<Object>} Purchase options analysis
     */
    async analyzePurchaseOptions(testData) {
        console.log('[PurchaseFlowHelper] Analyzing purchase options');

        const pageObjects = this.pages.getAll();
        const page = pageObjects.productPage.page;

        const purchaseElements = await page.$$eval('.purchase-option', elements =>
            elements.map(el => ({
                subscription: el.getAttribute('data-variant-option-subscription'),
                visible: window.getComputedStyle(el).display !== 'none',
                text: el.textContent.trim(),
                classes: el.className,
                attributes: {
                    ...Array.from(el.attributes).reduce((acc, attr) => ({
                        ...acc,
                        [attr.name]: attr.value
                    }), {})
                }
            }))
        );

        const expectedTypes = Object.keys(testData.product.options.purchaseTypes);
        console.log('Expected purchase types:', expectedTypes);

        const availableTypes = purchaseElements
            .filter(el => el.visible)
            .map(el => el.subscription === 'yes' ? 'subscription' : 'oneTime');
        console.log('Available purchase types:', availableTypes);

        return { expectedTypes, availableTypes };
    }

    /**
     * Select flavor if available (from purchase-fixtures.js)
     * @param {Object} testData - Test data
     * @returns {Promise<string|null>} Selected flavor
     */
    async selectFlavor(testData) {
        console.log('[PurchaseFlowHelper] Selecting flavor if available');

        const pageObjects = this.pages.getAll();
        const { productPage } = pageObjects;
        const page = productPage.page;

        const flavorContainer = page.locator(productPage.selectors?.variant?.flavorContainer || '.flavor-container');
        const hasFlavorOptions = await flavorContainer.count() > 0;

        if (hasFlavorOptions) {
            const flavorCode = testData.product.flavors?.classic?.name.toLowerCase() || 'truffle';
            await productPage.selectFlavor(flavorCode);

            // Wait for variant update
            await page.waitForTimeout(1000);

            // Store selected flavor for later verification
            testData.selectedFlavor = flavorCode;
            return flavorCode;
        } else {
            console.log('No flavor options available for this product, skipping flavor selection');
            return null;
        }
    }

    /**
     * Set quantity (from purchase-fixtures.js)
     * @param {Object} testData - Test data
     * @returns {Promise<number>} Selected quantity
     */
    async setQuantity(testData) {
        console.log('[PurchaseFlowHelper] Setting quantity');

        const pageObjects = this.pages.getAll();
        const { productPage } = pageObjects;

        const availableOptions = await productPage.getAvailableQuantityOptions();

        let targetQuantity = testData.product.options.quantities.medium.numberOfItems;

        if (availableOptions.length === 1) {
            targetQuantity = parseInt(availableOptions[0].value);
            console.log(`Product has fixed quantity of ${targetQuantity}`);
        } else {
            const hasRequestedQuantity = availableOptions.some(opt => parseInt(opt.value) === targetQuantity);
            if (!hasRequestedQuantity) {
                const fallbackQuantity = parseInt(availableOptions[0].value);
                console.log(`Requested quantity ${targetQuantity} not available, using ${fallbackQuantity} instead`);
                targetQuantity = fallbackQuantity;
            }
        }

        await productPage.setQuantity(targetQuantity);

        // Wait for price update
        await productPage.page.waitForTimeout(1000);

        // Store the selected quantity for later price verification
        testData.selectedQuantity = targetQuantity;
        return targetQuantity;
    }

    /**
     * Select purchase type (from purchase-fixtures.js)
     * @param {Object} testData - Test data
     * @param {string} purchaseType - Purchase type ('oneTime' or 'subscription')
     * @returns {Promise<string>} Expected price
     */
    async selectPurchaseType(testData, purchaseType) {
        console.log('[PurchaseFlowHelper] Selecting purchase type:', purchaseType);

        const pageObjects = this.pages.getAll();
        const { productPage } = pageObjects;

        try {
            await productPage.selectPurchaseType(purchaseType);

            // Store the price after selection for later verification
            testData.expectedPrice = await productPage.getCurrentPrice();
            console.log(`Selected ${purchaseType} purchase, price: ${testData.expectedPrice}`);

            if (purchaseType === 'subscription') {
                await productPage.selectSubscriptionFrequency('2 months');
                testData.expectedFrequency = await productPage.getSubscriptionFrequency();
                testData.expectedPurchaseType = 'Subscribe & Save';
            } else {
                testData.expectedPurchaseType = 'One-Time Purchase';
            }

            return testData.expectedPrice;
        } catch (error) {
            console.error(`Failed to select ${purchaseType} purchase:`, error);
            await productPage.page.screenshot({ path: `./screenshots/purchase-selection-error-${purchaseType}.png` });
            throw error;
        }
    }

    /**
     * Store expected values (from purchase-fixtures.js)
     * @param {Object} testData - Test data
     * @returns {Promise<Object>} Stored values
     */
    async storeExpectedValues(testData) {
        console.log('[PurchaseFlowHelper] Storing expected values');

        const pageObjects = this.pages.getAll();
        const { productPage } = pageObjects;
        const page = productPage.page;

        // Verify price hasn't changed
        const currentPrice = await productPage.getCurrentPrice();

        // Store quantity for later verification
        testData.expectedQuantity = await page.inputValue(productPage.selectors?.quantity?.input || 'input[name="quantity"]')
            .then(value => parseInt(value));
        console.log(`Stored expected quantity: ${testData.expectedQuantity}`);

        // Store flavor if selected
        if (testData.selectedFlavor) {
            testData.expectedFlavorName = testData.product.flavors[testData.selectedFlavor].name;
            console.log(`Stored expected flavor: ${testData.expectedFlavorName}`);
        }

        return {
            price: currentPrice,
            quantity: testData.expectedQuantity,
            flavor: testData.expectedFlavorName
        };
    }

    /**
     * Add to cart and proceed to checkout (from purchase-fixtures.js)
     * @param {Object} testData - Test data
     * @returns {Promise<void>}
     */
    async addToCartAndProceedToCheckout(testData) {
        console.log('[PurchaseFlowHelper] Adding to cart and proceeding to checkout');

        const pageObjects = this.pages.getAll();
        const { productPage, cartPage } = pageObjects;

        // Add to cart
        await productPage.addToCart();
        await productPage.page.waitForTimeout(2000);

        // Navigate to cart
        await cartPage.page.goto(`${testData.baseUrl}/cart`);
        await cartPage.page.waitForLoadState('networkidle');

        // Proceed to checkout
        await cartPage.proceedToCheckout();
        await cartPage.page.waitForLoadState('networkidle');
    }

    /**
     * Verify shipping method and cost (from purchase-fixtures.js)
     * @param {Object} testData - Test data
     * @returns {Promise<void>}
     */
    async verifyShippingMethodAndCost(testData) {
        console.log('[PurchaseFlowHelper] Verifying shipping method and cost');

        const pageObjects = this.pages.getAll();
        const { checkoutPage } = pageObjects;

        // Get available shipping methods
        const shippingMethods = await checkoutPage.getAvailableShippingMethods();
        console.log('Available shipping methods:', shippingMethods);

        if (shippingMethods.length > 0) {
            // Select the first available shipping method
            const selectedMethod = shippingMethods[0];
            await checkoutPage.selectShippingMethod(selectedMethod.value);

            testData.expectedShippingMethodValue = selectedMethod.value;
            testData.expectedShippingCost = selectedMethod.cost;

            console.log(`Selected shipping method: ${selectedMethod.label} (${selectedMethod.cost})`);
        } else {
            console.log('No shipping methods available or shipping is free');
        }
    }

    /**
     * Complete payment and order (from purchase-fixtures.js)
     * @param {Object} testData - Test data
     * @returns {Promise<void>}
     */
    async completePaymentAndOrder(testData) {
        console.log('[PurchaseFlowHelper] Completing payment and order');

        const pageObjects = this.pages.getAll();
        const { checkoutPage } = pageObjects;

        // Fill payment information
        await checkoutPage.fillPaymentInfo(testData.paymentMethod);

        // Complete the order
        await checkoutPage.completeOrder();

        // Wait for confirmation page
        await checkoutPage.page.waitForURL('**/confirmation**', { timeout: 30000 });
        await checkoutPage.page.waitForLoadState('networkidle');
    }

    /**
     * Verify order confirmation (from purchase-fixtures.js)
     * @param {Object} testData - Test data
     * @returns {Promise<string>} Order number
     */
    async verifyOrderConfirmation(testData) {
        console.log('[PurchaseFlowHelper] Verifying order confirmation');

        const pageObjects = this.pages.getAll();
        const { confirmationPage } = pageObjects;

        // Get order number
        const orderNumber = await confirmationPage.getOrderNumber();
        if (!orderNumber) {
            throw new Error('Order number not found on confirmation page');
        }

        // Get order total
        const orderTotal = await confirmationPage.getOrderTotal();

        console.log(`Order confirmed: ${orderNumber}, Total: ${orderTotal}`);

        // Store order details
        testData.orderNumber = orderNumber;
        testData.orderTotal = orderTotal;

        return orderNumber;
    }

    /**
     * Verify payment error (from purchase-fixtures.js)
     * @param {Object} page - Playwright page object
     * @param {boolean} expectError - Whether to expect an error
     * @returns {Promise<boolean>} Whether error was found
     */
    async verifyPaymentError(page, expectError = true) {
        console.log('[PurchaseFlowHelper] Verifying payment error');

        try {
            // Wait for error message to appear
            const errorSelectors = [
                '.error-message',
                '.payment-error',
                '[data-test="error-message"]',
                '.alert-danger',
                '.stripe-error'
            ];

            let errorFound = false;
            for (const selector of errorSelectors) {
                const errorElement = page.locator(selector);
                if (await errorElement.count() > 0) {
                    const errorText = await errorElement.textContent();
                    if (errorText && errorText.trim()) {
                        console.log(`Payment error found: ${errorText.trim()}`);
                        errorFound = true;
                        break;
                    }
                }
            }

            if (expectError && !errorFound) {
                console.warn('Expected payment error but none was found');
            } else if (!expectError && errorFound) {
                console.warn('Unexpected payment error found');
            }

            return errorFound;
        } catch (error) {
            console.warn('Error while checking for payment error:', error.message);
            return false;
        }
    }

    /**
     * Take screenshot if enabled
     * @param {string} name - Screenshot name
     * @param {Object} testData - Test data
     * @param {Object} options - Options
     * @private
     */
    async takeScreenshotIfEnabled(name, testData, options) {
        if (options.takeScreenshots) {
            try {
                await this.browserStackHelper.takeScreenshotWithContext(
                    this.pages.shop.product.page,
                    name,
                    {
                        testName: 'purchase-flow',
                        brand: testData.brand,
                        environment: testData.environment,
                        step: name
                    }
                );
            } catch (error) {
                console.warn(`[PurchaseFlowHelper] Screenshot failed: ${error.message}`);
            }
        }
    }
}

module.exports = { PurchaseFlowHelper };
