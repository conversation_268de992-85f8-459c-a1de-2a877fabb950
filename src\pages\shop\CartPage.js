class CartPage {
    /**
     * @param {import('@playwright/test').Page} page
     */
    constructor(page) {
        this.page = page;

        // Selectors based on actual site structure
        this.selectors = {
            // Main cart elements
            cartTitle: '.cart-title h1',
            cartTable: '.cart-table',
            cartContainer: 'form[name="sylius_cart"]',

            // Cart items and details
            cartItems: '.cart-table tbody tr',
            productContainer: '.product-container',
            productImageAndDescription: '.product-image-and-description',
            // Updated selector to find product by name in the cart table with more precise path
            cartItem: (name) => `.cart-table tbody tr:has(td .product-description h3:text-is("${name}"))`,
            itemName: '.product-description h3',
            // Updated price selector to be more specific
            itemPrice: 'td.numbers span',
            itemQuantity: 'input[type="number"]',
            itemTotal: 'td.numbers:last-of-type',

            // Updated purchase type and variant selectors
            itemPurchaseType: 'span[data-sylius-option-name="Purchase type"]',
            itemFlavor: 'span[data-sylius-option-name="Flavour"]',
            itemFrequency: '.product-description span:has-text("Frequency:") span',

            // Cart actions
            removeItemButton: '[data-js-remove-from-cart-button]',
            updateCartButton: '.update-cart-button',

            // Coupon section
            couponInput: '#sylius_cart_promotionCoupon',
            applyCouponButton: 'button:has-text("Apply coupon")',

            // Summary section
            summarySection: '.cart-summary',
            itemsTotal: '.subtotal .amount',
            shippingTotal: '.shipping .amount',
            orderTotal: '.total .amount',
            checkoutButton: 'button.checkout-btn',

            // Loading state
            loading: '.spinner-overlay',

            // Empty cart
            emptyCart: '.empty-cart-message'
        };
    }

    /**
     * Wait for cart to be ready
     */
    async waitForCartDrawer() {
        try {
            console.log('Waiting for cart container to be visible...');

            // First wait for the cart container
            await this.page.locator(this.selectors.cartContainer).waitFor({
                state: 'visible',
                timeout: 10000
            });

            // Then check if there's a loading overlay and wait for it to disappear
            const loadingOverlay = this.page.locator(this.selectors.loading);
            if (await loadingOverlay.count() > 0) {
                console.log('Loading overlay found, waiting for it to disappear...');
                await loadingOverlay.waitFor({ state: 'hidden', timeout: 10000 });
            }

            // Finally wait for the cart table to be visible
            console.log('Waiting for cart table to be visible...');
            await this.page.locator(this.selectors.cartTable).waitFor({
                state: 'visible',
                timeout: 10000
            });

            console.log('Cart is ready');
        } catch (error) {
            console.error('Error waiting for cart page:', error);
            await this.page.screenshot({ path: 'cart-page-error.png' });
            throw new Error(`Failed to wait for cart page: ${error.message}`);
        }
    }

    /**
     * Find a cart item by product name
     * @param {string} productName
     * @returns {Promise<Locator>}
     */
    async findCartItem(productName) {
        try {
            console.log(`Looking for product "${productName}" in cart...`);

            // Wait for cart table to be visible first
            await this.page.locator(this.selectors.cartTable).waitFor({
                state: 'visible',
                timeout: 10000
            });

            // Use the cartItem selector to find the specific product row
            const item = this.page.locator(this.selectors.cartItem(productName));

            // Wait for the item to be visible with increased timeout
            await item.waitFor({ state: 'visible', timeout: 15000 });

            // Verify that we found exactly one item
            const count = await item.count();
            if (count === 0) {
                throw new Error(`Product "${productName}" not found in cart`);
            }
            if (count > 1) {
                console.warn(`Found ${count} items for product "${productName}" in cart, using the first one`);
            }

            return item;
        } catch (error) {
            console.error(`Failed to find cart item "${productName}":`, error);
            await this.page.screenshot({ path: 'find-cart-item-error.png' });
            throw error;
        }
    }

    /**
     * Get cart item details with improved error handling
     * @param {string} productName
     * @returns {Promise<{name: string, price: number, quantity: number, flavor?: string, purchaseType?: string, frequency?: string}>}
     */
    async getCartItemDetails(productName) {
        try {
            console.log(`Getting details for product "${productName}"...`);

            // Wait for cart table to be visible first
            await this.page.locator(this.selectors.cartTable).waitFor({ state: 'visible', timeout: 10000 });

            console.log(`Looking for product "${productName}" in cart...`);
            const itemSelector = this.selectors.cartItem(productName);
            const item = this.page.locator(itemSelector);

            // Wait for the item to be visible
            await item.waitFor({ state: 'visible', timeout: 10000 });

            // Get basic details with improved error handling and logging
            console.log('Fetching price...');
            const priceText = await item.locator(this.selectors.itemPrice).textContent();
            console.log('Raw price text:', priceText);

            const details = {
                name: await item.locator(this.selectors.itemName).textContent()
                    .then(text => text?.trim() || ''),
                price: parseFloat(priceText?.replace(/[^0-9.]/g, '') || '0'),
                quantity: await item.locator(this.selectors.itemQuantity).inputValue()
                    .then(value => parseInt(value || '1'))
            };

            console.log('Basic cart item details:', details);

            // Get purchase type if available
            const purchaseTypeElement = item.locator(this.selectors.itemPurchaseType);
            if (await purchaseTypeElement.count() > 0) {
                const purchaseType = await purchaseTypeElement.textContent()
                    .then(text => text?.trim() || '');
                // Normalize purchase type text to match expected format
                details.purchaseType = purchaseType === 'Subscribe & Save' ? 'Subscribe & Save' : 'One-Time Purchase';
            }

            // Get flavor if available
            const flavorElement = item.locator(this.selectors.itemFlavor);
            if (await flavorElement.count() > 0) {
                details.flavor = await flavorElement.textContent()
                    .then(text => text?.trim() || '');
            }

            // Get frequency if available
            const frequencyElement = item.locator(this.selectors.itemFrequency);
            if (await frequencyElement.count() > 0) {
                details.frequency = await frequencyElement.textContent()
                    .then(text => text?.trim() || '');
            }

            console.log('Complete cart item details:', details);
            return details;
        } catch (error) {
            console.error(`Failed to get cart item details for "${productName}":`, error);
            if (this.page) {
                try {
                    await this.page.screenshot({ path: 'cart-item-details-error.png' });
                } catch (screenshotError) {
                    console.error('Failed to take error screenshot:', screenshotError);
                }
            }
            throw error;
        }
    }

    /**
     * Get item flavor
     * @param {string} productName
     * @returns {Promise<string|null>}
     */
    async getItemFlavor(productName) {
        try {
            const item = await this.findCartItem(productName);
            const flavorElement = item.locator(this.selectors.itemFlavor);
            return await flavorElement.count() > 0 ? await flavorElement.textContent() : null;
        } catch (error) {
            console.warn(`Error getting flavor for ${productName}:`, error);
            return null;
        }
    }

    /**
     * Get item savings
     * @param {string} productName
     * @returns {Promise<string|null>}
     */
    async getItemSavings(productName) {
        try {
            const item = await this.findCartItem(productName);
            const savingsElement = item.locator(this.selectors.itemSavings);
            return await savingsElement.count() > 0 ? await savingsElement.textContent() : null;
        } catch (error) {
            console.warn(`Error getting savings for ${productName}:`, error);
            return null;
        }
    }

    /**
     * Proceed to checkout
     */
    async proceedToCheckout() {
        await this.page.click(this.selectors.checkoutButton);

        try {
            // First try to wait for the checkout page with a long timeout
            await this.page.waitForLoadState('domcontentloaded', { timeout: 60000 });
        } catch (error) {
            console.warn('Network idle timeout exceeded. Trying alternative approach...');
        }

        // Multiple ways to verify we've reached the checkout page
        try {
            // 1. First check URL - most reliable indicator of page navigation
            const currentUrl = this.page.url();
            console.log(`Current URL after clicking checkout: ${currentUrl}`);

            if (currentUrl.includes('/checkout')) {
                console.log('URL confirms we are on checkout page');

                console.log('Checkout form detected. Proceeding...');
                return; // Successfully navigated to checkout
            } else {
                console.warn('URL does not indicate checkout page. Current URL:', currentUrl);
            }
        } catch (error) {
            console.error('Error detecting checkout page:', error.message);
        }
    }

    /**
     * Verify product details in cart
     * @param {Object} product Product data
     * @param {Object} options Options for verification
     * @param {number} options.quantity Expected quantity
     * @param {string} [options.flavor] Expected flavor
     * @param {string} [options.purchaseType] Expected purchase type
     * @param {string} [options.frequency] Expected frequency
     */
    async verifyProductDetails(product, { quantity, flavor, purchaseType, frequency }) {
        const details = await this.getCartItemDetails(product.name);

        // Verify basic details
        expect(details.name).toBe(product.name);
        expect(details.quantity).toBe(quantity);

        // Verify purchase type if specified
        if (purchaseType) {
            expect(details.purchaseType).toBe(purchaseType);
        }

        // Verify flavor if specified
        if (flavor) {
            expect(details.flavor).toBe(flavor);
        }

        // Verify frequency if specified
        if (frequency) {
            expect(details.frequency).toBe(frequency);
        }

        // Verify price based on purchase type and quantity
        const priceKey = purchaseType === 'Subscribe & Save' ? 'subscription' : 'oneTime';
        let expectedPrice;

        if (quantity === product.options.quantities.minimum.numberOfItems) {
            expectedPrice = product.prices[priceKey].minimum;
        } else if (quantity === product.options.quantities.medium.numberOfItems) {
            expectedPrice = product.prices[priceKey].medium;
        } else if (quantity === product.options.quantities.maximum.numberOfItems) {
            expectedPrice = product.prices[priceKey].maximum;
        }

        if (expectedPrice) {
            expect(details.price).toBe(expectedPrice);
        }
    }
}

module.exports = { CartPage };