import { type MagnitudeConfig } from 'magnitude-test';

export default {
    url: "https://aeonstest.info",
    planner: {
        provider: 'google-ai', // your provider of choice
        options: {
            // any required + optional configuration for that provider
            model: 'claude-3-7-sonnet-latest',
            apiKey: process.env.ANTHROPIC_API_KEY
        }
    },
    executor: {
        provider: 'moondream', // only moondream currently supported
        options: {
            baseUrl: 'https://moondream.ai',
            apiKey: process.env.MOONDREAM_API_KEY // not necessary if self-hosted
        }
    }

} satisfies MagnitudeConfig;
