/**
 * @fileoverview Sales Funnel Flow Validation Test
 *
 * Test to validate the new sales funnel flow fixture by demonstrating
 * the DRY improvements and validating backward compatibility.
 *
 * @tags @validation @sales_funnel_flow @migration
 */

const { test, expect } = require('../fixtures/workflows/sales-funnel-flow-fixture');

test.describe('Sales Funnel Flow Migration Validation', () => {
    test.describe('Enhanced Sales Funnel Flow Tests', () => {
        test('TC-001: Enhanced sales funnel with upsell @enhanced_sales_funnel', async ({
            salesFunnelFlow,
            testDataHelper,
            browserStackHelper,
            deviceHelper,
            salesFunnelPages
        }) => {
            // Set timeout for sales funnel flow
            test.setTimeout(180000);

            console.log('[Sales Funnel Validation] Starting enhanced sales funnel flow test');

            await test.step('Initialize test data', async () => {
                const testData = testDataHelper.getSalesFunnelTestData('default');
                expect(testData).toBeTruthy();
                expect(testData.product).toBeTruthy();
                expect(testData.user).toBeTruthy();
                expect(testData.paymentMethod).toBeTruthy();

                console.log(`Test data initialized for product: ${testData.product.name}`);
            });

            await test.step('Complete sales funnel flow with upsell', async () => {
                const flowOptions = {
                    product: 'default',
                    customer: 'default',
                    paymentMethod: 'stripe_valid',
                    acceptUpsell: true,
                    skipEmailVerification: true // Skip for validation test
                };

                const results = await salesFunnelFlow.completeSalesFunnelWithUpsell(flowOptions);

                expect(results.flowCompleted).toBe(true);
                expect(results.initialOrderId).toBeTruthy();
                expect(results.upsellOrderId).toBeTruthy();
                expect(results.totalAmount).toBeGreaterThan(0);

                console.log(`Sales funnel completed successfully:`, {
                    initialOrder: results.initialOrderId,
                    upsellOrder: results.upsellOrderId,
                    totalAmount: results.totalAmount
                });
            });

            await test.step('Validate page objects accessibility', async () => {
                // Test that all sales funnel page objects are accessible
                expect(salesFunnelPages.adminLogin).toBeTruthy();
                expect(salesFunnelPages.adminSalesFunnel).toBeTruthy();
                expect(salesFunnelPages.adminOrders).toBeTruthy();
                expect(salesFunnelPages.initialCheckout).toBeTruthy();
                expect(salesFunnelPages.payment).toBeTruthy();
                expect(salesFunnelPages.upsell).toBeTruthy();
                expect(salesFunnelPages.confirmation).toBeTruthy();

                console.log('All sales funnel page objects are accessible');
            });
        });

        test('TC-002: Enhanced abandoned cart flow @enhanced_abandoned_cart', async ({
            salesFunnelFlow,
            testDataHelper,
            emailHelper
        }) => {
            test.setTimeout(120000);

            console.log('[Sales Funnel Validation] Starting enhanced abandoned cart flow test');

            await test.step('Complete abandoned cart flow', async () => {
                const flowOptions = {
                    product: 'default',
                    customer: 'default',
                    abandonAtStep: 'payment',
                    waitForEmail: false // Skip email waiting for validation
                };

                const results = await salesFunnelFlow.completeAbandonedCartFlow(flowOptions);

                expect(results.abandonedAt).toBe('payment');

                console.log(`Abandoned cart flow completed:`, {
                    abandonedAt: results.abandonedAt,
                    emailReceived: results.emailReceived
                });
            });
        });

        test('TC-003: Backend API integration validation @backend_api', async ({
            salesFunnelFlow,
            salesFunnelApi,
            testDataHelper
        }) => {
            test.setTimeout(60000);

            console.log('[Sales Funnel Validation] Starting backend API integration test');

            await test.step('Validate API configuration', async () => {
                const apiConfig = testDataHelper.getApiConfig();
                expect(apiConfig).toBeTruthy();
                expect(apiConfig.baseUrl).toBeTruthy();
                expect(apiConfig.brand).toBeTruthy();
                expect(apiConfig.environment).toBeTruthy();

                console.log('API configuration validated:', apiConfig);
            });

            await test.step('Test force complete order functionality', async () => {
                // This is a mock test - in real scenario we'd have an actual order ID
                const mockOrderId = 'test-order-12345';

                try {
                    // Pass the API instance to the helper for testing
                    await salesFunnelFlow.forceCompleteOrderViaBackend(mockOrderId, salesFunnelApi);
                    console.log('Force complete order API call succeeded');
                } catch (error) {
                    console.log('Force complete order API call failed as expected in validation:', error.message);
                    // This is expected in validation environment
                }
            });
        });
    });

    test.describe('Backward Compatibility Tests', () => {
        test('TC-004: Legacy fixture compatibility @legacy_compatibility', async ({
            salesFunnelPages,
            salesFunnelTestData,
            testDataManager,
            pageObjectFactory
        }) => {
            test.setTimeout(60000);

            console.log('[Sales Funnel Validation] Testing backward compatibility');

            await test.step('Validate legacy data access patterns', async () => {
                // Test legacy data access patterns - use AEONS product since we're running with AEONS brand
                const product = testDataManager.getProduct('ancient_roots_olive_oil');
                const user = testDataManager.getUser('default');
                const paymentMethod = testDataManager.getPaymentMethod('stripe_valid');

                expect(product).toBeTruthy();
                expect(user).toBeTruthy();
                expect(paymentMethod).toBeTruthy();

                console.log('Legacy data patterns work correctly with enhanced fixture');
            });

            await test.step('Validate legacy page object access', async () => {
                // Test legacy page object access
                const allPageObjects = pageObjectFactory.getAll();
                expect(allPageObjects.salesFunnelInitialCheckoutPage).toBeTruthy();
                expect(allPageObjects.salesFunnelPaymentPage).toBeTruthy();
                expect(allPageObjects.salesFunnelUpsellPage).toBeTruthy();
                expect(allPageObjects.salesFunnelConfirmationPage).toBeTruthy();
                expect(allPageObjects.adminLoginPage).toBeTruthy();
                expect(allPageObjects.adminSalesFunnelPage).toBeTruthy();

                console.log('Legacy page object patterns work correctly with enhanced fixture');
            });

            await test.step('Validate sales funnel test data structure', async () => {
                expect(salesFunnelTestData).toBeTruthy();
                expect(salesFunnelTestData.product).toBeTruthy();
                expect(salesFunnelTestData.user).toBeTruthy();
                expect(salesFunnelTestData.paymentMethod).toBeTruthy();

                console.log('Sales funnel test data structure is valid');
            });

            await test.step('Validate sales funnel pages structure', async () => {
                // Test the salesFunnelPages fixture structure
                expect(salesFunnelPages.adminLogin).toBeTruthy();
                expect(salesFunnelPages.adminSalesFunnel).toBeTruthy();
                expect(salesFunnelPages.adminOrders).toBeTruthy();
                expect(salesFunnelPages.initialCheckout).toBeTruthy();
                expect(salesFunnelPages.payment).toBeTruthy();
                expect(salesFunnelPages.upsell).toBeTruthy();
                expect(salesFunnelPages.confirmation).toBeTruthy();

                console.log('Sales funnel pages structure is valid');
            });
        });
    });

    test.describe('DRY Optimization Validation', () => {
        test('TC-005: Code duplication reduction validation @dry_optimization', async ({
            salesFunnelFlow,
            testDataHelper,
            pageObjectFactory
        }) => {
            test.setTimeout(60000);

            console.log('[Sales Funnel Validation] Validating DRY optimization benefits');

            await test.step('Validate centralized test data management', async () => {
                // Test that we can get different types of test data through one helper
                const purchaseData = testDataHelper.getPurchaseTestData();
                const subscriptionData = testDataHelper.getSubscriptionTestData();
                const paypalData = testDataHelper.getPayPalTestData();
                const salesFunnelData = testDataHelper.getSalesFunnelTestData('default');

                expect(purchaseData).toBeTruthy();
                expect(subscriptionData).toBeTruthy();
                expect(paypalData).toBeTruthy();
                expect(salesFunnelData).toBeTruthy();

                console.log('Centralized test data management working correctly');
            });

            await test.step('Validate page object factory efficiency', async () => {
                // Test that page objects are created efficiently
                const cacheStats = pageObjectFactory.getCacheStats();
                console.log('Page object factory cache stats:', cacheStats);

                // Access different page object groups
                const shopPages = pageObjectFactory.shop;
                const adminPages = pageObjectFactory.admin;
                const salesFunnelPages = pageObjectFactory.salesFunnel;

                expect(shopPages).toBeTruthy();
                expect(adminPages).toBeTruthy();
                expect(salesFunnelPages).toBeTruthy();

                const finalCacheStats = pageObjectFactory.getCacheStats();
                console.log('Final cache stats:', finalCacheStats);

                // Verify caching is working
                expect(finalCacheStats.size).toBeGreaterThan(0);
                expect(finalCacheStats.totalPageObjects).toBeGreaterThan(0);

                console.log('Page object factory caching working efficiently');
            });
        });
    });
});
