/**
 * @fileoverview BrowserStack integration helper with enhanced functionality
 */

const fs = require('fs').promises;
const path = require('path');

// Fix ES Module loading issue by using dynamic import
let fetchModule;
async function getFetch() {
    if (!fetchModule) {
        try {
            // Try to use node-fetch as CommonJS module first
            fetchModule = require('node-fetch');
        } catch (e) {
            // If that fails, use dynamic import for ES module
            const module = await import('node-fetch');
            fetchModule = module.default;
        }
    }
    return fetchModule;
}

class BrowserStackHelper {
    constructor() {
        this.browserStackUrl = 'https://api.browserstack.com/automate/';
        this.auth = Buffer.from(
            `${process.env.BROWSERSTACK_USERNAME || ''}:${process.env.BROWSERSTACK_ACCESS_KEY || ''}`
        ).toString('base64');
        
        // Track active sessions to prevent resource contention
        this.activeSessionId = null;
        this.isAndroidPlatform = process.env.PLATFORM && process.env.PLATFORM.includes('android');
    }

    // Update all fetch calls to use the async getFetch function
    async _fetch(url, options) {
        const fetch = await getFetch();
        return fetch(url, options);
    }

    /**
     * Waits for visual stability on the page
     * @param {import('@playwright/test').Page} page - Playwright page object
     * @param {Object} options - Stability options
     * @returns {Promise<void>}
     */
    async waitForVisualStability(page, options = {}) {
        const defaultOptions = {
            timeout: 10000,
            maxAttempts: 3,
            stabilityThreshold: 500
        };

        const config = { ...defaultOptions, ...options };
        let lastSnapshot = '';
        let stableCount = 0;
        let attempts = 0;

        while (attempts < config.maxAttempts) {
            // Wait for network and animations
            await page.waitForLoadState('networkidle');
            await page.evaluate(() => document.fonts.ready);
            
            // Get current DOM snapshot
            const snapshot = await page.evaluate(() => document.documentElement.outerHTML);
            
            if (snapshot === lastSnapshot) {
                stableCount++;
                if (stableCount >= 2) break;
            } else {
                stableCount = 0;
            }
            
            lastSnapshot = snapshot;
            attempts++;
            await page.waitForTimeout(config.stabilityThreshold);
        }
    }

    /**
     * Registers a new session with our helper to track and prevent contention
     * @param {string} sessionId - BrowserStack session ID
     */
    registerSession(sessionId) {
        // If we already have an active session and it's different, close the previous session
        if (this.activeSessionId && this.activeSessionId !== sessionId) {
            console.warn(`Another session is active (${this.activeSessionId}). Will be replaced with ${sessionId}.`);
            this.closeSession(this.activeSessionId)
                .catch(err => console.error('Error closing previous session:', err));
        }
        
        this.activeSessionId = sessionId;
        console.log(`Registered session: ${sessionId}`);
    }
    
    /**
     * Terminates a BrowserStack session
     * @param {string} sessionId - BrowserStack session ID
     * @returns {Promise<boolean>} - Success or failure
     */
    async closeSession(sessionId) {
        if (!sessionId) return false;
        
        try {
            const response = await this._fetch(
                `${this.browserStackUrl}sessions/${sessionId}.json`,
                {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Basic ${this.auth}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        status: 'completed'
                    })
                }
            );
            
            const result = await response.json();
            return result.status === 'completed';
        } catch (error) {
            console.error('Error closing BrowserStack session:', error);
            return false;
        } finally {
            if (sessionId === this.activeSessionId) {
                this.activeSessionId = null;
            }
        }
    }

    /**
     * Takes a screenshot using VisualAnalysisHelper
     * @param {import('@playwright/test').Page} page - Playwright page object
     * @param {string} name - Screenshot name
     * @param {Object} options - Screenshot options
     * @returns {Promise<Object>} - Screenshot metadata including Cloudinary URL
     */
    async takeScreenshot(page, name, options = {}) {
        const { VisualAnalysisHelper } = require('../visual-analisys-helper');
        
        // Add platform info to options
        const enhancedOptions = {
            ...options,
            cdpMeta: {
                _requestedPlatform: process.env.PLATFORM || 'unknown',
                _actualPlatform: process.env.PLATFORM || 'unknown',
                _requestedDeviceType: this.isAndroidPlatform ? 'Android Device' : null,
                browserStackSession: this.activeSessionId
            }
        };
        
        // Use the correct screenshot method
        return await VisualAnalysisHelper.captureAndUploadScreenshot(
            page, 
            options.testName || 'unknown', 
            name, 
            enhancedOptions
        );
    }

    /**
     * Downloads BrowserStack artifacts
     * @param {string} sessionId - BrowserStack session ID
     * @returns {Promise<void>}
     */
    async downloadArtifacts(sessionId) {
        if (!process.env.BROWSERSTACK_USERNAME || !process.env.BROWSERSTACK_ACCESS_KEY) {
            console.warn('BrowserStack credentials not found. Skipping artifact download.');
            return;
        }

        const artifactsDir = path.join(process.cwd(), 'test-results', 'browserstack', sessionId);
        await fs.mkdir(artifactsDir, { recursive: true });

        try {
            // Get artifacts from BrowserStack
            const artifacts = await this.getSessionArtifacts(sessionId);
            if (!artifacts) {
                console.warn('No artifacts found for session:', sessionId);
                return;
            }

            // Download screenshots if available
            if (artifacts.screenshotUrl) {
                const screenshotsResponse = await this._fetch(artifacts.screenshotUrl, {
                    headers: { Authorization: `Basic ${this.auth}` }
                });
                
                if (screenshotsResponse.ok) {
                    // Save screenshots data
                    const screenshotsData = await screenshotsResponse.json();
                    await fs.writeFile(
                        path.join(artifactsDir, 'screenshots-metadata.json'),
                        JSON.stringify(screenshotsData, null, 2)
                    );
                    
                    // Download individual screenshots
                    if (screenshotsData.screenshots && Array.isArray(screenshotsData.screenshots)) {
                        const screenshotsDir = path.join(artifactsDir, 'screenshots');
                        await fs.mkdir(screenshotsDir, { recursive: true });
                        
                        for (const [index, screenshot] of screenshotsData.screenshots.entries()) {
                            if (screenshot.image_url) {
                                const imgResponse = await this._fetch(screenshot.image_url);
                                if (imgResponse.ok) {
                                    const buffer = await imgResponse.buffer();
                                    await fs.writeFile(
                                        path.join(screenshotsDir, `screenshot_${index}.png`),
                                        buffer
                                    );
                                }
                            }
                        }
                    }
                }
            }

            // Download video if available
            if (artifacts.videoUrl) {
                const videoResponse = await this._fetch(artifacts.videoUrl, {
                    headers: { Authorization: `Basic ${this.auth}` }
                });
                
                if (videoResponse.ok) {
                    const buffer = await videoResponse.buffer();
                    await fs.writeFile(
                        path.join(artifactsDir, 'session-video.mp4'),
                        buffer
                    );
                }
            }

            // Download logs if available
            if (artifacts.logsUrl) {
                const logs = await this.getSessionLogs(sessionId);
                await fs.writeFile(
                    path.join(artifactsDir, 'session-logs.txt'),
                    logs
                );
            }

            console.log(`BrowserStack artifacts downloaded to: ${artifactsDir}`);
            return artifactsDir;
        } catch (error) {
            console.error('Error downloading BrowserStack artifacts:', error);
            return null;
        }
    }

    /**
     * Updates test status on BrowserStack
     * @param {string} sessionId - BrowserStack session ID
     * @param {Object} status - Test status details
     * @returns {Promise<void>}
     */
    async updateTestStatus(sessionId, status) {
        if (!process.env.BROWSERSTACK_USERNAME || !process.env.BROWSERSTACK_ACCESS_KEY) {
            console.warn('BrowserStack credentials not found. Skipping status update.');
            return;
        }

        try {
            const response = await this._fetch(
                `${this.browserStackUrl}sessions/${sessionId}.json`,
                {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Basic ${this.auth}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        status: status.passed ? 'passed' : 'failed',
                        reason: status.reason || ''
                    })
                }
            );
            
            if (response.ok) {
                console.log(`BrowserStack status updated for session ${sessionId}`);
            } else {
                console.warn(`Failed to update BrowserStack status: ${response.status}`);
            }
        } catch (error) {
            console.error('Error updating BrowserStack status:', error);
        }
    }

    /**
     * Gets session artifacts URLs from BrowserStack
     * @param {string} sessionId - BrowserStack session ID
     * @returns {Promise<Object>} - Object containing artifact URLs
     */
    async getSessionArtifacts(sessionId) {
        try {
            const response = await this._fetch(
                `${this.browserStackUrl}sessions/${sessionId}.json`,
                { headers: { Authorization: `Basic ${this.auth}` } }
            );
            const session = await response.json();

            return {
                screenshotUrl: session.automation_session.browser_url + '/screenshots',
                videoUrl: session.automation_session.video_url,
                logsUrl: session.automation_session.logs
            };
        } catch (error) {
            console.error('Error fetching BrowserStack artifacts:', error);
            return null;
        }
    }

    /**
     * Gets latest screenshot URL from session logs
     * @param {string} sessionId - BrowserStack session ID
     * @returns {Promise<string|null>} - URL of the latest screenshot
     */
    async getLatestScreenshotUrl(sessionId) {
        try {
            const logs = await this.getSessionLogs(sessionId);
            const matches = logs.match(/screenshot.*https:\/\/[^\s]*.png/g);
            if (matches && matches.length > 0) {
                const lastScreenshot = matches[matches.length - 1];
                return lastScreenshot.match(/https:\/\/[^\s]*.png/)[0];
            }
        } catch (error) {
            console.error('Error getting latest screenshot URL:', error);
        }
        return null;
    }

    /**
     * Gets session logs
     * @param {string} sessionId - BrowserStack session ID
     * @returns {Promise<string>} - Session logs
     */
    async getSessionLogs(sessionId) {
        const response = await this._fetch(
            `${this.browserStackUrl}sessions/${sessionId}/logs`,
            { headers: { Authorization: `Basic ${this.auth}` } }
        );
        return response.text();
    }

    /**
     * Safely initializes Android testing context with browser
     * @param {import('@playwright/test').AndroidDevice} androidDevice 
     * @param {Object} options 
     * @returns {Promise<{context: import('@playwright/test').BrowserContext, page: import('@playwright/test').Page}>}
     */
    async safeAndroidBrowserInit(androidDevice, options = {}) {
        const defaultOptions = {
            maxRetries: 5,
            retryInterval: 5000,
            timeout: process.env.TEST_TIMEOUT ? parseInt(process.env.TEST_TIMEOUT) : 300000
        };
        
        const config = { ...defaultOptions, ...options };
        let lastError = null;
        
        console.log(`Android device setup initiated with timeout: ${config.timeout}ms and ${config.maxRetries} retries`);
        
        // Always attempt to close any existing sessions first
        try {
            await androidDevice.close();
            console.log('Successfully closed any existing Android sessions');
            await new Promise(resolve => setTimeout(resolve, 3000)); // Wait longer after closing
        } catch (e) {
            console.log('No existing Android sessions to close or error closing:', e.message);
        }
        
        for (let attempt = 1; attempt <= config.maxRetries; attempt++) {
            try {
                console.log(`Attempt ${attempt}/${config.maxRetries} to launch browser on Android device`);
                
                // Use a progressive timeout approach
                const attemptTimeout = Math.min(config.timeout, config.timeout * (attempt / config.maxRetries) + 30000);
                console.log(`Using timeout of ${attemptTimeout}ms for this attempt`);
                
                // Launch browser with explicit timeout setting
                const context = await androidDevice.launchBrowser({
                    ...options,
                    timeout: attemptTimeout
                });
                
                console.log('Browser launched successfully, creating new page');
                
                // Create page with explicit timeout
                const page = await Promise.race([
                    context.newPage(),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('Page creation timed out')), 60000)
                    )
                ]);
                
                // Minimal wait for page to be ready
                await page.waitForLoadState('domcontentloaded', { timeout: 30000 }).catch(e => {
                    console.log('Initial page load warning (continuing anyway):', e.message);
                });
                
                console.log('Successfully launched browser and created page on Android device');
                return { context, page };
            } catch (error) {
                lastError = error;
                console.warn(`Failed to launch browser on attempt ${attempt}: ${error.message}`);
                
                // Take more drastic cleanup measures on later attempts
                if (attempt > 1) {
                    try {
                        console.log('Performing additional cleanup after failed attempt');
                        // Force garbage collection if possible (Node.js only)
                        if (global.gc) {
                            global.gc();
                        }
                    } catch (e) {
                        // Ignore cleanup errors
                    }
                }
                
                // Wait longer before retrying on later attempts
                const waitTime = config.retryInterval * attempt;
                console.log(`Waiting ${waitTime}ms before next attempt`);
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }
        }
        
        throw new Error(`Failed to launch browser after ${config.maxRetries} attempts: ${lastError.message}`);
    }
}

module.exports = { BrowserStackHelper };