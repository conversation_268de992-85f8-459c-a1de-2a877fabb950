#!/bin/bash
set -e

# Get current values
TIMESTAMP=$(date +"%Y%m%dT%H%M%S")
COMMIT_SHA=$(git rev-parse HEAD 2>/dev/null || echo "manual-test-$(date +%s)")

# Set default values
ENV=${1:-stage}
BROWSER=${2:-chromium}
DEVICE=${3:-desktop}
MAX_WAIT_TIME=${4:-1800} # Default maximum wait time is 30 minutes (1800 seconds)

# GitLab variables
PROJECT_ID=66834057
TOKEN=glptt-f732f3c8ad6b9357e8b07770215006239ac29e5f
BRANCH=main
API_URL="https://gitlab.com/api/v4/projects/$PROJECT_ID"

# Create a results directory if it doesn't exist
RESULTS_DIR="pipeline-results/${TIMESTAMP}"
mkdir -p "$RESULTS_DIR"

# Function to check pipeline status
check_pipeline_status() {
    local pipeline_id=$1
    curl --silent --header "PRIVATE-TOKEN: $TOKEN" "${API_URL}/pipelines/${pipeline_id}" | jq -r '.status'
}

# Function to wait for pipeline completion
wait_for_pipeline() {
    local pipeline_id=$1
    local wait_time=0
    local interval=30 # Check every 30 seconds
    local status=""

    echo "Waiting for pipeline #${pipeline_id} to complete..."
    
    while [ $wait_time -lt $MAX_WAIT_TIME ]; do
        status=$(check_pipeline_status $pipeline_id)
        
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Pipeline status: $status"
        
        if [[ "$status" == "success" || "$status" == "failed" || "$status" == "canceled" ]]; then
            echo "Pipeline #${pipeline_id} finished with status: $status"
            return 0
        elif [[ "$status" == "running" || "$status" == "pending" || "$status" == "created" ]]; then
            # Still in progress, continue waiting
            sleep $interval
            wait_time=$((wait_time + interval))
        else
            echo "Pipeline has unexpected status: $status"
            return 1
        fi
    done
    
    echo "Timeout waiting for pipeline #${pipeline_id} to complete. Last status: $status"
    return 1
}

# Function to download job artifacts
download_job_artifacts() {
    local pipeline_id=$1
    
    # Get all jobs for the pipeline
    echo "Retrieving jobs for pipeline #${pipeline_id}..."
    local jobs=$(curl --silent --header "PRIVATE-TOKEN: $TOKEN" "${API_URL}/pipelines/${pipeline_id}/jobs")
    
    # Extract job IDs and names
    local job_ids=($(echo "$jobs" | jq -r '.[] | select(.artifacts_file != null) | .id'))
    local job_names=($(echo "$jobs" | jq -r '.[] | select(.artifacts_file != null) | .name'))
    
    if [ ${#job_ids[@]} -eq 0 ]; then
        echo "No artifacts found for pipeline #${pipeline_id}"
        return 1
    fi
    
    echo "Found ${#job_ids[@]} jobs with artifacts."
    
    # Download artifacts for each job
    for i in "${!job_ids[@]}"; do
        local job_id=${job_ids[$i]}
        local job_name=${job_names[$i]}
        local artifact_file="${RESULTS_DIR}/${job_name}-artifacts.zip"
        
        echo "Downloading artifacts for job '${job_name}' (#${job_id})..."
        curl --silent --header "PRIVATE-TOKEN: $TOKEN" "${API_URL}/jobs/${job_id}/artifacts" --output "$artifact_file"
        
        if [ -f "$artifact_file" ] && [ -s "$artifact_file" ]; then
            echo "Artifacts saved to $artifact_file"
            
            # Extract artifacts
            local extract_dir="${RESULTS_DIR}/${job_name}"
            mkdir -p "$extract_dir"
            unzip -q "$artifact_file" -d "$extract_dir"
            echo "Extracted artifacts to $extract_dir"
        else
            echo "Failed to download artifacts for job #${job_id}"
        fi
    done
}

# Function to display pipeline details
get_pipeline_url() {
    local pipeline_id=$1
    echo "https://gitlab.com/projects/${PROJECT_ID}/pipelines/${pipeline_id}"
}

# Main execution
echo "Triggering pipeline for $ENV environment with $BROWSER on $DEVICE"
echo "Results will be saved to: ${RESULTS_DIR}"

# Trigger the pipeline and capture the response
RESPONSE=$(curl -s -X POST \
  -F token=$TOKEN \
  -F ref=$BRANCH \
  -F "variables[TEST_ENV]=$ENV" \
  -F "variables[BROWSER]=$BROWSER" \
  -F "variables[DEVICE]=$DEVICE" \
  -F "variables[COMMIT_SHA]=$COMMIT_SHA" \
  -F "variables[TIMESTAMP]=$TIMESTAMP" \
  "${API_URL}/trigger/pipeline")

# Check if the request was successful
if [[ "$RESPONSE" == *"\"id\""* ]]; then
    # Extract pipeline ID
    PIPELINE_ID=$(echo "$RESPONSE" | grep -o '"id":[0-9]*' | cut -d':' -f2)
    
    if [ -z "$PIPELINE_ID" ]; then
        echo "Failed to extract pipeline ID from response:"
        echo "$RESPONSE"
        exit 1
    fi
    
    echo "Pipeline triggered successfully with ID: $PIPELINE_ID"
    echo "Pipeline URL: $(get_pipeline_url $PIPELINE_ID)"
    
    # Wait for pipeline to complete
    if wait_for_pipeline $PIPELINE_ID; then
        # Download artifacts
        download_job_artifacts $PIPELINE_ID
        
        # Get final status and exit with appropriate code
        FINAL_STATUS=$(check_pipeline_status $PIPELINE_ID)
        
        echo "Test results saved to: ${RESULTS_DIR}"
        echo "===== Pipeline Summary ====="
        echo "Pipeline ID: $PIPELINE_ID"
        echo "URL: $(get_pipeline_url $PIPELINE_ID)"
        echo "Environment: $ENV"
        echo "Browser/Device: $BROWSER / $DEVICE"
        echo "Status: $FINAL_STATUS"
        echo "Results location: ${RESULTS_DIR}"
        
        if [ "$FINAL_STATUS" == "success" ]; then
            exit 0
        else
            exit 1
        fi
    else
        echo "Failed to monitor pipeline. Check pipeline status manually at:"
        echo "$(get_pipeline_url $PIPELINE_ID)"
        exit 1
    fi
else
    echo "Failed to trigger pipeline. Response:"
    echo "$RESPONSE"
    exit 1
fi
