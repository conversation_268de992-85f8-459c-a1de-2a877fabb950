/**
 * @fileoverview Helper for capturing screenshots and uploading to Cloudinary
 */

const fs = require('fs').promises;
const path = require('path');
const cloudinary = require('cloudinary').v2;

// Cloudinary configuration from environment variables
cloudinary.config({
    cloud_name: 'dovykzbn9',
    api_key: '819558736635999',
    api_secret: process.env.CLOUDINARY_API_SECRET // Ensure this is set in .env
});

class VisualAnalysisHelper {
    /**
     * Captures screenshot, uploads to Cloudinary, and returns URL
     * @param {Page} page - Playwright page object
     * @param {string} testName - Name of the test
     * @param {string} screenshotName - Name of the screenshot
     * @param {Object} options - Screenshot options
     * @returns {Promise<Object>} Screenshot metadata including Cloudinary URL
     */
    static async captureAndUploadScreenshot(page, testName, screenshotName, options = {}) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        
        // Get platform info from options or environment variables
        const platform = options.platform || process.env.PLATFORM || 'desktop';
        
        // Use CDP metadata if available
        const cdpMeta = options.cdpMeta || {};
        const requestedPlatform = cdpMeta._requestedPlatform || platform;
        const requestedDeviceType = cdpMeta._requestedDeviceType || null;
        
        // Determine device info based on platform
        let deviceInfo = {
            width: page.viewportSize()?.width || 'full',
            height: page.viewportSize()?.height || 'full'
        };
        
        if (requestedPlatform.includes('android')) {
            deviceInfo.deviceType = 'android';
            deviceInfo.browserName = 'chrome';
            deviceInfo.deviceName = requestedDeviceType || 'Samsung Galaxy S23';
        } else if (requestedPlatform.includes('ios')) {
            deviceInfo.deviceType = 'ios';
            deviceInfo.browserName = 'safari';
            deviceInfo.deviceName = requestedDeviceType || 'iPhone 14';
        } else {
            deviceInfo.deviceType = 'desktop';
            deviceInfo.browserName = 'chrome';
            deviceInfo.deviceName = requestedDeviceType || 'Windows';
        }

        // Add platform-specific tags for clarity
        deviceInfo.requestedPlatform = requestedPlatform;
        deviceInfo.actualPlatform = cdpMeta._actualPlatform || platform;
        
        // Create directory for screenshots
        const screenshotDir = path.join(process.cwd(), 'test-results', testName);
        await fs.mkdir(screenshotDir, { recursive: true });
        
        // Generate timestamp-based directory for this test run
        const timestampDir = path.join(screenshotDir, timestamp);
        await fs.mkdir(timestampDir, { recursive: true });
        
        // Create screenshot filename with platform and device info
        // Ensure we don't add .png if the name already includes it
        const baseFileName = screenshotName.endsWith('.png') 
            ? screenshotName.substring(0, screenshotName.length - 4) 
            : screenshotName;
        
        // Use requested platform for the filename to maintain consistency
        const fileName = `${baseFileName}_${requestedPlatform}_${deviceInfo.deviceName}_${deviceInfo.width}x${deviceInfo.height}.png`;
        const filePath = path.join(timestampDir, fileName);

        const defaultOptions = {
            fullPage: true,
            timeout: 30000,
            ...options
        };

        await page.screenshot({
            path: filePath,
            ...defaultOptions
        });

        // Upload screenshot to Cloudinary
        const uploadResult = await cloudinary.uploader.upload(filePath, {
            public_id: `${testName}/${timestamp}/${fileName}`
        });

        // Write metadata to a JSON file alongside the screenshot
        const metadata = {
            timestamp,
            screenshotPath: filePath,
            cloudinaryUrl: uploadResult.secure_url,
            // Ensure device info follows consistent naming
            requestedPlatform,
            actualPlatform: deviceInfo.actualPlatform,
            device: {
                type: deviceInfo.deviceType,
                width: deviceInfo.width,
                height: deviceInfo.height,
                browser: deviceInfo.browserName,
                name: deviceInfo.deviceName
            },
            browserstack: {
                platform: requestedPlatform,
                deviceType: deviceInfo.deviceType,
                os: requestedPlatform.includes('android') ? 'Android' : 
                     requestedPlatform.includes('ios') ? 'iOS' : 
                     requestedPlatform.includes('windows') ? 'Windows' : 'Unknown',
                browserName: deviceInfo.browserName
            },
            cdpMetadata: cdpMeta
        };

        const metadataFilePath = filePath.replace('.png', '-metadata.json');
        await fs.writeFile(metadataFilePath, JSON.stringify(metadata, null, 2));
        
        console.log(`Screenshot captured and uploaded to Cloudinary: ${metadata.cloudinaryUrl}`);
        console.log(`Device used: ${deviceInfo.deviceName} (${metadata.requestedPlatform})`);
        
        return metadata;
    }
}

module.exports = { VisualAnalysisHelper };
