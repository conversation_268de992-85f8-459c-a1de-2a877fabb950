/**
 * @fileoverview Aeons defect verification tests
 * @tags @regression @defects
 */
const { test, expect } = require('../fixtures/enhanced-unified-fixture');
const { ProductPage } = require('../../src/pages/shop/ProductPage');
const { LoginPage } = require('../../src/pages/account/LoginPage');
const { DashboardPage } = require('../../src/pages/account/DashboardPage');
const { HomePage } = require('../../src/pages/general/HomePage');
const { AnalysisOrchestrator } = require('../../src/utils/gemini/service/AnalysisOrchestrator');
const { GeminiService } = require('../../src/utils/gemini/service/GeminiService');
const { VisualAnalysisHelper } = require('../../src/utils/visual-analisys-helper');
const fs = require('fs').promises;
const path = require('path');
const yaml = require('js-yaml');

test.describe('Aeons Website', () => {
    test.describe('Defect Verification Tests @defects', () => {
        /**
         * @description Verifies that the "6 Powerful Ingredients" thumbnail image for Sunset Soothe
         * product maintains consistent background styling (not white).
         * @defect Asana Task #****************
         * @summary The selected product detail image for "Sunset Soothe" displays a white background
         * that is inconsistent with the styling of other similar product images on the same page.
         */
        test.skip('Verify Sunset Product Image Background Styling @visual', async ({ page, bsHelper, testDataManager }) => {
            const productPage = new ProductPage(page);
            const analysisOrchestrator = new AnalysisOrchestrator({
                includeVisualAnalysis: true
            });

            await test.step('Navigate to Sunset Soothe product page', async () => {
                // Use centralized URL management
                const baseUrl = testDataManager.getBaseUrl();
                const productUrl = `${baseUrl}/products/aeons-sunset-soothe`;

                await page.goto(productUrl);
                await page.waitForLoadState('networkidle');

                // Take screenshot after page loads
                await bsHelper.takeScreenshot(page, 'sunset-soothe-product-page');
            });

            await test.step('Check 6 Ingredients thumbnail styling', async () => {
                // Wait for product page to load
                await page.waitForLoadState('networkidle');

                // Find the "6 Powerful Ingredients" thumbnail - updated selector
                const thumbnailSelector = '.product-gallery__thumbnails img, .product__thumbnails img';

                // Wait for any thumbnail to be visible first
                await page.waitForSelector(thumbnailSelector, { state: 'visible' });

                // Then locate the specific thumbnail containing the ingredients text
                const ingredientsThumbnail = await page.locator('img[alt*="Ingredients"], img[src*="ingredients"], img[data-title*="Ingredients"]').first();

                // If found, click on it
                if (await ingredientsThumbnail.count() > 0) {
                    await ingredientsThumbnail.click();
                } else {
                    console.log('Ingredients thumbnail not found, clicking the first available thumbnail');
                    // Fallback to clicking the first thumbnail
                    await page.locator(thumbnailSelector).first().click();
                }

                // Wait for the main image to update
                await page.waitForTimeout(1000);

                // Wait for visual stability before capturing screenshot
                await bsHelper.waitForVisualStability(page);

                // Ensure the main image is visible
                const mainImageSelector = '.product__media img[alt*="6 Powerful Ingredients"]';
                await page.waitForSelector(mainImageSelector, { state: 'visible' });

                // Take screenshot and upload to Cloudinary
                const metadata = await VisualAnalysisHelper.captureAndUploadScreenshot(
                    page,
                    'sunset-soothe-defect-test',
                    'ingredients-background',
                    {
                        fullPage: false,
                        platform: process.env.PLATFORM || 'desktop'
                    }
                );

                console.log('Screenshot uploaded to Cloudinary:', metadata.cloudinaryUrl);

                // Use Gemini AI to analyze the image background via AnalysisOrchestrator
                const analysisPrompt = "Analyze this product image and determine if the background is white or has a dark/themed background consistent with the product's branding. The image should NOT have a plain white background. Focus on the background color and consistency with the brand styling. Clearly state whether the background is white or not.";

                const analysisResults = await analysisOrchestrator.analyze(
                    {
                        title: 'Sunset Soothe Ingredients Image Analysis',
                        status: 'passed'
                    },
                    {
                        screenshots: [metadata.cloudinaryUrl],
                        customPrompts: {
                            visual: analysisPrompt
                        }
                    }
                );

                // Save analysis results
                const resultsDir = path.join(process.cwd(), 'test-results', 'gemini-analysis');
                await fs.mkdir(resultsDir, { recursive: true });

                const resultsPath = path.join(resultsDir, 'sunset-soothe-analysis.json');
                await fs.writeFile(resultsPath, JSON.stringify(analysisResults, null, 2));

                console.log('Analysis results saved to:', resultsPath);

                // Extract the visual analysis text
                let visualAnalysisText = '';
                if (analysisResults.visual && analysisResults.visual.length > 0) {
                    visualAnalysisText = analysisResults.visual[0].analysis;
                }

                // Check if the analysis confirms a non-white background
                const hasNonWhiteBackground =
                    visualAnalysisText.toLowerCase().includes('not white') ||
                    visualAnalysisText.toLowerCase().includes('dark background') ||
                    visualAnalysisText.toLowerCase().includes('themed background') ||
                    !visualAnalysisText.toLowerCase().includes('white background');

                console.log('Gemini Analysis:', visualAnalysisText);
                console.log('Has non-white background:', hasNonWhiteBackground);

                expect(hasNonWhiteBackground,
                    'The product image should have a non-white background consistent with brand styling'
                ).toBeTruthy();
            });
        });

        /**
         * @description Verifies that the mobile layout for product thumbnails has been fixed
         * after resolving an issue with overlapping elements on smaller screens.
         * @defect Asana Task #1208830579833889
         * @summary The product thumbnail section displays overlapping or improperly arranged images
         * in the product gallery section for "Ancient Roots Olive Oil" on mobile devices.
         */
        test.skip('Verify Mobile Image Thumbnail Layout @mobile', async ({ page, bsHelper, testDataManager }) => {
            const productPage = new ProductPage(page);
            const analysisOrchestrator = new AnalysisOrchestrator({
                includeVisualAnalysis: true
            });

            await test.step('Setup mobile viewport and navigate', async () => {
                // Set viewport to mobile size - BrowserStack will handle real device dimensions
                if (!process.env.BROWSERSTACK_REAL_DEVICE) {
                    await page.setViewportSize({ width: 375, height: 667 });
                }

                // Navigate to product page with retry mechanism
                const maxRetries = 3;
                let retryCount = 0;
                let pageLoaded = false;

                // Get product URL from centralized data
                const product = testDataManager.getProduct('ancient_roots_olive_oil');
                const baseUrl = testDataManager.getBaseUrl();
                const productUrl = `${baseUrl}${product.urlPath}`;

                while (retryCount < maxRetries && !pageLoaded) {
                    try {
                        console.log(`Attempt ${retryCount + 1} to load product page: ${productUrl}`);
                        await page.goto(productUrl, {
                            waitUntil: 'networkidle',
                            timeout: 30000
                        });
                        pageLoaded = true;
                        console.log('Page loaded successfully!');
                    } catch (error) {
                        console.error(`Error loading page (attempt ${retryCount + 1}):`, error.message);
                        retryCount++;
                        if (retryCount >= maxRetries) {
                            throw new Error(`Failed to load page after ${maxRetries} attempts`);
                        }
                        // Wait before retry
                        await page.waitForTimeout(2000);
                    }
                }

                // Take screenshot after page loads
                await bsHelper.takeScreenshot(page, 'mobile-product-page-loaded');
            });

            await test.step('Capture and analyze mobile layout', async () => {
                // Wait for visual stability before capturing screenshot
                await bsHelper.waitForVisualStability(page);

                // Upload screenshot to Cloudinary
                const metadata = await VisualAnalysisHelper.captureAndUploadScreenshot(
                    page,
                    'mobile-thumbnail-layout',
                    'gallery-section',
                    {
                        fullPage: false,
                        platform: process.env.PLATFORM || 'mobile'
                    }
                );

                console.log('Screenshot uploaded to Cloudinary:', metadata.cloudinaryUrl);

                // Use Gemini AI to analyze the layout
                const analysisPrompt = "Analyze this mobile product gallery section and determine if the thumbnail images are properly arranged without overlapping. Focus on the thumbnail gallery section, check for overlapping elements, proper spacing, and clean layout. Each thumbnail should be clearly visible and properly aligned.";

                const analysisResults = await analysisOrchestrator.analyze(
                    {
                        title: 'Mobile Image Gallery Layout Analysis',
                        status: 'analyzing'
                    },
                    {
                        screenshots: [metadata.cloudinaryUrl],
                        customPrompts: {
                            visual: analysisPrompt
                        }
                    }
                );

                // Save analysis results
                const resultsDir = path.join(process.cwd(), 'test-results', 'gemini-analysis');
                await fs.mkdir(resultsDir, { recursive: true });

                const resultsPath = path.join(resultsDir, 'mobile-layout-analysis.json');
                await fs.writeFile(resultsPath, JSON.stringify(analysisResults, null, 2));

                console.log('Analysis results saved to:', resultsPath);

                // Extract the visual analysis text
                let visualAnalysisText = '';
                if (analysisResults.visual && analysisResults.visual.length > 0) {
                    visualAnalysisText = analysisResults.visual[0].analysis;
                }

                // Check if the analysis confirms proper layout
                const hasProperLayout =
                    visualAnalysisText.toLowerCase().includes('properly arranged') ||
                    visualAnalysisText.toLowerCase().includes('no overlapping') ||
                    visualAnalysisText.toLowerCase().includes('clean layout') ||
                    !visualAnalysisText.toLowerCase().includes('overlapping elements');

                console.log('Gemini Analysis:', visualAnalysisText);
                console.log('Has proper layout:', hasProperLayout);

                expect(hasProperLayout,
                    'The mobile product gallery should have properly arranged thumbnails without overlapping'
                ).toBeTruthy();

                // Take additional screenshot after analysis
                await bsHelper.takeScreenshot(page, 'mobile-layout-verification-complete');
            });
        });

        /**
         * @description Verifies that the user account logout functionality works correctly
         * after fixing a defect where the logout button was not visible on the account page.
         * @defect Asana Task #****************
         * @summary Users were unable to logout from their account due to the logout button
         * being missing or not visible on the account page after a recent site update.
         */
        test('Verify Account Logout Functionality', async ({ page, bsHelper, testDataManager }) => {
            await test.step('Log in to user account', async () => {
                // Navigate to login page using centralized URL management
                const baseUrl = testDataManager.getBaseUrl();
                const loginUrl = `${baseUrl}/account/login`;

                await page.goto(loginUrl);
                await page.waitForLoadState('networkidle');

                // Take screenshot of login page
                await bsHelper.takeScreenshot(page, 'account-login-page');

                // Get test user credentials from centralized data
                const testUser = testDataManager.getUser('default');

                // Fill login form with test credentials
                await page.fill('#CustomerEmail', testUser.email);
                await page.fill('#CustomerPassword', 'testpassword123'); // Note: Password should be in environment variables

                // Take screenshot before submitting
                await bsHelper.takeScreenshot(page, 'login-form-filled');

                // Submit login form
                await page.click('input[type="submit"]');

                // Wait for account page to load
                await page.waitForNavigation({ waitUntil: 'networkidle' });

                // Take screenshot of account page
                await bsHelper.takeScreenshot(page, 'account-page-loaded');

                // Verify logged in state
                const accountTitle = page.locator('h1');
                await expect(accountTitle).toContainText('My Account');
            });

            await test.step('Verify logout button visibility', async () => {
                // Check if logout button exists and is visible
                const logoutButton = page.locator('a[href="/account/logout"]');

                // Wait for visual stability
                await bsHelper.waitForVisualStability(page);

                // Take screenshot showing logout button
                await bsHelper.takeScreenshot(page, 'logout-button-visibility');

                // Verify button exists and is visible
                await expect(logoutButton).toBeVisible();
            });

            await test.step('Perform logout and verify', async () => {
                // Click logout button
                await page.click('a[href="/account/logout"]');

                // Wait for logout to complete
                await page.waitForNavigation({ waitUntil: 'networkidle' });

                // Take screenshot after logout
                await bsHelper.takeScreenshot(page, 'after-logout');

                // Verify logged out state (login button should be visible)
                const loginButton = page.locator('a[href="/account/login"]');
                await expect(loginButton).toBeVisible();
            });
        });
    });
});
