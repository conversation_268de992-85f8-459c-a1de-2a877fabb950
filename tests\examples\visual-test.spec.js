/**
 * @fileoverview Example test demonstrating proper use of TestDataManager with YAML-based test data
 */

const { test, expect } = require('@playwright/test');
const TestDataManager = require('../../tests/data/test-data-manager');
const { validateProductData } = require('../utils/data-validator');

test.describe('Product Tests', () => {
    let testDataManager;
    let testData;
    let baseUrl;

    test.beforeEach(async ({ page }) => {
        // Initialize test data manager
        testDataManager = new TestDataManager();
        testDataManager.initialize('default', 'aeons', process.env.TEST_ENV || 'stage');
        
        // Get test data and configuration
        testData = testDataManager.getTestData();
        const config = testDataManager.getBrandConfig();
        baseUrl = config.url;
        
        // Validate product data
        validateProductData(testData.product);
        
        // Log test data for debugging
        console.log('Running test with:', {
            brand: testDataManager.brand,
            env: testDataManager.environment,
            product: testData.product.name
        });
    });

    test('Product page verification @smoke', async ({ page }) => {
        // 1. Navigate to product page using data from YAML
        const productUrl = `${baseUrl}/${testData.product.url_path}`;
        await page.goto(productUrl);

        // 2. Verify product title
        const title = page.locator('.product-title');
        await expect(title).toHaveText(testData.product.name);

        // 3. Select different variant if available
        if (testData.product.variants && testData.product.variants.length > 0) {
            const variantName = testData.product.variants[0].name;
            await page.selectOption('.variant-selector', variantName);
            
            // 4. Verify updated price
            const price = page.locator('.product-price');
            await expect(price).toHaveText(testData.product.variants[0].price);
        }
    });

    test('Responsive design verification @smoke', async ({ page }) => {
        // 1. Navigate to home page
        await page.goto('/');

        // 2. Test at different viewport sizes
        const viewports = [
            { width: 1920, height: 1080, name: 'desktop' },
            { width: 1024, height: 768, name: 'tablet' },
            { width: 375, height: 812, name: 'mobile' }
        ];

        for (const viewport of viewports) {
            await page.setViewportSize(viewport);
            await page.waitForLoadState('networkidle');

            // Verify responsive elements
            if (viewport.width < 768) {
                await expect(page.locator('.mobile-menu-button')).toBeVisible();
                await expect(page.locator('.desktop-menu')).toBeHidden();
            } else {
                await expect(page.locator('.mobile-menu-button')).toBeHidden();
                await expect(page.locator('.desktop-menu')).toBeVisible();
            }

            // Product grid layout
            const productGrid = page.locator('.product-grid');
            const gridComputedStyle = await productGrid.evaluate((element) => {
                const style = window.getComputedStyle(element);
                return {
                    gridTemplateColumns: style.gridTemplateColumns,
                    gap: style.gap
                };
            });

            // Verify grid columns based on viewport
            if (viewport.width >= 1024) {
                expect(gridComputedStyle.gridTemplateColumns).toContain('repeat(4,');
            } else if (viewport.width >= 768) {
                expect(gridComputedStyle.gridTemplateColumns).toContain('repeat(3,');
            } else {
                expect(gridComputedStyle.gridTemplateColumns).toContain('repeat(2,');
            }
        }
    });

    test('Form validation test @smoke', async ({ page }) => {
        // 1. Navigate to checkout
        await page.goto('/checkout');

        // 2. Submit form without required fields
        await page.click('button[type="submit"]');

        // 3. Verify error messages
        const errorMessages = page.locator('.error-message');
        await expect(errorMessages).toHaveCount(3); // Assuming 3 required fields

        // 4. Fill one field correctly
        await page.fill('input[name="email"]', '<EMAIL>');
        await page.click('button[type="submit"]');

        // 5. Verify updated error state
        await expect(errorMessages).toHaveCount(2); // Now only 2 errors
    });
}); 