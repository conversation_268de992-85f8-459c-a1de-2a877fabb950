My testing framework is [Playwright](https://playwright.dev/) with [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>](https://www.browserstack.com/).
I want to use Browserstack  for cross-browser and cross-platform testing on real devices.
My Browserstack account allow me to use only one thread for testing, so i cannot run tests in parallel.
I want to use all available Browserstack SDK (for playwright) features for session management, browser launch, and test execution.
I want to take screenshots during test execution, upload them to cloudinary and analyse them with Gemini AI.
I want to use Gemini AI for intelligent visual analysis and analyse test results.
I don't want to use Browserstack with <PERSON> for visual testing.