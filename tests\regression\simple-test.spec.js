/**
 * @fileoverview Simple test to verify the testing process
 * @tags @simple @test @smoke
 */
const { test, expect } = require('../fixtures/unified-fixture');

test('Simple page navigation test', async ({ page }) => {
  // Navigate to the base URL (configured in playwright.config.js)
  await test.step('Navigate to base URL', async () => {
    await page.goto('/');

    // Wait for the page to load
    await page.waitForLoadState('networkidle');

    // Verify that the page has loaded by checking for a common element
    const title = await page.title();
    console.log(`Page title: ${title}`);

    // Verify that the page has a title
    expect(title).toBeTruthy();

    // Take a screenshot for reference
    await page.screenshot({ path: 'test-results/simple-test-screenshot.png' });
  });
});
