/**
 * @fileoverview Helper for capturing and organizing screenshots for AI-based visual analysis
 */

const fs = require('fs').promises;
const path = require('path');

class VisualAnalysisHelper {
    /**
     * Captures screenshot for AI analysis
     * @param {Page} page - Playwright page object
     * @param {string} testName - Name of the test
     * @param {string} screenshotName - Name of the screenshot
     * @param {Object} options - Screenshot options
     * @returns {Promise<string>} Path to the saved screenshot
     */
    static async captureScreenshot(page, testName, screenshotName, options = {}) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const deviceInfo = {
            width: page.viewportSize()?.width || 'full',
            height: page.viewportSize()?.height || 'full',
            device: process.env.BROWSERSTACK_DEVICE || 'desktop',
            browser: process.env.BROWSERSTACK_BROWSER_NAME || 'chrome'
        };

        const screenshotDir = path.join(process.cwd(), 'test-results', 'ai-analysis', testName, timestamp);
        await fs.mkdir(screenshotDir, { recursive: true });

        const fileName = `${screenshotName}_${deviceInfo.device}_${deviceInfo.width}x${deviceInfo.height}.png`;
        const filePath = path.join(screenshotDir, fileName);

        const defaultOptions = {
            fullPage: true,
            timeout: 30000,
            ...options
        };

        await page.screenshot({
            path: filePath,
            ...defaultOptions
        });

        // Create metadata file for AI analysis
        const metadata = {
            testName,
            screenshotName,
            timestamp,
            deviceInfo,
            url: page.url(),
            viewportSize: page.viewportSize(),
            options: defaultOptions,
            browserstack: {
                device: process.env.BROWSERSTACK_DEVICE,
                os: process.env.BROWSERSTACK_OS,
                browser: process.env.BROWSERSTACK_BROWSER_NAME,
                browser_version: process.env.BROWSERSTACK_BROWSER_VERSION
            }
        };

        await fs.writeFile(
            filePath.replace('.png', '.json'),
            JSON.stringify(metadata, null, 2)
        );

        return filePath;
    }

    /**
     * Captures a series of interaction states for AI analysis
     * @param {Page} page - Playwright page object
     * @param {string} testName - Name of the test
     * @param {Array<{name: string, action: Function}>} interactions - Array of interaction steps
     */
    static async captureInteractionStates(page, testName, interactions) {
        for (const { name, action } of interactions) {
            // Capture before state
            await this.captureScreenshot(page, testName, `${name}_before`);
            
            // Perform interaction
            await action(page);
            
            // Wait for stability
            await page.waitForLoadState('networkidle');
            
            // Capture after state
            await this.captureScreenshot(page, testName, `${name}_after`);
        }
    }

    /**
     * Captures responsive states for AI analysis
     * @param {Page} page - Playwright page object
     * @param {string} testName - Name of the test
     * @param {Array<{width: number, height: number}>} viewports - Array of viewport sizes
     */
    static async captureResponsiveStates(page, testName, viewports) {
        for (const viewport of viewports) {
            await page.setViewportSize(viewport);
            await page.waitForLoadState('networkidle');
            
            await this.captureScreenshot(
                page,
                testName,
                `responsive_${viewport.width}x${viewport.height}`
            );
        }
    }
}

module.exports = { VisualAnalysisHelper }; 