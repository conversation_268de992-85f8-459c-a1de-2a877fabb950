// @ts-check

const { devices, defineConfig } = require('@playwright/test');
const path = require('path');
const yamlConfig = require('yaml');
const fs = require('fs');

// Load environment variables from .env file
try {
  require('dotenv').config();
  console.log('[Playwright Config] Loaded environment variables from .env file');

  // Log Mailtrap environment variables (masked)
  if (process.env.MAILTRAP_API_TOKEN) {
    const maskedToken = `${process.env.MAILTRAP_API_TOKEN.substring(0, 4)}...${process.env.MAILTRAP_API_TOKEN.substring(process.env.MAILTRAP_API_TOKEN.length - 4)}`;
    console.log(`[Playwright Config] MAILTRAP_API_TOKEN: ${maskedToken}`);
  } else {
    console.log('[Playwright Config] MAILTRAP_API_TOKEN: not defined');
  }

  if (process.env.MAILTRAP_INBOX_ID) {
    console.log(`[Playwright Config] MAILTRAP_INBOX_ID: ${process.env.MAILTRAP_INBOX_ID}`);
  } else {
    console.log('[Playwright Config] MAILTRAP_INBOX_ID: not defined');
  }
} catch (error) {
  console.warn('[Playwright Config] Could not load .env file:', error.message);
}

// Load environment variables
const brand = process.env.BRAND || 'aeons';
const testEnv = process.env.TEST_ENV || 'stage';
const platform = process.env.PLATFORM || 'windows-chrome';
const testType = process.env.TEST_TYPE || 'regression';

// Determine base URL
const getBaseUrl = () => {
  try {
    const urlManager = require('./src/utils/url-manager');
    const brand = process.env.BRAND || 'aeons';
    const environment = process.env.TEST_ENV || 'stage';

    // Get URL from URL manager
    const baseUrl = urlManager.getBaseUrl(brand, environment);
    console.log(`URL Manager returned base URL: ${baseUrl} for brand: ${brand}, environment: ${environment}`);

    return baseUrl;
  } catch (error) {
    console.warn(`Error loading URL from URL Manager: ${error.message}`);

    // Fallback URLs if URL manager fails
    const fallbackUrls = {
      aeons: {
        dev: 'https://aeons-dev.info',
        stage: 'https://aeonstest.info',
        prod: 'https://aeons.co.uk'
      },
      dss: {
        dev: 'https://dss-dev.info',
        stage: 'https://dss.crm-test.info',
        prod: 'https://drsisterskincare.com'
      },
      ypn: {
        dev: 'https://ypn-dev.info',
        stage: 'https://ypntest.info',
        prod: 'https://yourpetnutrition.com'
      }
    };

    const brand = process.env.BRAND || 'aeons';
    const environment = process.env.TEST_ENV || 'stage';

    // Use brand-specific fallback URL if available
    if (fallbackUrls[brand] && fallbackUrls[brand][environment]) {
      console.log(`Using fallback URL for ${brand} in ${environment} environment: ${fallbackUrls[brand][environment]}`);
      return fallbackUrls[brand][environment];
    }

    return 'https://aeonstest.info';
  }
};

const baseUrl = getBaseUrl();
console.log(`Using base URL: ${baseUrl}`);

// Determine if we're running in BrowserStack mode
const isBrowserStack = (!!process.env.BROWSERSTACK_USERNAME &&
  !!process.env.BROWSERSTACK_ACCESS_KEY) &&
  (process.env.BROWSERSTACK_SDK_ENABLED === 'true' ||
   process.env.BROWSERSTACK_FORCED === 'true');

console.log(`Running in BrowserStack mode: ${isBrowserStack}`);
console.log(`Platform: ${platform}`);

// Determine if it's a mobile platform
const isMobile = platform.includes('android') ||
                platform.includes('iphone') ||
                platform.includes('ios') ||
                platform.includes('galaxy') ||
                platform.includes('mobile');

console.log(`Mobile platform: ${isMobile}`);

/**
 * @see https://playwright.dev/docs/test-configuration
 * @type {import('@playwright/test').PlaywrightTestConfig}
 */
module.exports = defineConfig({
  testDir: './tests',
  testMatch: '**/*.spec.js',
  testIgnore: ['**/node_modules/**', '**/playwright-report/**', '**/test-results/**'],
  timeout: parseInt(process.env.TEST_TIMEOUT || (isMobile ? '300000' : '180000'), 10),
  fullyParallel: false,
  forbidOnly: !!process.env.CI,
  retries: parseInt(process.env.RETRY_COUNT || process.env.CI ? '1' : '0', 10),
  workers: parseInt(process.env.WORKERS || '1', 10),
  reporter: [['html', { open: 'never' }], ['list']],

  use: {
    // Prioritize the URL from the URL manager over the BASE_URL environment variable
    baseURL: baseUrl || process.env.BASE_URL,
    screenshot: 'only-on-failure',
    trace: 'retain-on-failure',
    video: 'on-first-retry',
    navigationTimeout: 60000,
    actionTimeout: 30000,
  },

  projects: [
    // Local testing projects with names matching platform IDs
    {
      name: 'windows-chrome',
      use: {
        ...devices['Desktop Chrome'],
      },
    },
    {
      name: 'mac-safari',
      use: {
        ...devices['Desktop Safari'],
      },
    },
    {
      name: 'firefox',
      use: {
        ...devices['Desktop Firefox'],
      },
    },
    {
      name: 'samsung-galaxy-s23',
      use: {
        ...devices['Galaxy S23'],
      },
    },
    {
      name: 'iphone-14',
      use: {
        ...devices['iPhone 14'],
      },
    },
  ],

  outputDir: 'test-results/'
});

// Conditionally modify config for BrowserStack
if (isBrowserStack) {
  // Load BrowserStack configuration
  const browserstackYmlPath = path.join(__dirname, 'browserstack.yml');
  let browserstackConfig = {};

  try {
    const yamlContent = fs.readFileSync(browserstackYmlPath, 'utf8');
    browserstackConfig = yamlConfig.parse(yamlContent);
    console.log('Loaded BrowserStack configuration from browserstack.yml');
  } catch (error) {
    console.error(`Error loading browserstack.yml: ${error.message}`);
  }

  // Log platform info
  console.log(`Using BrowserStack for platform: ${platform}`);
  
  // BrowserStack projects will be configured by the SDK
  // The SDK will use the browserstack.yml configuration
  console.log('Using BrowserStack SDK for test execution.');
}