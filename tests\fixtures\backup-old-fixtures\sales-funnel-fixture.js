/**
 * Fixture for sales funnel tests
 */
const { test: baseTest, expect } = require('./unified-fixture');
const { TestDataManager } = require('../data/test-data-manager');

// Page objects for admin
const { AdminLoginPage } = require('../../src/pages/admin/AdminLoginPage');
const { AdminSalesFunnelPage } = require('../../src/pages/admin/AdminSalesFunnelPage');

// Page objects for sales funnel
const { 
    SalesFunnelInitialCheckoutPage,
    SalesFunnelPaymentPage,
    SalesFunnelUpsellPage,
    SalesFunnelConfirmationPage
} = require('../../src/pages/shop');

/**
 * Extended test fixture for sales funnel flows
 */
const test = baseTest.extend({
    /**
     * Test data manager
     */
    testDataManager: async ({}, use) => {
        const testDataManager = require('../../tests/data/test-data-manager');
        testDataManager.initialize('default', 'dss', 'stage');
        await use(testDataManager);
    },

    /**
     * Test data with common values
     */
    testData: async ({ testDataManager }, use) => {
        // Prepare test data
        const testData = {
            // Customer data
            customer: {
                email: '<EMAIL>',
                firstName: 'Test',
                lastName: 'User',
                phone: '1234567890',
                address1: '123 Test Street',
                address2: 'Apt 4B',
                city: 'London',
                postcode: 'SW1A 1AA',
                country: 'GB'
            },
            
            // Credit card data (test card)
            creditCard: {
                number: '****************',
                expiry: '12/30',
                cvc: '123'
            }
        };
        
        // Add test data from the manager
      //  testData.brand = testDataManager.getBrandData();
        
        await use(testData);
    },
    
    /**
     * Page objects for sales funnel tests
     */
    salesFunnelPages: async ({ page }, use) => {
        // Create page objects
        const salesFunnelPages = {
            // Admin pages
            adminLogin: new AdminLoginPage(page),
            adminSalesFunnel: new AdminSalesFunnelPage(page),
            
            // These will be instantiated later when we have the checkout page
            initialCheckout: null,
            payment: null,
            upsell: null,
            confirmation: null
        };
        
        await use(salesFunnelPages);
    }
});

module.exports = { test, expect }; 