/**
 * Updated Mailtrap helper using the official Mailtrap SDK
 * @see https://github.com/railsware/mailtrap-nodejs
 */

class MailtrapHelper {
    constructor(config = {}) {
        // Force reload environment variables to ensure they're available
        try {
            require('dotenv').config();
            console.log('[<PERSON>trap<PERSON>el<PERSON>] Reloaded environment variables from .env file');
        } catch (error) {
            console.warn('[<PERSON><PERSON>p<PERSON>el<PERSON>] Could not reload .env file:', error.message);
        }

        // Check if environment variables are available
        console.log('[MailtrapHelper] Environment check:');
        console.log(`[MailtrapHelper] MAILTRAP_API_TOKEN in process.env: ${process.env.MAILTRAP_API_TOKEN ? 'YES' : 'NO'}`);
        console.log(`[MailtrapHelper] MAILTRAP_INBOX_ID in process.env: ${process.env.MAILTRAP_INBOX_ID ? 'YES' : 'NO'}`);

        // Try to get values from environment or config
        this.apiToken = config.apiToken || process.env.MAILTRAP_API_TOKEN;
        this.inboxId = config.inboxId || process.env.MAILTRAP_INBOX_ID;
        this.accountId = config.accountId || process.env.MAILTRAP_ACCOUNT_ID;

        // Fallback to hardcoded values if needed (from .env)
        if (!this.apiToken) {
            console.warn('[MailtrapHelper] API Token not found in environment, using fallback value');
            this.apiToken = '5a221153388388882ce72db80cd8ac23'; // Fallback to value from .env
        }

        if (!this.inboxId) {
            console.warn('[MailtrapHelper] Inbox ID not found in environment, using fallback value');
            this.inboxId = '3136083'; // Fallback to value from .env
        }

        if (!this.accountId) {
            console.warn('[MailtrapHelper] Account ID not found in environment, using fallback value');
            this.accountId = '634655'; // Fallback to value from .env
        }

        // Store testInboxId for SDK compatibility (SDK requires this specific parameter name)
        this.testInboxId = this.inboxId;

        console.log(`[MailtrapHelper] Configuration: apiToken=${this.apiToken ? '******' : 'undefined'}, inboxId=${this.inboxId || 'undefined'}, accountId=${this.accountId || 'undefined'}`);

        this.initialized = false;
        this.client = null;

        console.log(`[MailtrapHelper] Initializing with: apiToken=${this.apiToken ? '******' : 'undefined'}, inboxId=${this.inboxId || 'undefined'}`);

        // Initialize the client if possible
        this._initializeClient();
    }

    /**
     * Initialize the Mailtrap client
     * @private
     */
    _initializeClient() {
        try {
            // Check if required configuration is present
            if (!this.apiToken) {
                console.warn('[MailtrapHelper] Mailtrap API Token not found. Email verification will be disabled.');
                return;
            }

            if (!this.inboxId) {
                console.warn('[MailtrapHelper] Mailtrap Inbox ID not found. Email verification will be disabled.');
                return;
            }

            // Check if the Mailtrap SDK is installed
            try {
                const { MailtrapClient } = require('mailtrap');

                // Initialize with all required parameters for the SDK
                this.client = new MailtrapClient({
                    token: this.apiToken,
                    testInboxId: this.testInboxId,  // SDK requires this specific parameter name
                    accountId: this.accountId       // SDK requires this for some testing API features
                });

                this.initialized = true;
                console.log('[MailtrapHelper] Mailtrap helper initialized successfully with SDK');
            } catch (error) {
                // Handle if the Mailtrap package is not installed
                if (error.code === 'MODULE_NOT_FOUND') {
                    console.warn('[MailtrapHelper] Mailtrap SDK not found. Run: npm install mailtrap');
                    console.warn('[MailtrapHelper] Using fallback HTTP implementation');
                    this._initializeFallback();
                } else {
                    console.error(`[MailtrapHelper] Error initializing Mailtrap client: ${error.message}`, error);
                }
            }
        } catch (error) {
            console.error(`[MailtrapHelper] Error in Mailtrap initialization: ${error.message}`, error);
        }
    }

    /**
     * Initialize fallback implementation using HTTP requests
     * @private
     */
    _initializeFallback() {
        try {
            // Use node-fetch for HTTP requests if available
            const fetch = require('node-fetch');
            this.fetch = fetch;
            this.baseUrl = 'https://mailtrap.io/api/v1';
            this.headers = {
                'Authorization': `Bearer ${this.apiToken}`,
                'Content-Type': 'application/json'
            };
            this.initialized = true;
            console.log('[MailtrapHelper] Mailtrap fallback initialized with node-fetch');
        } catch (error) {
            console.error(`[MailtrapHelper] Failed to initialize fallback implementation: ${error.message}`, error);
        }
    }

    /**
     * Check if the helper is properly initialized
     * @returns {boolean} True if initialized
     */
    isInitialized() {
        console.log(`[MailtrapHelper] Checking initialization status: ${this.initialized}`);
        return this.initialized;
    }

    /**
     * Get all emails from the inbox
     * @param {Object} params Optional query parameters
     * @returns {Promise<Array>} List of emails
     */
    async getEmails(params = {}) {
        console.log(`[MailtrapHelper] Getting emails with params: ${JSON.stringify(params)}`);

        if (!this.initialized) {
            console.warn('[MailtrapHelper] Mailtrap helper not initialized. Cannot get emails.');
            return [];
        }

        try {
            if (this.client) {
                // SDK implementation - convert to compatible response
                console.log(`[MailtrapHelper] Using SDK to get messages from inbox: ${this.inboxId}`);
                const response = await this.client.testing.messages.get(this.inboxId);
                console.log(`[MailtrapHelper] SDK returned ${response ? response.length : 0} emails`);
                return response || [];
            } else {
                // Fallback implementation
                const url = new URL(`${this.baseUrl}/inboxes/${this.inboxId}/messages`);
                Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

                console.log(`[MailtrapHelper] Using HTTP fallback to get messages: ${url.toString()}`);
                const response = await this.fetch(url, {
                    method: 'GET',
                    headers: this.headers
                });

                if (!response.ok) {
                    console.error(`[MailtrapHelper] HTTP error: ${response.status}`);
                    return [];
                }

                const data = await response.json();
                console.log(`[MailtrapHelper] HTTP fallback returned ${data ? data.length : 0} emails`);
                return data;
            }
        } catch (error) {
            console.error(`[MailtrapHelper] Error getting emails from Mailtrap: ${error.message}`, error);
            return [];
        }
    }

    /**
     * Find email by search criteria
     * @param {string} recipient Email recipient
     * @param {string} subject Email subject (partial match)
     * @param {number} searchPeriod Search period in seconds (default: 1 hour)
     * @returns {Promise<Object|null>} Matching email or null
     */
    async findEmail(recipient, subject, searchPeriod = 3600) {
        console.log(`[MailtrapHelper] Finding email for recipient: "${recipient}", subject: "${subject}", searchPeriod: ${searchPeriod}s`);

        if (!this.initialized) {
            console.warn('[MailtrapHelper] Mailtrap helper not initialized. Cannot find email.');
            return null;
        }

        try {
            // Get emails from the last hour by default
            const search_after = new Date(Date.now() - searchPeriod * 1000).toISOString();
            console.log(`[MailtrapHelper] Search after: ${search_after}`);

            const emails = await this.getEmails({ search_after });
            if (!emails || emails.length === 0) {
                console.log('[MailtrapHelper] No emails found in the inbox.');
                return null;
            }

            console.log(`[MailtrapHelper] Found ${emails.length} total emails in search period`);

            // Filter by recipient if provided
            let filtered = emails;
            if (recipient) {
                console.log(`[MailtrapHelper] Filtering by recipient: ${recipient}`);
                filtered = filtered.filter(email =>
                    email.to_email && email.to_email.includes(recipient)
                );

                console.log(`[MailtrapHelper] After recipient filter: ${filtered.length} emails`);
                if (filtered.length === 0) {
                    console.log(`[MailtrapHelper] No emails found with recipient '${recipient}'`);
                    return null;
                }
            }

            // Filter by subject if provided
            if (subject) {
                console.log(`[MailtrapHelper] Filtering by subject: ${subject}`);
                filtered = filtered.filter(email =>
                    email.subject && email.subject.includes(subject)
                );

                console.log(`[MailtrapHelper] After subject filter: ${filtered.length} emails`);
                if (filtered.length === 0) {
                    console.log(`[MailtrapHelper] No emails found with subject containing '${subject}'`);
                    return null;
                }
            }

            // Return the most recent matching email
            const result = filtered.length > 0 ? filtered[0] : null;
            if (result) {
                console.log(`[MailtrapHelper] Found matching email: ID=${result.id}, Subject="${result.subject}"`);
            } else {
                console.log('[MailtrapHelper] No matching email found after filtering');
            }
            return result;
        } catch (error) {
            console.error(`[MailtrapHelper] Error finding email: ${error.message}`, error);
            return null;
        }
    }

    /**
     * Wait for an email matching criteria to arrive
     * @param {string} recipient Email recipient
     * @param {string} subject Email subject (partial match)
     * @param {number} timeoutSeconds Timeout in seconds (default: 300 seconds / 5 minutes)
     * @param {number} maxAttempts Maximum number of attempts (default: 10)
     * @param {number} intervalSeconds Interval between attempts in seconds (default: 10)
     * @returns {Promise<Object|null>} Matching email or null
     */
    async waitForEmail(recipient, subject, timeoutSeconds = 300, maxAttempts = 10, intervalSeconds = 10) {
        console.log(`[MailtrapHelper] Waiting for email: recipient="${recipient}", subject="${subject}", timeout=${timeoutSeconds}s, maxAttempts=${maxAttempts}, interval=${intervalSeconds}s`);

        if (!this.initialized) {
            console.warn('[MailtrapHelper] Mailtrap helper not initialized. Cannot wait for email.');
            return null;
        }

        let attempts = 0;
        let email = null;
        const startTime = Date.now();
        const timeoutMs = timeoutSeconds * 1000;
        const intervalMs = intervalSeconds * 1000;

        while (attempts < maxAttempts && !email && (Date.now() - startTime) < timeoutMs) {
            console.log(`[MailtrapHelper] Attempt ${attempts + 1}/${maxAttempts} to find email...`);
            email = await this.findEmail(recipient, subject);

            if (!email) {
                attempts++;
                const elapsedTime = Math.round((Date.now() - startTime) / 1000);
                console.log(`[MailtrapHelper] Email not found, attempt ${attempts}/${maxAttempts}. Time elapsed: ${elapsedTime}s/${timeoutSeconds}s. Waiting ${intervalSeconds} seconds...`);
                await new Promise(resolve => setTimeout(resolve, intervalMs));
            } else {
                console.log(`[MailtrapHelper] Email found after ${attempts + 1} attempts and ${Math.round((Date.now() - startTime) / 1000)}s!`);
            }
        }

        if (!email) {
            console.warn(`[MailtrapHelper] Email not found after ${attempts} attempts and ${timeoutSeconds} seconds.`);
        }

        return email;
    }

    /**
     * Get HTML content of an email
     * @param {string} inboxId Inbox ID
     * @param {string} messageId Message ID
     * @returns {Promise<string|null>} HTML content or null
     */
    async getHtmlContent(inboxId, messageId) {
        console.log(`[MailtrapHelper] Getting HTML content for message: inbox=${inboxId}, messageId=${messageId}`);

        if (!this.initialized) {
            console.warn('[MailtrapHelper] Mailtrap helper not initialized. Cannot get email content.');
            return null;
        }

        try {
            if (this.client) {
                // SDK implementation
                console.log('[MailtrapHelper] Using SDK to get HTML content');
                const response = await this.client.testing.messages.getHtmlMessage(inboxId, messageId);
                
                // Add preview logging with a focus on order number elements if present
                if (response) {
                    console.log(`[MailtrapHelper] SDK returned HTML content: ${response.substring(0, 100) + '...'}`);
                    
                    // Check for order number elements
                    if (response.includes('itemprop="orderNumber"')) {
                        const orderNumberMatch = response.match(/<span itemprop="orderNumber"[^>]*>([^<]+)<\/span>/);
                        if (orderNumberMatch && orderNumberMatch[1]) {
                            console.log(`[MailtrapHelper] Found order number in HTML: ${orderNumberMatch[1].trim()}`);
                        } else {
                            console.log(`[MailtrapHelper] Found order number attribute but couldn't extract value`);
                        }
                    } else if (response.includes('Your Order Number')) {
                        console.log(`[MailtrapHelper] Found 'Your Order Number' text but not in expected format`);
                    }
                } else {
                    console.log(`[MailtrapHelper] SDK returned empty content`);
                }
                
                return response || null;
            } else {
                // Fallback implementation
                console.log('[MailtrapHelper] Using HTTP fallback to get HTML content');
                const response = await this.fetch(`${this.baseUrl}/inboxes/${inboxId}/messages/${messageId}/body.html`, {
                    method: 'GET',
                    headers: this.headers
                });

                if (!response.ok) {
                    console.error(`[MailtrapHelper] HTTP error: ${response.status}`);
                    return null;
                }

                const content = await response.text();
                
                // Add preview logging with a focus on order number elements if present
                if (content) {
                    console.log(`[MailtrapHelper] HTTP fallback returned HTML content: ${content.substring(0, 100) + '...'}`);
                    
                    // Check for order number elements
                    if (content.includes('itemprop="orderNumber"')) {
                        const orderNumberMatch = content.match(/<span itemprop="orderNumber"[^>]*>([^<]+)<\/span>/);
                        if (orderNumberMatch && orderNumberMatch[1]) {
                            console.log(`[MailtrapHelper] Found order number in HTML: ${orderNumberMatch[1].trim()}`);
                        } else {
                            console.log(`[MailtrapHelper] Found order number attribute but couldn't extract value`);
                        }
                    } else if (content.includes('Your Order Number')) {
                        console.log(`[MailtrapHelper] Found 'Your Order Number' text but not in expected format`);
                    }
                } else {
                    console.log(`[MailtrapHelper] HTTP fallback returned empty content`);
                }
                
                return content;
            }
        } catch (error) {
            console.error(`[MailtrapHelper] Error getting email HTML content: ${error.message}`, error);
            return null;
        }
    }

    /**
     * Get text content of an email
     * @param {string} inboxId Inbox ID
     * @param {string} messageId Message ID
     * @returns {Promise<string|null>} Text content or null
     */
    async getTextContent(inboxId, messageId) {
        console.log(`[MailtrapHelper] Getting TEXT content for message: inbox=${inboxId}, messageId=${messageId}`);

        if (!this.initialized) {
            console.warn('[MailtrapHelper] Mailtrap helper not initialized. Cannot get email content.');
            return null;
        }

        try {
            if (this.client) {
                // SDK implementation
                console.log('[MailtrapHelper] Using SDK to get TEXT content');
                const response = await this.client.testing.getMessageTextBody(inboxId, messageId);
                console.log(`[MailtrapHelper] SDK returned TEXT content: ${response ? response.substring(0, 100) + '...' : 'null'}`);
                return response || null;
            } else {
                // Fallback implementation
                console.log('[MailtrapHelper] Using HTTP fallback to get TEXT content');
                const response = await this.fetch(`${this.baseUrl}/inboxes/${inboxId}/messages/${messageId}/body.txt`, {
                    method: 'GET',
                    headers: this.headers
                });

                if (!response.ok) {
                    console.error(`[MailtrapHelper] HTTP error: ${response.status}`);
                    return null;
                }

                const content = await response.text();
                console.log(`[MailtrapHelper] HTTP fallback returned TEXT content: ${content ? content.substring(0, 100) + '...' : 'null'}`);
                return content;
            }
        } catch (error) {
            console.error(`[MailtrapHelper] Error getting email text content: ${error.message}`, error);
            return null;
        }
    }
}

module.exports = { MailtrapHelper };