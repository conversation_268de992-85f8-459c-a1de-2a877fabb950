/**
 * @fileoverview Script to analyze visual test results using Gemini AI
 */

const { GeminiAnalysisHelper } = require('../tests/utils/gemini/analysis-helper');
const { ResultProcessor } = require('../tests/utils/gemini/result-processor');
const { HtmlReportGenerator } = require('../tests/utils/reporting/html-generator');
const { ANALYSIS_PROMPTS } = require('../tests/utils/gemini/prompts');
const path = require('path');
const fs = require('fs').promises;
require('dotenv').config();

async function analyzeTestResults() {
    try {
        console.log('Starting visual analysis...');
        
        // Initialize helpers
        const gemini = new GeminiAnalysisHelper(process.env.GEMINI_API_KEY);
        const processor = new ResultProcessor(path.join(process.cwd(), 'test-results'));
        const reportGenerator = new HtmlReportGenerator();

        // Get all test sessions
        const bsResultsDir = path.join(process.cwd(), 'test-results', 'browserstack');
        const sessions = await processor.findScreenshots(bsResultsDir);
        
        for (const session of sessions) {
            console.log(`\nAnalyzing session: ${session}`);
            
            // Find and analyze screenshots
            const screenshots = await processor.findScreenshots(session);
            const screenshotGroups = processor.groupScreenshots(screenshots);
            const screenshotAnalysis = await analyzeScreenshots(gemini, processor, screenshotGroups);
            
            // Analyze video if available
            const videoPath = path.join(session, 'session.mp4');
            let videoAnalysis = null;
            if (await processor.fileExists(videoPath)) {
                console.log('Analyzing session video...');
                const videoData = await fs.readFile(videoPath);
                videoAnalysis = await gemini.analyzeVideo(videoData);
            }

            // Analyze logs
            const logs = await processor.collectLogs(session);
            console.log('Analyzing session logs...');
            const logAnalysis = await gemini.analyzeText(
                JSON.stringify(logs),
                'Analyze these test logs for issues, errors, and performance concerns.'
            );

            // Generate report
            const report = {
                session: path.basename(session),
                timestamp: new Date().toISOString(),
                summary: {
                    screenshots: processor.summarizeAnalysis(screenshotAnalysis),
                    video: videoAnalysis?.summary,
                    logs: logAnalysis?.summary
                },
                details: {
                    screenshots: screenshotAnalysis,
                    video: videoAnalysis,
                    logs: logAnalysis
                }
            };

            // Save reports
            const sessionId = path.basename(session);
            await processor.saveAnalysis(report, sessionId, 'complete');
            
            const htmlReport = reportGenerator.generateReport(report);
            await processor.saveAnalysis(
                htmlReport,
                sessionId,
                'report',
                'html'
            );

            console.log(`Analysis complete for session ${sessionId}`);
        }

    } catch (error) {
        console.error('Error analyzing test results:', error);
        process.exit(1);
    }
}

async function analyzeScreenshots(gemini, processor, groups) {
    const analysis = [];

    for (const group of groups) {
        console.log(`Analyzing ${group.type} screenshots for ${group.testName}...`);

        switch (group.type) {
            case 'responsive':
                analysis.push(await analyzeResponsiveGroup(gemini, processor, group));
                break;
            case 'content':
                analysis.push(await analyzeContentGroup(gemini, processor, group));
                break;
            case 'interaction':
                analysis.push(await analyzeInteractionGroup(gemini, processor, group));
                break;
        }
    }

    return analysis;
}

async function analyzeResponsiveGroup(gemini, processor, group) {
    const analysis = {
        type: 'responsive',
        testName: group.testName,
        viewportAnalysis: [],
        comparisonAnalysis: []
    };

    // Analyze each viewport's screenshots
    const byViewport = new Map();
    for (const screenshot of group.screenshots) {
        const viewport = screenshot.metadata.viewport;
        if (!viewport) continue;

        const key = `${viewport.width}x${viewport.height}`;
        if (!byViewport.has(key)) {
            byViewport.set(key, []);
        }
        byViewport.get(key).push(screenshot);
    }

    // Analyze each viewport
    for (const [viewport, screenshots] of byViewport) {
        const prompt = ANALYSIS_PROMPTS.responsive.single
            .replace('{viewport.width}', screenshots[0].metadata.viewport.width)
            .replace('{viewport.height}', screenshots[0].metadata.viewport.height)
            .replace('{deviceInfo}', JSON.stringify(screenshots[0].metadata.deviceInfo));

        const imageData = await fs.readFile(screenshots[0].path);
        const result = await gemini.analyzeImage(imageData, prompt);
        analysis.viewportAnalysis.push({
            viewport,
            analysis: result
        });
    }

    // Compare across viewports
    if (byViewport.size > 1) {
        const prompt = ANALYSIS_PROMPTS.responsive.comparison
            .replace('{viewports}', Array.from(byViewport.keys()).join(', '));

        const imageDataArray = await Promise.all(
            Array.from(byViewport.values())
                .flat()
                .map(s => fs.readFile(s.path))
        );

        const result = await gemini.compareImages(imageDataArray, prompt);
        analysis.comparisonAnalysis = result;
    }

    return analysis;
}

async function analyzeContentGroup(gemini, processor, group) {
    const analysis = {
        type: 'content',
        testName: group.testName,
        individualAnalysis: [],
        comparisonAnalysis: []
    };

    // Analyze individual screenshots
    for (const screenshot of group.screenshots) {
        const prompt = ANALYSIS_PROMPTS.content.single
            .replace('{metadata.testMetadata}', JSON.stringify(screenshot.metadata.testMetadata));

        const imageData = await fs.readFile(screenshot.path);
        const result = await gemini.analyzeImage(imageData, prompt);
        analysis.individualAnalysis.push({
            screenshot: screenshot.metadata.name,
            analysis: result
        });
    }

    // Find and analyze pairs
    const pairs = processor.findBaselineShopifyPairs(group.screenshots);
    for (const pair of pairs) {
        const prompt = ANALYSIS_PROMPTS.content.comparison
            .replace('{metadata.type}', pair[0].metadata.testMetadata?.section || 'content');

        const imageDataArray = await Promise.all(
            pair.map(s => fs.readFile(s.path))
        );

        const result = await gemini.compareImages(imageDataArray, prompt);
        analysis.comparisonAnalysis.push({
            section: pair[0].metadata.testMetadata?.section || 'unknown',
            analysis: result
        });
    }

    return analysis;
}

async function analyzeInteractionGroup(gemini, processor, group) {
    const analysis = {
        type: 'interaction',
        testName: group.testName,
        sequences: []
    };

    const sequences = processor.groupInteractionSequences(group.screenshots);

    for (const sequence of sequences) {
        const prompt = ANALYSIS_PROMPTS.interaction.sequence
            .replace('{metadata.interaction}', sequence[0].metadata.testMetadata?.interaction || 'unknown')
            .replace('{deviceInfo}', JSON.stringify(sequence[0].metadata.deviceInfo));

        const imageDataArray = await Promise.all(
            sequence.map(s => fs.readFile(s.path))
        );

        const result = await gemini.analyzeSequence(imageDataArray, prompt);
        analysis.sequences.push({
            interaction: sequence[0].metadata.testMetadata?.interaction || 'unknown',
            analysis: result
        });
    }

    return analysis;
}

// Run analysis if called directly
if (require.main === module) {
    analyzeTestResults().catch(console.error);
}

module.exports = {
    analyzeTestResults
}; 