/**
 * @fileoverview Common test helper utilities
 */

const { PageStabilityHelper } = require('../../utils/test-config');
const path = require('path');

class TestHelper {
    /**
     * Wait for page stability
     * @param {import('@playwright/test').Page} page
     */
    static async waitForPageStability(page) {
        await PageStabilityHelper.waitForPageStability(page);
    }

    /**
     * Get baseline screenshot path
     * @param {string} platformName
     * @param {string} testName
     * @returns {string} Path to baseline screenshot
     */
    static getBaselineScreenshotPath(platformName, testName) {
        const brand = platformName.split('-')[0];
        const sanitizedTestName = testName.replace(/[^a-zA-Z0-9]/g, '_');
        return path.join(process.cwd(), 'test-results', 'baseline', brand, `${sanitizedTestName}.png`);
    }

    /**
     * Get test artifacts directory
     * @param {string} testName
     * @returns {string} Path to test artifacts directory
     */
    static getTestArtifactsDir(testName) {
        const sanitizedTestName = testName.replace(/[^a-zA-Z0-9]/g, '_');
        return path.join(process.cwd(), 'test-results', 'artifacts', sanitizedTestName);
    }

    /**
     * Clean test name for file paths
     * @param {string} testName
     * @returns {string} Sanitized test name
     */
    static sanitizeTestName(testName) {
        return testName.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
    }
}

module.exports = {
    TestHelper
}; 