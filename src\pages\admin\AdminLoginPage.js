class AdminLoginPage {
    constructor(page) {
        this.page = page;
        this.selectors = {
            usernameField: '#_username',
            passwordField: '#_password',
            loginButton: 'button[type="submit"]'
        };
    }

    async navigate() {
        // Try to navigate to the admin login page
        await this.page.goto('/admin/login');

        try {
            // Wait for the username field with a shorter timeout first
            await this.page.waitForSelector(this.selectors.usernameField, { timeout: 5000 });
        } catch (error) {
            console.log('Username field not found on first attempt, checking URL and trying alternatives');

            // Check if we're on the right domain
            const currentUrl = this.page.url();
            console.log(`Current URL: ${currentUrl}`);

            // If we're not on an admin page, try alternative admin URLs
            if (!currentUrl.includes('/admin')) {
                // Try alternative admin paths
                console.log('Trying alternative admin URL paths...');

                // Try with /backend which is used in some Symfony-based shops
                await this.page.goto('/backend/login');

                try {
                    await this.page.waitForSelector(this.selectors.usernameField, { timeout: 5000 });
                    console.log('Found login form at /backend/login');
                    return;
                } catch (e) {
                    console.log('Login form not found at /backend/login');
                }

                // Try with /admin-panel which is used in some shops
                await this.page.goto('/admin-panel/login');

                try {
                    await this.page.waitForSelector(this.selectors.usernameField, { timeout: 5000 });
                    console.log('Found login form at /admin-panel/login');
                    return;
                } catch (e) {
                    console.log('Login form not found at /admin-panel/login');
                }

                // Fall back to original admin URL with longer timeout
                console.log('Falling back to original admin URL with longer timeout');
                await this.page.goto('/admin/login');
            }

            // Wait with a longer timeout for the final attempt
            await this.page.waitForSelector(this.selectors.usernameField, { timeout: 30000 });
        }
    }

    async login(username, password) {
        await this.navigate();
        await this.page.fill(this.selectors.usernameField, username);
        await this.page.fill(this.selectors.passwordField, password);

        // Use Promise.all to wait for navigation initiated by the click
        await Promise.all([
            this.page.waitForNavigation({ waitUntil: 'load' }), // Start waiting for navigation
            this.page.click(this.selectors.loginButton)         // Click the button
        ]);
    }
}

module.exports = { AdminLoginPage };