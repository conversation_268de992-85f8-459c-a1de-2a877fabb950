/**
 * @fileoverview Error analysis utilities for Gemini
 */

const { prompts } = require('../config/prompts');

class ErrorAnalyzer {
    /**
     * Constructor for ErrorAnalyzer
     * @param {Object} geminiService Gemini service instance
     */
    constructor(geminiService) {
        this.geminiService = geminiService;
    }

    /**
     * Analyze error data
     * @param {Object} data Error data to analyze
     * @param {Object} options Analysis options
     * @returns {Promise<Object>} Analysis results
     */
    async analyze(data, options = {}) {
        try {
            const prompt = prompts.errorAnalysis;
            const analysis = await this.geminiService.analyzeError(data, prompt);

            return {
                error: this.formatError(data.errors),
                logs: data.logs,
                screenshots: data.screenshots,
                analysis,
                severity: this.determineSeverity(data.errors),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error analyzing error data:', error);
            return {
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Format error data for analysis
     * @private
     * @param {Array<Error>} errors Array of errors
     * @returns {Object} Formatted error data
     */
    formatError(errors) {
        if (!Array.isArray(errors)) {
            errors = [errors];
        }

        return errors.map(error => ({
            message: error.message || error,
            stack: error.stack,
            type: error.name || 'Error',
            timestamp: new Date().toISOString()
        }));
    }

    /**
     * Determine error severity
     * @private
     * @param {Array<Error>} errors Array of errors
     * @returns {string} Error severity
     */
    determineSeverity(errors) {
        if (!Array.isArray(errors)) {
            errors = [errors];
        }

        // This is a simple implementation
        // In practice, you'd want more sophisticated logic
        if (errors.some(e => e.name === 'AssertionError')) {
            return 'critical';
        }
        if (errors.some(e => e.name === 'TypeError' || e.name === 'ReferenceError')) {
            return 'major';
        }
        return 'minor';
    }
}

module.exports = { ErrorAnalyzer }; 