/**
 * @fileoverview LoginPage page object for account login functionality
 */
class LoginPage {
  /**
   * Initialize the page object
   * @param {import('@playwright/test').Page} page 
   */
  constructor(page) {
    this.page = page;
    this.selectors = {
      emailInput: 'input[name="_username"]',
      passwordInput: 'input[name="_password"]',
      loginButton: 'button[type="submit"]',
      forgotPasswordLink: 'a.resset-pass',
      errorMessage: '.alert-danger'
    };
  }

  /**
   * Navigate to the login page
   * @param {string} [baseUrl] Optional base URL, defaults to environment variable or fallback
   */
  async navigate(baseUrl) {
    const url = baseUrl || process.env.BASE_URL || 'https://aeonstest.info';
    await this.page.goto(`${url}/login`);
    await this.page.waitForSelector(this.selectors.emailInput);
  }

  /**
   * Fill the email input field
   * @param {string} email 
   */
  async fillEmail(email) {
    await this.page.fill(this.selectors.emailInput, email);
  }

  /**
   * Fill the password input field
   * @param {string} password 
   */
  async fillPassword(password) {
    await this.page.fill(this.selectors.passwordInput, password);
  }

  /**
   * Click the login button
   */
  async clickLogin() {
    await this.page.click(this.selectors.loginButton);
  }

  /**
   * Complete the login process
   * @param {string} email 
   * @param {string} password 
   */
  async login(email, password) {
    await this.fillEmail(email);
    await this.fillPassword(password);
    await this.clickLogin();
    
    // Wait for navigation to complete after login
    await this.page.waitForNavigation({ waitUntil: 'networkidle' });
  }

  /**
   * Check if the email format is valid
   * @param {string} email 
   * @returns {Promise<boolean>}
   */
  async isEmailValidFormat(email) {
    await this.fillEmail(email);
    const value = await this.page.inputValue(this.selectors.emailInput);
    return /\S+@\S+\.\S+/.test(value);
  }

  /**
   * Check if the password is hidden
   * @returns {Promise<boolean>}
   */
  async isPasswordHidden() {
    const type = await this.page.getAttribute(this.selectors.passwordInput, 'type');
    return type === 'password';
  }

  /**
   * Check if the login button is enabled
   * @returns {Promise<boolean>}
   */
  async isLoginButtonEnabled() {
    return await this.page.isEnabled(this.selectors.loginButton);
  }

  /**
   * Click the forgot password link
   */
  async clickForgotPassword() {
    await this.page.click(this.selectors.forgotPasswordLink);
    await this.page.waitForNavigation({ waitUntil: 'networkidle' });
  }

  /**
   * Get error message if login fails
   * @returns {Promise<string|null>}
   */
  async getErrorMessage() {
    const errorElement = this.page.locator(this.selectors.errorMessage);
    if (await errorElement.count() > 0) {
      return await errorElement.textContent();
    }
    return null;
  }
}

module.exports = { LoginPage };
