/**
 * @fileoverview Example test using the enhanced BrowserStack Android integration
 */

// Import from the fixture factory for automatic environment-based fixtures
const { test, expect } = require('../fixtures/fixture-factory');

test.describe('BrowserStack Android Enhanced Example', () => {
    test('should load the homepage with session management @smoke_android', async ({ page, bsHelper }) => {
        // Navigate to the homepage
        await page.goto('/');
        
        // Wait for the page to be fully loaded
        await page.waitForSelector('body', { state: 'visible' });
        
        // Take a screenshot using the enhanced helper that tracks URLs
        await bsHelper.takeScreenshot(page, 'home-page', {
            fullPage: true,
            testName: 'Android Homepage Test'
        });
        
        // Verify title exists
        const title = await page.title();
        console.log(`Page title: ${title}`);
        expect(title).not.toBe('');
        
        // Verify content loads
        const bodyText = await page.textContent('body');
        expect(bodyText).not.toBe('');
    });
    
    test('should use full page screenshots @smoke_android', async ({ page }) => {
        // Navigate to the site
        await page.goto('/');
        
        // Take screenshot using the page extension method
        await page.fullPageScreenshot('android-full-page', {
            testName: 'Android Full Page Test'
        });
        
        // Verification
        expect(await page.isVisible('body')).toBeTruthy();
    });
    
    test('should handle network idle wait @smoke_android', async ({ page }) => {
        // Navigate to the site
        await page.goto('/');
        
        // Use the enhanced network idle wait
        await page.waitForNetworkIdle();
        
        // Take a screenshot after network is idle
        await page.fullPageScreenshot('after-network-idle', {
            testName: 'Android Network Idle Test'
        });
        
        // Verification
        expect(await page.isVisible('body')).toBeTruthy();
    });
}); 