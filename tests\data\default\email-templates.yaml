# Email Templates Configuration
# This file contains email template definitions for testing

# Order confirmation email template
order_confirmation:
  subject: "Order # Is Confirmed | Aeons"
  from_email: "<EMAIL>"
  required_elements:
    - "Order Number"
    - "Order Summary"
    - "Shipping Address"
    - "Payment Method"
    - "Order Total"
  keywords:
    - "Thank you for your purchase"
    - "shipping"
    - "payment"
  success_patterns:
    - "Your order has been successfully processed"
    - "Thank you for shopping with Aeons"
  action_links:
    track_order: "Track Order"
    view_account: "View Account"
    customer_service: "Customer Service"

# Abandoned cart recovery email template
abandoned_cart:
  subject: "Complete your purchase"
  from_email: "<EMAIL>"
  required_elements:
    - "Cart Items"
    - "Special Offer"
  keywords:
    - "items in your cart"
    - "checkout"
    - "limited time"
  success_patterns:
    - "complete your purchase"
    - "waiting for you"
  action_links:
    return_to_cart: "Return to Cart"
    view_offer: "View Special Offer"

# Default sender information
sender:
  email: "<EMAIL>"
  name: "Aeons Customer Care"

# DSS (Dr. <PERSON>) specific overrides
dss:
  order_confirmation:
    subject: "Your Dr. Sister Skincare Order Confirmation"
    from_email: "<EMAIL>"
  sender:
    email: "<EMAIL>"
    name: "Dr. Sister Skincare"

# YPN (Your Pet Nutrition) specific overrides
ypn:
  order_confirmation:
    subject: "Your Pet Nutrition Order Confirmation"
    from_email: "<EMAIL>"
  sender:
    email: "<EMAIL>"
    name: "Your Pet Nutrition"