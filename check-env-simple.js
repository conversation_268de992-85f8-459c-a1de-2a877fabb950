// Simple script to check Mailtrap environment variables
require('dotenv').config();

console.log('ENV CHECK START');

// Check API TOKEN
if (process.env.MAILTRAP_API_TOKEN) {
  console.log('MAILTRAP_API_TOKEN is defined');
} else {
  console.log('MAILTRAP_API_TOKEN is NOT defined');
}

// Check INBOX ID
if (process.env.MAILTRAP_INBOX_ID) {
  console.log('MAILTRAP_INBOX_ID is defined');
} else {
  console.log('MAILTRAP_INBOX_ID is NOT defined');
}

console.log('ENV CHECK END'); 