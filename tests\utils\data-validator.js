/**
 * @fileoverview Utility for validating test data
 * 
 * This utility provides functions to validate test data and ensure required
 * fields are present before running tests.
 */

/**
 * Validates product data to ensure required fields are present
 * @param {Object} product - The product data object to validate
 * @param {Array<string>} [requiredFields] - Optional array of required field names
 * @throws {Error} Throws error if required fields are missing
 */
function validateProductData(product, requiredFields = ['name', 'url_path', 'price']) {
    if (!product) {
        throw new Error('Product data is missing');
    }

    const missingFields = requiredFields.filter(field => !product[field]);
    if (missingFields.length > 0) {
        throw new Error(`Product data is missing required fields: ${missingFields.join(', ')}`);
    }
}

/**
 * Validates user data to ensure required fields are present
 * @param {Object} user - The user data object to validate
 * @param {Array<string>} [requiredFields] - Optional array of required field names
 * @throws {Error} Throws error if required fields are missing
 */
function validateUserData(user, requiredFields = ['email', 'firstName', 'lastName']) {
    if (!user) {
        throw new Error('User data is missing');
    }

    const missingFields = requiredFields.filter(field => !user[field]);
    if (missingFields.length > 0) {
        throw new Error(`User data is missing required fields: ${missingFields.join(', ')}`);
    }
}

/**
 * Validates payment method data to ensure required fields are present
 * @param {Object} paymentMethod - The payment method data object to validate
 * @param {Array<string>} [requiredFields] - Optional array of required field names
 * @throws {Error} Throws error if required fields are missing
 */
function validatePaymentMethodData(paymentMethod, requiredFields = ['type', 'cardNumber', 'expiry', 'cvc']) {
    if (!paymentMethod) {
        throw new Error('Payment method data is missing');
    }

    const missingFields = requiredFields.filter(field => !paymentMethod[field]);
    if (missingFields.length > 0) {
        throw new Error(`Payment method data is missing required fields: ${missingFields.join(', ')}`);
    }
}

/**
 * Validates payment method data based on payment type
 * @param {Object} paymentMethod - The payment method data object to validate
 * @throws {Error} Throws error if required fields for the payment type are missing
 */
function validatePaymentMethodByType(paymentMethod) {
    if (!paymentMethod) {
        throw new Error('Payment method data is missing');
    }

    // Determine payment method type
    let paymentType = 'card'; // Default to card
    
    if (paymentMethod.email && !paymentMethod.cardNumber) {
        paymentType = 'paypal';
    } else if (paymentMethod.type) {
        paymentType = paymentMethod.type.toLowerCase();
    }
    
    // Validate based on payment type
    switch (paymentType) {
        case 'card':
            const requiredCardFields = ['cardNumber', 'expiry', 'cvc'];
            const missingCardFields = requiredCardFields.filter(field => !paymentMethod[field]);
            if (missingCardFields.length > 0) {
                throw new Error(`Card payment method missing required fields: ${missingCardFields.join(', ')}`);
            }
            break;
            
        case 'paypal':
            if (!paymentMethod.email) {
                throw new Error('PayPal payment method missing required field: email');
            }
            break;
            
        default:
            console.warn(`Unknown payment type: ${paymentType}, basic validation only`);
            // For unknown types, just ensure there's some data
            if (Object.keys(paymentMethod).length === 0) {
                throw new Error(`Payment method has no data`);
            }
    }
}

/**
 * Validates test data to ensure all required components are present
 * @param {Object} testData - The test data object containing product, user, and payment_method
 * @throws {Error} Throws error if required data is missing
 */
function validateTestData(testData) {
    if (!testData) {
        throw new Error('Test data is missing');
    }

    validateProductData(testData.product);
    validateUserData(testData.user);
    validatePaymentMethodData(testData.payment_method);
}

/**
 * Enhanced test data validation with improved quantity handling
 * @param {Object} input - Either a testDataManager instance or a testData object
 * @returns {Object} The validated and normalized test data
 */
function validateTestDataEnhanced(input) {
    // Determine if input is a testDataManager or a direct testData object
    let testData;
    
    if (input.getProduct && input.getUser && input.getPaymentMethod) {
        // Input is a testDataManager
        testData = {
            product: input.getProduct('ancient_roots_olive_oil'),
            user: input.getUser('default'),
            paymentMethod: input.getPaymentMethod('stripe_valid')
        };
    } else if (input.product && input.user && input.paymentMethod) {
        // Input is already a testData object
        testData = input;
    } else {
        throw new Error('Invalid input: expected either testDataManager or testData object');
    }

    // Validate product data
    if (!testData.product) {
        throw new Error('Failed to get product data');
    }

    // Adapt validation to YAML structure
    // In YAML the structure is different from what the test expects
    // Fix the structure to match what the test needs
    if (!testData.product.options) {
        testData.product.options = {
            purchaseTypes: {
                oneTime: "One-Time Purchase",
                subscription: "Subscribe & Save"
            },
            quantities: {
                minimum: { numberOfItems: 1 },
                medium: { numberOfItems: 3 },
                maximum: { numberOfItems: 6 }
            }
        };
    } else if (!testData.product.options.purchaseTypes) {
        // Make sure purchaseTypes is initialized even if options exists
        // First check if we have purchase_types from YAML in options.purchase_types
        if (testData.product.options.purchase_types) {
            testData.product.options.purchaseTypes = {
                oneTime: testData.product.options.purchase_types.one_time || "One-Time Purchase",
                subscription: testData.product.options.purchase_types.subscription || "Subscribe & Save"
            };
        } else {
            // Default fallback
            testData.product.options.purchaseTypes = {
                oneTime: "One-Time Purchase",
                subscription: "Subscribe & Save"
            };
        }
    }
    
    // Ensure urlPath is set properly (handle different property naming conventions)
    if (!testData.product.urlPath && testData.product.url_path) {
        testData.product.urlPath = testData.product.url_path;
    }

    // If neither urlPath nor url_path exists, create it from the slug
    if (!testData.product.urlPath && testData.product.slug) {
        testData.product.urlPath = `products/${testData.product.slug}`;
    }

    // Validate flavor data if product has flavors
    if (testData.product.flavors) {
        const defaultFlavor = 'classic';
        if (!testData.product.flavors[defaultFlavor]) {
            throw new Error(`Product missing required default flavor: ${defaultFlavor}`);
        }
        
        // Check for prices in the YAML structure
        if (!testData.product.flavors[defaultFlavor].prices?.one_time?.medium) {
            throw new Error('Product missing required medium oneTime price for default flavor');
        }
        
        // Add a reference for easier access in the tests
        if (testData.product.flavors[defaultFlavor].prices.one_time.medium.price) {
            if (!testData.product.prices) {
                testData.product.prices = {
                    one_time: {
                        medium: testData.product.flavors[defaultFlavor].prices.one_time.medium.price
                    }
                };
            }
        }
    }

    // Validate user data
    const requiredUserFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'city', 'postcode', 'country'];
    requiredUserFields.forEach(field => {
        if (!testData.user[field]) {
            throw new Error(`User missing required field: ${field}`);
        }
    });

    // Replace the existing payment method validation with the type-aware version
    validatePaymentMethodByType(testData.paymentMethod);

    return testData;
}

module.exports = {
    validateProductData,
    validateUserData,
    validatePaymentMethodData,
    validatePaymentMethodByType,
    validateTestData,
    validateTestDataEnhanced
};