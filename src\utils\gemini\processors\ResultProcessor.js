/**
 * @fileoverview Result processing utilities for Gemini analysis
 */

class ResultProcessor {
    constructor() {
        this.results = {
            visual: [],
            performance: [],
            errors: [],
            browserstack: null
        };
    }

    /**
     * Add visual analysis result
     * @param {string} screenshot Screenshot identifier
     * @param {Object} analysis Analysis results
     */
    addVisualAnalysis(screenshot, analysis) {
        this.results.visual.push({
            screenshot,
            analysis,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * Add performance analysis result
     * @param {Object} data Performance data
     * @param {Object} analysis Analysis results
     */
    addPerformanceData(data, analysis) {
        this.results.performance.push({
            data,
            analysis,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * Add error analysis result
     * @param {Error} error Error object
     * @param {Object} analysis Analysis results
     */
    addError(error, analysis) {
        this.results.errors.push({
            error: error.message || error,
            stack: error.stack,
            analysis,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * Add BrowserStack artifacts to results
     * @param {Object} artifacts - BrowserStack artifacts
     */
    addBrowserStackArtifacts(artifacts) {
        this.results.browserstack = artifacts;
    }

    /**
     * Generate final analysis report
     * @returns {Object} - Complete analysis report
     */
    generateReport() {
        return {
            timestamp: new Date().toISOString(),
            results: this.results,
            summary: this.generateSummary()
        };
    }

    /**
     * Clear all results
     */
    clear() {
        this.results = {
            visual: [],
            performance: [],
            errors: [],
            browserstack: null
        };
    }

    generateSummary() {
        // Implementation of generateSummary method
    }
}

module.exports = { ResultProcessor }; 