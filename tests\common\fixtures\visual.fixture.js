/**
 * @fileoverview Visual testing fixture extending BrowserStack fixture
 */

const { VisualTestHelper } = require('../../utils/visual-test-helper');
const { PageStabilityHelper } = require('../../utils/test-config');

/**
 * Extended test fixture for visual testing
 */
exports.test = bsTest.extend({
    /**
     * Visual test helper instance
     */
    visualHelper: async ({}, use) => {
        await use(new VisualTestHelper());
    },

    /**
     * Enhanced page object with visual testing capabilities
     */
    page: async ({ page }, use, testInfo) => {
        const visualHelper = new VisualTestHelper();
        const screenshots = [];

        // Enhance page with visual testing methods
        await enhancePageWithVisualMethods(page, visualHelper, testInfo, screenshots);

        // Use enhanced page
        await use(page);

        // After test execution, analyze results
        if (screenshots.length > 0) {
            const browserLogs = await page.context().browser().browserLogs;
            const results = await visualHelper.analyzeTestResults(
                testInfo,
                screenshots,
                browserLogs
            );
            await visualHelper.saveAnalysisResults(results, testInfo);
        }
    }
});

/**
 * Enhance page with visual testing methods
 * @param {Page} page Playwright page object
 * @param {VisualTestHelper} visualHelper Visual test helper
 * @param {Object} testInfo Test information
 * @param {Array<string>} screenshots Screenshot collection
 */
async function enhancePageWithVisualMethods(page, visualHelper, testInfo, screenshots) {
    /**
     * Take screenshot with visual stability check
     * @param {string} name Screenshot name
     * @param {Object} options Screenshot options
     */
    page.screenshot = async (name, options = {}) => {
        await PageStabilityHelper.waitForPageStability(page);
        const path = await visualHelper.takeScreenshot(page, name, {
            ...options,
            testInfo
        });
        screenshots.push(path);
        return path;
    };

    /**
     * Take element screenshot
     * @param {string} selector Element selector
     * @param {string} name Screenshot name
     * @param {Object} options Screenshot options
     */
    page.screenshotElement = async (selector, name, options = {}) => {
        await PageStabilityHelper.waitForPageStability(page);
        const element = await page.$(selector);
        if (!element) {
            throw new Error(`Element not found: ${selector}`);
        }
        const path = await visualHelper.takeScreenshot(page, name, {
            ...options,
            clip: await element.boundingBox(),
            testInfo
        });
        screenshots.push(path);
        return path;
    };

    /**
     * Take full page screenshot
     * @param {string} name Screenshot name
     * @param {Object} options Screenshot options
     */
    page.screenshotFullPage = async (name, options = {}) => {
        await PageStabilityHelper.waitForPageStability(page);
        const path = await visualHelper.takeScreenshot(page, name, {
            ...options,
            fullPage: true,
            testInfo
        });
        screenshots.push(path);
        return path;
    };

    /**
     * Take screenshots of multiple elements
     * @param {Array<Object>} elements Elements to screenshot
     * @param {string} baseName Base name for screenshots
     * @param {Object} options Screenshot options
     */
    page.screenshotElements = async (elements, baseName, options = {}) => {
        const results = [];
        for (const element of elements) {
            const name = `${baseName}-${element.name}`;
            const path = await page.screenshotElement(
                element.selector,
                name,
                options
            );
            results.push({ name, path, element });
        }
        return results;
    };

    /**
     * Take responsive screenshots
     * @param {string} name Screenshot name
     * @param {Array<Object>} viewports Viewport configurations
     * @param {Object} options Screenshot options
     */
    page.screenshotResponsive = async (name, viewports, options = {}) => {
        const results = [];
        const originalViewport = page.viewportSize();

        for (const viewport of viewports) {
            await page.setViewportSize(viewport);
            await PageStabilityHelper.waitForPageStability(page);

            const path = await visualHelper.takeScreenshot(page, `${name}-${viewport.name}`, {
                ...options,
                fullPage: true,
                testInfo
            });
            results.push({ viewport, path });
            screenshots.push(path);
        }

        // Restore original viewport
        await page.setViewportSize(originalViewport);
        return results;
    };
}

// Export the test object
exports.test = test;
