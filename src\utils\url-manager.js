/**
 * @fileoverview URL Manager for handling brand and environment-specific URLs
 */
const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

class UrlManager {
    constructor() {
        this.brandConfigs = {};
    }

    /**
     * Initialize URL manager with brand and environment
     * @param {string} brand - Brand code (e.g., 'aeons', 'dss')
     * @param {string} environment - Environment (e.g., 'dev', 'stage', 'prod')
     */
    initialize(brand = 'aeons', environment = 'stage') {
        // Set defaults if not provided
        brand = brand || process.env.BRAND || 'aeons';
        environment = environment || process.env.TEST_ENV || 'stage';
        
        console.log(`Initializing URL manager for brand: ${brand}, environment: ${environment}`);
        
        try {
            // Load brand configuration for the specified environment
            const configPath = path.join(
                process.cwd(), 
                'tests', 
                'data', 
                'brands', 
                brand, 
                'config', 
                `${environment}.yml`
            );
            
            if (fs.existsSync(configPath)) {
                const fileContents = fs.readFileSync(configPath, 'utf8');
                const config = yaml.load(fileContents);
                this.brandConfigs[`${brand}_${environment}`] = config;
                console.log(`Loaded URL configuration for ${brand} in ${environment} environment: ${config.brand.url}`);
                return config.brand.url;
            } else {
                console.warn(`Configuration file not found: ${configPath}`);
                // Fall back to default URL
                return this.getFallbackUrl(environment);
            }
        } catch (error) {
            console.error(`Error loading brand configuration: ${error.message}`);
            return this.getFallbackUrl(environment);
        }
    }

    /**
     * Get base URL for the specified brand and environment
     * @param {string} brand - Brand code
     * @param {string} environment - Environment
     * @returns {string} Base URL
     */
    getBaseUrl(brand = null, environment = null) {
        // Use provided values or fall back to environment variables
        brand = brand || process.env.BRAND || 'aeons';
        environment = environment || process.env.TEST_ENV || 'stage';
        
        const configKey = `${brand}_${environment}`;
        
        // If configuration is already loaded, return it
        if (this.brandConfigs[configKey]) {
            return this.brandConfigs[configKey].brand.url;
        }
        
        // Otherwise, initialize and return
        return this.initialize(brand, environment);
    }

    /**
     * Get product URL for the specified product, brand, and environment
     * @param {string} productSlug - Product slug
     * @param {string} brand - Brand code
     * @param {string} environment - Environment
     * @returns {string} Full product URL
     */
    getProductUrl(productSlug, brand = null, environment = null) {
        const baseUrl = this.getBaseUrl(brand, environment);
        return `${baseUrl}/products/${productSlug}`;
    }

    /**
     * Get fallback URL for the specified environment
     * @param {string} environment - Environment
     * @returns {string} Fallback URL
     */
    getFallbackUrl(environment) {
        // Determine the brand from environment or default to aeons
        const brand = process.env.BRAND || 'aeons';
        
        // Brand-specific fallback URLs
        const fallbackUrlsByBrand = {
            aeons: {
                dev: 'https://aeons-dev.info',
                stage: 'https://aeonstest.info',
                prod: 'https://aeons.co.uk'
            },
            dss: {
                dev: 'https://dss-dev.info',
                stage: 'https://dss.crm-test.info',
                prod: 'https://drsisterskincare.com'
            },
            ypn: {
                dev: 'https://ypn-dev.info',
                stage: 'https://ypntest.info',
                prod: 'https://yourpetnutrition.com'
            }
        };
        
        // Get URLs for the current brand, fallback to aeons if brand not found
        const brandUrls = fallbackUrlsByBrand[brand] || fallbackUrlsByBrand.aeons;
        
        return brandUrls[environment] || brandUrls.stage;
    }
}

// Export singleton instance
module.exports = new UrlManager(); 