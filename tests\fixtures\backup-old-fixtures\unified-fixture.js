/**
 * @fileoverview Unified Test Fixture
 *
 * A standardized fixture for all test types that works with both
 * local Playwright and BrowserStack SDK mode.
 */

const base = require('@playwright/test');
const { BrowserStackService } = require('../../src/utils/browserstack/browserstack-service');
const testDataManager = require('../data/test-data-manager');
const { MailtrapHelper } = require('../../src/utils/email/mailtrap-helper');
const { SessionManager } = require('../../src/utils/session-manager');
const path = require('path');
const fs = require('fs').promises;

/**
 * Creates a unified test fixture that works with both local and BrowserStack tests
 */
class UnifiedFixture {
    /**
     * Get the appropriate test fixtures
     * @returns {Object} Configured test object with all needed fixtures
     */
    static getFixtures() {
        // Base test with common fixtures
        const baseTest = base.test.extend({
            // Add test data manager
            testDataManager: async ({}, use) => {
                const dataSet = process.env.TEST_DATA_SET || 'default';
                const brand = process.env.BRAND || 'aeons';
                const environment = process.env.TEST_ENV || 'stage';

                console.log(`Initializing TestDataManager for brand: ${brand}, environment: ${environment}, dataSet: ${dataSet}`);
                testDataManager.initialize(dataSet, brand, environment);

                await use(testDataManager);
            },

            // Add BrowserStack helper (always available, even in local mode)
            bsHelper: async ({}, use) => {
                const helper = BrowserStackService.getInstance();
                await use(helper);
            },

            // Add Mailtrap helper
            mailtrapHelper: async ({}, use) => {
                console.log('[Unified Fixture] Initializing Mailtrap helper');

                // Force reload environment variables
                try {
                    require('dotenv').config();
                    console.log('[Unified Fixture] Reloaded environment variables from .env file');

                    // Log Mailtrap environment variables (masked)
                    if (process.env.MAILTRAP_API_TOKEN) {
                        const maskedToken = `${process.env.MAILTRAP_API_TOKEN.substring(0, 4)}...${process.env.MAILTRAP_API_TOKEN.substring(process.env.MAILTRAP_API_TOKEN.length - 4)}`;
                        console.log(`[Unified Fixture] MAILTRAP_API_TOKEN: ${maskedToken}`);
                    } else {
                        console.log('[Unified Fixture] MAILTRAP_API_TOKEN: not defined');
                    }

                    if (process.env.MAILTRAP_INBOX_ID) {
                        console.log(`[Unified Fixture] MAILTRAP_INBOX_ID: ${process.env.MAILTRAP_INBOX_ID}`);
                    } else {
                        console.log('[Unified Fixture] MAILTRAP_INBOX_ID: not defined');
                    }
                } catch (error) {
                    console.warn('[Unified Fixture] Could not reload .env file:', error.message);
                }

                // Initialize with explicit values if environment variables are not available
                const config = {
                    apiToken: process.env.MAILTRAP_API_TOKEN || '5a221153388388882ce72db80cd8ac23',
                    inboxId: process.env.MAILTRAP_INBOX_ID || '3136083'
                };

                const mailtrapHelper = new MailtrapHelper(config);
                await use(mailtrapHelper);
            },

            // Set appropriate timeout based on platform
            timeout: [({ }) => {
                const platform = process.env.PLATFORM || '';
                // Longer timeout for mobile platforms
                if (platform.includes('android') || platform.includes('ios') ||
                    platform.includes('galaxy') || platform.includes('iphone') ||
                    process.env.IS_MOBILE === 'true') {
                    return 300000; // 5 minutes for mobile
                }
                return 120000; // 2 minutes for desktop
            }, { option: true }],

            // Register session with metadata
            context: async ({ context }, use) => {
                const sessionManager = SessionManager.getInstance();
                const testId = process.env.TEST_ID || `test_${Date.now()}`;
                const isBrowserStackSdkMode = process.env.BROWSERSTACK_SDK_ENABLED === 'true';

                // Skip context tracking when in BrowserStack SDK mode
                if (isBrowserStackSdkMode) {
                    console.log('BrowserStack SDK mode enabled - context will be managed by SDK');
                } else {
                    // Register browser context with session manager
                    const platform = process.env.PLATFORM || 'unknown';
                    const isAndroid = platform.includes('android') || platform.includes('galaxy');

                    // Skip context tracking for Android to prevent race conditions
                    if (!isAndroid) {
                        await sessionManager.registerSession(testId, context, {
                            metadata: {
                                platform: platform,
                                browser: process.env.BROWSER || 'unknown',
                                testEnv: process.env.TEST_ENV || 'stage',
                                brand: process.env.BRAND || 'aeons'
                            }
                        });
                    } else {
                        console.log('Android platform detected, skipping session registration to prevent race conditions');
                    }
                }

                // Use the context
                await use(context);
            },

            // Initialize BrowserStack session at page creation
            page: async ({ page, bsHelper }, use) => {
                const isBrowserStackSdkMode = process.env.BROWSERSTACK_SDK_ENABLED === 'true';

                // Register session with BrowserStack if running in BrowserStack mode but not SDK mode
                if (process.env.BROWSERSTACK_SESSION_ID && !isBrowserStackSdkMode) {
                    await bsHelper.registerSession(process.env.BROWSERSTACK_SESSION_ID);
                    console.log('BrowserStack session registered');
                }

                // Add screenshot method that uses VisualAnalysisHelper
                page.takeScreenshotForBrowserStack = async (name, options = {}) => {
                    const { VisualAnalysisHelper } = require('../../src/utils/visual-analisys-helper');

                    const testName = options.testName || base.test.info().title || 'unknown';

                    // Add platform info to options
                    const enhancedOptions = {
                        ...options,
                        cdpMeta: {
                            _requestedPlatform: process.env.PLATFORM || 'unknown',
                            _actualPlatform: process.env.PLATFORM || 'unknown',
                            _requestedDeviceType: null,
                            browserStackSession: process.env.BROWSERSTACK_SESSION_ID
                        }
                    };

                    return await VisualAnalysisHelper.captureAndUploadScreenshot(
                        page,
                        testName,
                        name,
                        enhancedOptions
                    );
                };

                // Add waitForVisualStability method to page
                page.waitForVisualStability = async (options = {}) => {
                    return await bsHelper.waitForVisualStability(page, options);
                };

                // Set viewport size for mobile devices
                const platform = process.env.PLATFORM || '';
                const isMobile = platform.includes('galaxy') ||
                               platform.includes('iphone') ||
                               platform.includes('android') ||
                               platform.includes('ios') ||
                               process.env.IS_MOBILE === 'true';

                if (isMobile && !isBrowserStackSdkMode) {
                    console.log('[Unified Fixture] Setting mobile viewport size');
                    await page.setViewportSize({ width: 375, height: 812 });
                }

                await use(page);

                // After test completion, attempt to update test status
                if (process.env.BROWSERSTACK_SESSION_ID && !isBrowserStackSdkMode) {
                    try {
                        // Try to determine test status from test info
                        const testInfo = base.test.info();
                        const status = testInfo.status === 'passed' ? 'passed' : 'failed';
                        const reason = testInfo.error ? testInfo.error.message : 'Test completed';

                        await bsHelper.setTestStatus(status, reason);
                    } catch (error) {
                        console.warn('Could not update BrowserStack test status:', error.message);
                    }
                }
            },

            // Add device info fixture
            deviceInfo: async ({}, use) => {
                // Provide device information to tests
                const platform = process.env.PLATFORM || '';
                const isMobile = platform.includes('galaxy') ||
                               platform.includes('iphone') ||
                               platform.includes('android') ||
                               platform.includes('ios') ||
                               process.env.IS_MOBILE === 'true';

                // Map platform IDs to friendly names
                const getPlatformName = (platformId) => {
                    const platformMap = {
                        'windows-chrome': 'Chrome on Windows',
                        'mac-safari': 'Safari on Mac',
                        'firefox': 'Firefox',
                        'samsung-galaxy-s23': 'Samsung Galaxy S23',
                        'iphone-14': 'iPhone 14'
                    };
                    return platformMap[platformId] || platformId;
                };

                const info = {
                    platform,
                    isMobile,
                    deviceName: getPlatformName(platform)
                };

                await use(info);
            }
        });

        return baseTest;
    }
}

// Export the unified test fixture
module.exports = {
    test: UnifiedFixture.getFixtures(),
    expect: base.expect
};
