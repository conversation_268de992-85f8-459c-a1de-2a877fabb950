#!/bin/bash
# Bash script to add test cases to Notion
# Usage: ./add-test-cases-to-notion.sh your_notion_api_key

# Check if API key is provided
if [ -z "$1" ]; then
  echo "Error: Notion API key is required"
  echo "Usage: ./add-test-cases-to-notion.sh your_notion_api_key"
  exit 1
fi

NOTION_API_KEY=$1
TEST_CASE_DATABASE_ID="1e5a8ba3-b0b5-81a4-bfa2-e3b89d6b21d6"
NOTION_API_URL="https://api.notion.com/v1/pages"

# Path to the test cases JSON file
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_CASES_FILE="$SCRIPT_DIR/../docs/apex-shopify-migration-notion-import.json"

# Check if jq is installed
if ! command -v jq &> /dev/null; then
  echo "Error: jq is required but not installed. Please install jq."
  exit 1
fi

# Function to add a test case to Notion
add_test_case_to_notion() {
  local test_case=$1
  local test_case_id=$(echo $test_case | jq -r '.properties.ID.title[0].text.content')
  
  echo "Adding test case: $test_case_id"
  
  # Create the request body
  local body=$(jq -n \
    --argjson test_case "$test_case" \
    --arg database_id "$TEST_CASE_DATABASE_ID" \
    '{
      "parent": {"database_id": $database_id},
      "properties": $test_case.properties
    }')
  
  # Make the API request
  local response=$(curl -s -X POST "$NOTION_API_URL" \
    -H "Authorization: Bearer $NOTION_API_KEY" \
    -H "Content-Type: application/json" \
    -H "Notion-Version: 2022-06-28" \
    -d "$body")
  
  # Check if the request was successful
  if echo "$response" | jq -e '.id' > /dev/null; then
    echo "Successfully added test case: $test_case_id"
  else
    echo "Error adding test case: $test_case_id"
    echo "$response" | jq '.'
  fi
  
  # Add a small delay to avoid rate limiting
  sleep 0.5
}

# Main function to add all test cases
add_all_test_cases() {
  echo "Starting to add test cases to Notion..."
  
  # Read the test cases from the JSON file
  local test_cases=$(jq -c '.test_cases[]' "$TEST_CASES_FILE")
  
  # Loop through each test case
  echo "$test_cases" | while read -r test_case; do
    add_test_case_to_notion "$test_case"
  done
  
  echo "Finished adding test cases to Notion."
}

# Execute the main function
add_all_test_cases
