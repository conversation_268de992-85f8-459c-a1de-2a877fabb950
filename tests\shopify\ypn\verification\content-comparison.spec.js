/**
 * @fileoverview Content comparison tests for YourPetNutrition Shopify site
 */

const { test, expect } = require('@playwright/test');
const { ContentHelper } = require('../../../utils/content-helper');
const { BrowserStackHelper } = require('../../../../src/utils/browserstack/browserstack-helper');
const TestDataManager = require('../../../../tests/data/test-data-manager');

test.describe('YourPetNutrition Content Verification Tests @shopify @ypn', () => {
    let page;
    let bsHelper;
    let testDataManager;
    let shopUrl;
    let shopPassword;
    let expectedContent;

    test.beforeEach(async ({ browser }, testInfo) => {
        // Initialize test data manager and load content mapping
        testDataManager = new TestDataManager();
        testDataManager.initialize('default', 'ypn', process.env.TEST_ENV || 'stage');
        
        // Get configuration from YAML
        const config = testDataManager.getBrandConfig();
        shopUrl = config.url;
        shopPassword = config.password;
        
        // Get content mapping from YAML
        const contentMapping = testDataManager.getContentMapping();
        expectedContent = contentMapping.EXPECTED_CONTENT;
        
        // Create a new context and page for each test
        const context = await browser.newContext();
        page = await context.newPage();
        
        // Initialize BrowserStack helper
        bsHelper = new BrowserStackHelper();
        
        // Navigate to the password-protected store
        await page.goto(shopUrl);
        await page.waitForLoadState('networkidle');
        
        // Enter store password
        await page.fill('input[type="password"]', shopPassword);
        await page.click('button:has-text("Enter")');
        
        // Wait for the store to load
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
    });

    test.afterEach(async ({ }, testInfo) => {
        if (process.env.BROWSERSTACK_SESSION_ID) {
            await bsHelper.downloadArtifacts(process.env.BROWSERSTACK_SESSION_ID);
        }
    });

    test('Homepage Content Verification', async ({ }, testInfo) => {
        // Take a screenshot for reference
        await bsHelper.takeScreenshot(page, 'ypn-homepage-content', {
            testName: testInfo.title,
            fullPage: true,
            type: 'content'
        });
        
        // Verify homepage title
        const title = await page.textContent('h1');
        expect(title).toContain(expectedContent.homepage.title);
        
        // Verify homepage description
        const description = await page.textContent('h1 + p');
        expect(description).toContain(expectedContent.homepage.description);
        
        // Verify trust badges
        for (const badge of expectedContent.trustBadges) {
            await expect(page.locator(`text=${badge}`)).toBeVisible();
        }
    });

    test('Dog Products Content Verification', async ({ }, testInfo) => {
        // Navigate to Shop Dogs page
        await page.click('text=Shop Dogs');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Take a screenshot for reference
        await bsHelper.takeScreenshot(page, 'ypn-shop-dogs-content', {
            testName: testInfo.title,
            fullPage: true,
            type: 'content'
        });
        
        // Verify page title
        const title = await page.textContent('h1');
        expect(title).toBe('Dogs');
        
        // Verify all dog products are displayed
        for (const product of expectedContent.dogProducts) {
            await expect(page.locator(`text=${product}`)).toBeVisible();
        }
        
        // Verify product descriptions and prices
        for (const product of expectedContent.dogProducts) {
            if (expectedContent.productDetails[product]) {
                // Find the product card
                const productCard = page.locator(`h3:has-text("${product}")`).locator('xpath=..');
                
                // Verify price
                if (expectedContent.productDetails[product].price) {
                    const priceText = await productCard.locator('.price').textContent();
                    expect(priceText).toContain(expectedContent.productDetails[product].price);
                }
            }
        }
    });

    test('Cat Products Content Verification', async ({ }, testInfo) => {
        // Navigate to Shop Cats page
        await page.click('text=Shop Cats');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Take a screenshot for reference
        await bsHelper.takeScreenshot(page, 'ypn-shop-cats-content', {
            testName: testInfo.title,
            fullPage: true,
            type: 'content'
        });
        
        // Verify page title
        const title = await page.textContent('h1');
        expect(title).toBe('Cats');
        
        // Verify all cat products are displayed
        for (const product of expectedContent.catProducts) {
            await expect(page.locator(`text=${product}`)).toBeVisible();
        }
    });

    test('Canine Prime Product Content Verification', async ({ }, testInfo) => {
        // Navigate to Shop Dogs page
        await page.click('text=Shop Dogs');
        await page.waitForLoadState('networkidle');
        
        // Click on Canine Prime product
        await page.click('text=Canine Prime');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Take a screenshot for reference
        await bsHelper.takeScreenshot(page, 'ypn-canine-prime-content', {
            testName: testInfo.title,
            fullPage: true,
            type: 'content'
        });
        
        // Verify product title
        const title = await page.textContent('h1');
        expect(title).toBe('Canine Prime');
        
        // Verify product price
        const price = await page.locator('.price').first().textContent();
        expect(price).toContain(expectedContent.productDetails['Canine Prime'].price);
        
        // Click on Description tab
        await page.click('button:has-text("Description")');
        await page.waitForTimeout(500);
        
        // Verify product description
        const description = await page.locator('.product__description').textContent();
        expect(description).toContain(expectedContent.productDetails['Canine Prime'].description);
        
        // Verify purchase options
        await expect(page.locator('text=ONE TIME PURCHASE')).toBeVisible();
        await expect(page.locator('text=SUBSCRIBE AND SAVE')).toBeVisible();
        
        // Verify subscription discount
        const subscriptionOption = page.locator('text=SUBSCRIBE AND SAVE').locator('xpath=..');
        const discountText = await subscriptionOption.locator('.save').textContent();
        expect(discountText).toContain(expectedContent.productDetails['Canine Prime'].subscriptionDiscount);
    });

    test('Denta Soft Product Content Verification', async ({ }, testInfo) => {
        // Navigate to Shop Dogs page
        await page.click('text=Shop Dogs');
        await page.waitForLoadState('networkidle');
        
        // Click on Denta Soft product
        await page.click('text=Denta Soft');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Take a screenshot for reference
        await bsHelper.takeScreenshot(page, 'ypn-denta-soft-content', {
            testName: testInfo.title,
            fullPage: true,
            type: 'content'
        });
        
        // Verify product title
        const title = await page.textContent('h1');
        expect(title).toBe('Denta Soft');
        
        // Verify product price
        const price = await page.locator('.price').first().textContent();
        expect(price).toContain(expectedContent.productDetails['Denta Soft'].price);
        
        // Click on Description tab
        await page.click('button:has-text("Description")');
        await page.waitForTimeout(500);
        
        // Verify product description
        const description = await page.locator('.product__description').textContent();
        expect(description).toContain(expectedContent.productDetails['Denta Soft'].description);
        
        // Verify quantity options
        await expect(page.locator('text=1 jar')).toBeVisible();
        await expect(page.locator('text=3 jars')).toBeVisible();
        await expect(page.locator('text=6 jars')).toBeVisible();
        
        // Verify purchase options
        await expect(page.locator('text=ONE TIME PURCHASE')).toBeVisible();
        await expect(page.locator('text=SUBSCRIBE AND SAVE')).toBeVisible();
        
        // Verify subscription discount
        const subscriptionOption = page.locator('text=SUBSCRIBE AND SAVE').locator('xpath=..');
        const discountText = await subscriptionOption.locator('.save').textContent();
        expect(discountText).toContain(expectedContent.productDetails['Denta Soft'].subscriptionDiscount);
    });

    test('Cart Content Verification', async ({ }, testInfo) => {
        // Navigate to Shop Dogs page
        await page.click('text=Shop Dogs');
        await page.waitForLoadState('networkidle');
        
        // Click on Denta Soft product (since we know it works)
        await page.click('text=Denta Soft');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Click Add to Cart button
        await page.click('button:has-text("Add to cart")');
        await page.waitForTimeout(2000);
        
        // Click on cart icon
        await page.click('.header__icon--cart');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Take a screenshot for reference
        await bsHelper.takeScreenshot(page, 'ypn-cart-content', {
            testName: testInfo.title,
            fullPage: true,
            type: 'content'
        });
        
        // Verify cart title
        const title = await page.textContent('h1');
        expect(title).toBe('Your cart');
        
        // Verify product in cart
        await expect(page.locator('text=Denta Soft')).toBeVisible();
        
        // Verify product price
        const price = await page.locator('.cart-item .price').textContent();
        expect(price).toContain(expectedContent.productDetails['Denta Soft'].price);
        
        // Verify subtotal
        await expect(page.locator('text=Subtotal')).toBeVisible();
        
        // Verify checkout button
        await expect(page.locator('button:has-text("CHECK OUT")')).toBeVisible();
    });

    test('Footer Content Verification', async ({ }, testInfo) => {
        // Scroll to the footer
        await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
        await page.waitForTimeout(500);
        
        // Take a screenshot for reference
        await bsHelper.takeScreenshot(page, 'ypn-footer-content', {
            testName: testInfo.title,
            fullPage: false,
            type: 'content'
        });
        
        // Verify footer links
        await expect(page.locator('footer text=Shop')).toBeVisible();
        await expect(page.locator('footer text=Contact')).toBeVisible();
        await expect(page.locator('footer text=Returns & Refunds')).toBeVisible();
        await expect(page.locator('footer text=Privacy Policy')).toBeVisible();
        await expect(page.locator('footer text=Terms of Use')).toBeVisible();
        
        // Verify social media links
        await expect(page.locator('footer text=Facebook')).toBeVisible();
        await expect(page.locator('footer text=Instagram')).toBeVisible();
        await expect(page.locator('footer text=Twitter')).toBeVisible();
        
        // Verify copyright
        const copyright = await page.locator('footer').textContent();
        expect(copyright).toContain('Your Pet Nutrition. All rights reserved.');
    });
});
