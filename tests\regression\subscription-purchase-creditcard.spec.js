/**
 * @fileoverview Test implementation for TC-037 and TC-002: Subscription Purchase with Credit Card
 * @tags @dss @regression @subscription @creditcard
 */
// This test is tagged with @dss for filtering
// Adding a direct tag for <PERSON><PERSON> to find
// @dss
const { test, expect } = require('../fixtures/critical-flow-fixture');

test.describe('Test Cases TC-037 & TC-002: Subscription Purchase @dss', () => {
    // Store test data at the suite level
    let productData;
    let expectedPrice;
    let expectedFrequency;

    test.beforeEach(async ({ testDataManager }) => {
        // The test data manager should already be initialized by the fixture
        // with the correct brand and environment from environment variables
        // But we'll log the current state to verify
        console.log(`Test data manager state: brand=${testDataManager.brand}, env=${testDataManager.environment}, dataSet=${testDataManager.dataSet}`);

        // Get product data
        productData = testDataManager.getProduct('younger_you_skin_cream');

        // Set expected values
        expectedPrice = productData.options.purchaseTypes.subscription.price;
        expectedFrequency = productData.options.purchaseTypes.subscription.frequencies[0].value;

        console.log('Test data initialized:', {
            product: productData.name,
            productUrl: productData.fullUrl,
            subscriptionPrice: expectedPrice,
            subscriptionFrequency: expectedFrequency
        });
    });

    test('TC-037: Complete a successful subscription purchase using credit card', async ({
        page, pageObjects, emailUtils, testData, testDataManager
    }) => {
        const { productPage, cartPage, checkoutPage, confirmationPage } = pageObjects;

        // 1. Navigate to product page
        await test.step('Navigate to product page', async () => {
            await page.goto(productData.fullUrl);
        });

        // 2. Select subscription & save
        await test.step('Select subscription option', async () => {
            await productPage.selectPurchaseType('subscription');
            await productPage.selectSubscriptionFrequency(expectedFrequency.toString());
            await page.waitForTimeout(500); // Wait for price update

            const currentPrice = await productPage.getCurrentPrice();
            expect(currentPrice).toContain(`£${expectedPrice.toFixed(2)}`);
        });

        // 3. Add to cart
        await test.step('Add product to cart', async () => {
            await productPage.addToCart();
            await cartPage.waitForCartDrawer();
        });

        // 4. Navigate to cart and verify
        await test.step('Verify cart contents', async () => {
            await cartPage.navigateToCart();

            const cartItem = await cartPage.getCartItemDetails(productData.name);
            expect(cartItem.name).toContain(productData.name);
            expect(cartItem.price).toBe(`£${expectedPrice.toFixed(2)}`);
            expect(cartItem.purchaseType).toBe('Subscribe & Save');
            expect(cartItem.subscriptionFrequency).toContain(`${expectedFrequency} Days`);
            expect(cartItem.shipping).toBe('FREE');
        });

        // 5. Proceed to checkout
        await test.step('Proceed to checkout', async () => {
            await cartPage.proceedToCheckout();
            await checkoutPage.waitForCheckoutPage();
        });

        // 6. Fill checkout form
        await test.step('Fill customer information', async () => {
            // Get user data from test data manager
            const userData = testDataManager.getUser('subscription_test');

            await checkoutPage.fillCustomerInfo({
                email: userData.email
            });

            await checkoutPage.fillAddress('billing', {
                firstName: userData.firstName,
                lastName: userData.lastName,
                phone: userData.phone,
                address1: userData.address,
                address2: userData.address2,
                city: userData.city,
                postcode: userData.postcode,
                country: userData.country
            });

            await checkoutPage.useSameAddressForBilling();
            await checkoutPage.continueToShipping();
        });

        // 7. Verify shipping method is free
        await test.step('Verify free shipping for subscription', async () => {
            const shippingMethod = await checkoutPage.getSelectedShippingMethod();
            expect(shippingMethod).toBe('domestic_tracked');

            const shippingCost = await checkoutPage.getShippingCost();
            expect(shippingCost).toBe(0);
        });

        // 8. Select credit card and complete checkout
        await test.step('Complete payment with credit card', async () => {
            // Get payment method data from test data manager
            const paymentMethod = testDataManager.getPaymentMethod('stripe_valid');

            await checkoutPage.selectPaymentMethod('credit_card');
            await checkoutPage.fillCreditCardInfo(paymentMethod);
            await checkoutPage.completeOrder();
        });

        // 9. Verify order confirmation
        await test.step('Verify order confirmation', async () => {
            await confirmationPage.waitForOrderConfirmation();

            const orderDetails = await confirmationPage.getOrderDetails();
            expect(orderDetails.items.length).toBe(1);
            expect(orderDetails.items[0].name).toContain(productData.name);
            expect(orderDetails.items[0].purchaseType).toContain('Subscribe & Save');
            expect(orderDetails.total).toContain(`£${expectedPrice.toFixed(2)}`);

            // Get order number for email verification
            const orderNumber = await confirmationPage.getOrderNumber();
            expect(orderNumber).toBeTruthy();

            // Store for later verification
            testData.orderNumber = orderNumber;

            // Store order data in test data manager for potential future use
            testDataManager.setOrderData(
                orderNumber,
                expectedPrice,
                testDataManager.getUser('subscription_test').email,
                {
                    productName: productData.name,
                    purchaseType: 'subscription',
                    frequency: expectedFrequency
                }
            );
        });

        // 10. Verify confirmation email
        await test.step('Verify confirmation email', async () => {
            const userData = testDataManager.getUser('subscription_test');
            const emailSubject = testDataManager.getOrderConfirmationEmailSubject().replace('%ORDER_NUMBER%', testData.orderNumber);

            const email = await emailUtils.waitForEmail({
                recipient: userData.email,
                subject: emailSubject,
                search_after: new Date(Date.now() - 10 * 60 * 1000).toISOString()
            });

            expect(email).toBeTruthy();

            const emailContent = await emailUtils.getEmailContent(email.id);
            expect(emailContent).toContain(productData.name);
            expect(emailContent).toContain(`£${expectedPrice.toFixed(2)}`);
        });

        // 11. Verify subscription creation email
        await test.step('Verify subscription creation email', async () => {
            const userData = testDataManager.getUser('subscription_test');
            const subscriptionEmailSubject = testDataManager.getEmailTemplate('subscription_created').subject;

            const subscriptionEmail = await emailUtils.waitForEmail({
                recipient: userData.email,
                subject: subscriptionEmailSubject,
                search_after: new Date(Date.now() - 10 * 60 * 1000).toISOString()
            });

            expect(subscriptionEmail).toBeTruthy();

            const emailContent = await emailUtils.getEmailContent(subscriptionEmail.id);
            expect(emailContent).toContain(`${expectedFrequency} days`);
            expect(emailContent).toContain(`£${expectedPrice.toFixed(2)}`);
        });

        // 12. Verify welcome email
        await test.step('Verify welcome email', async () => {
            const userData = testDataManager.getUser('subscription_test');
            const welcomeEmailSubject = testDataManager.getEmailTemplate('welcome_email').subject;

            const welcomeEmail = await emailUtils.waitForEmail({
                recipient: userData.email,
                subject: welcomeEmailSubject,
                search_after: new Date(Date.now() - 10 * 60 * 1000).toISOString()
            });

            expect(welcomeEmail).toBeTruthy();
        });
    });
});
