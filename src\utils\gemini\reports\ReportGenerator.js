/**
 * @fileoverview Report generation utilities for Gemini analysis
 */

const fs = require('fs').promises;
const path = require('path');

class ReportGenerator {
    /**
     * Generate HTML report from analysis results
     * @param {Object} results Analysis results
     * @returns {string} HTML report content
     */
    generateHtmlReport(results) {
        const template = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Visual Analysis Report</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .section { margin-bottom: 30px; }
                    .screenshot { margin: 20px 0; }
                    .screenshot img { max-width: 100%; border: 1px solid #ddd; }
                    .analysis { background: #f5f5f5; padding: 15px; border-radius: 5px; }
                    .error { color: #d32f2f; }
                    .warning { color: #f57c00; }
                    .success { color: #388e3c; }
                </style>
            </head>
            <body>
                <h1>Visual Analysis Report</h1>
                <div class="summary section">
                    <h2>Summary</h2>
                    ${this.generateSummaryHtml(results.summary)}
                </div>
                <div class="visual-analysis section">
                    <h2>Visual Analysis</h2>
                    ${this.generateVisualAnalysisHtml(results.details.visual)}
                </div>
                <div class="performance section">
                    <h2>Performance Analysis</h2>
                    ${this.generatePerformanceAnalysisHtml(results.details.performance)}
                </div>
                ${results.details.errors.length > 0 ? `
                    <div class="errors section">
                        <h2>Errors</h2>
                        ${this.generateErrorsHtml(results.details.errors)}
                    </div>
                ` : ''}
            </body>
            </html>
        `;

        return template;
    }

    /**
     * Generate JSON report from analysis results
     * @param {Object} results Analysis results
     * @returns {string} JSON report content
     */
    generateJsonReport(results) {
        return JSON.stringify(results, null, 2);
    }

    /**
     * Generate summary section HTML
     * @private
     */
    generateSummaryHtml(summary) {
        return `
            <p>Total Visual Analyses: ${summary.visualAnalyses}</p>
            <p>Total Performance Entries: ${summary.performanceEntries}</p>
            <p>Total Errors: ${summary.errors}</p>
            <p>Analysis Timestamp: ${new Date(summary.timestamp).toLocaleString()}</p>
        `;
    }

    /**
     * Generate visual analysis section HTML
     * @private
     */
    generateVisualAnalysisHtml(visualResults) {
        return visualResults.map(result => `
            <div class="screenshot">
                <img src="${result.screenshot}" alt="Screenshot">
                <div class="analysis">
                    <pre>${result.analysis}</pre>
                </div>
                <p class="timestamp">Analyzed: ${new Date(result.timestamp).toLocaleString()}</p>
            </div>
        `).join('\n');
    }

    /**
     * Generate performance analysis section HTML
     * @private
     */
    generatePerformanceAnalysisHtml(performanceResults) {
        return performanceResults.map(result => `
            <div class="performance-entry">
                <div class="analysis">
                    <pre>${result.analysis}</pre>
                </div>
                <p class="timestamp">Analyzed: ${new Date(result.timestamp).toLocaleString()}</p>
            </div>
        `).join('\n');
    }

    /**
     * Generate errors section HTML
     * @private
     */
    generateErrorsHtml(errors) {
        return errors.map(error => `
            <div class="error-entry">
                <h3>Error: ${error.error}</h3>
                <div class="analysis">
                    <pre>${error.analysis}</pre>
                </div>
                <p class="timestamp">Occurred: ${new Date(error.timestamp).toLocaleString()}</p>
            </div>
        `).join('\n');
    }

    /**
     * Save report to file
     * @param {string} content Report content
     * @param {string} outputPath Output file path
     * @param {string} format Report format ('html' or 'json')
     */
    async saveReport(content, outputPath, format = 'html') {
        try {
            await fs.mkdir(path.dirname(outputPath), { recursive: true });
            await fs.writeFile(outputPath, content);
        } catch (error) {
            console.error('Error saving report:', error);
            throw error;
        }
    }
}

module.exports = { ReportGenerator }; 