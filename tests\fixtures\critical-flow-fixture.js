const { test: baseTest, expect } = require('./unified-fixture');
// Shop page objects
const { ProductPage } = require('../../src/pages/shop/ProductPage');
const { CartPage } = require('../../src/pages/shop/CartPage');
const { CheckoutPage } = require('../../src/pages/shop/CheckoutPage.js');
const { ConfirmationPage } = require('../../src/pages/shop/ConfirmationPage.js');
const { PayPalPage } = require('../../src/pages/shop/PayPalPage.js');

// Account page objects
const { LoginPage } = require('../../src/pages/account/LoginPage.js');
const { DashboardPage } = require('../../src/pages/account/DashboardPage.js');

// General page objects
const { HomePage } = require('../../src/pages/general/HomePage.js');

// Utilities
const DatabaseUtils = require('../../src/utils/DatabaseUtils');
const EmailUtils = require('../../src/utils/EmailUtils');

/**
 * Extended test fixture for critical e-commerce flows
 */
exports.test = baseTest.extend({
    // Page objects
    pageObjects: async ({ page }, use) => {
        const pageObjects = {
            // Shop pages
            productPage: new ProductPage(page),
            cartPage: new CartPage(page),
            checkoutPage: new CheckoutPage(page),
            confirmationPage: new ConfirmationPage(page),
            paypalPage: new PayPalPage(page),

            // Account pages
            loginPage: new LoginPage(page),
            dashboardPage: new DashboardPage(page),

            // General pages
            homePage: new HomePage(page)
        };
        await use(pageObjects);
    },

    // Database utils
    dbUtils: async ({}, use) => {
        await DatabaseUtils.createConnection();
        await use(DatabaseUtils);
        await DatabaseUtils.closeConnection();
    },

    // Email utils
    emailUtils: async ({}, use) => {
        const emailUtils = new EmailUtils();
        await use(emailUtils);
    },

    // Test data
    testData: async ({ testDataManager }, use) => {
        // Get test data from the centralized TestDataManager
        const user = testDataManager.getUser('default');
        const paymentMethod = testDataManager.getPaymentMethod('stripe_valid');
        const paypalMethod = testDataManager.getPaymentMethod('paypal') || {
            email: '<EMAIL>',
            password: process.env.PAYPAL_PASSWORD
        };

        // Default test data for critical flows, using centralized data
        const testData = {
            customer: user,
            paypal: paypalMethod,
            creditCard: {
                number: paymentMethod.number,
                expiry: paymentMethod.expiry,
                cvc: paymentMethod.cvc
            }
        };
        await use(testData);
    }
});

// Export expect for assertions
exports.expect = expect;
