/**
 * @fileoverview Page Object Factory
 *
 * Centralized factory for creating and managing page objects with lazy loading
 * and caching to eliminate code duplication across fixtures and tests.
 *
 * Features:
 * - Lazy loading: Page objects created only when needed
 * - Caching: Reuse page objects within the same test
 * - Organized structure: Logical grouping by functionality
 * - Backward compatibility: getAll() method for existing tests
 */

// Shop page objects
const { ProductPage } = require('../../../src/pages/shop/ProductPage');
const { CartPage } = require('../../../src/pages/shop/CartPage');
const { CheckoutPage } = require('../../../src/pages/shop/CheckoutPage');
const { ConfirmationPage } = require('../../../src/pages/shop/ConfirmationPage');
const { PayPalPage } = require('../../../src/pages/shop/PayPalPage');
const { SalesFunnelInitialCheckoutPage } = require('../../../src/pages/shop/SalesFunnelInitialCheckoutPage');
const { SalesFunnelPaymentPage } = require('../../../src/pages/shop/SalesFunnelPaymentPage');
const { SalesFunnelUpsellPage } = require('../../../src/pages/shop/SalesFunnelUpsellPage');
const { SalesFunnelConfirmationPage } = require('../../../src/pages/shop/SalesFunnelConfirmationPage');

// Account page objects
const { LoginPage } = require('../../../src/pages/account/LoginPage');
const { DashboardPage } = require('../../../src/pages/account/DashboardPage');

// Admin page objects
const { AdminLoginPage } = require('../../../src/pages/admin/AdminLoginPage');
const { AdminOrdersPage } = require('../../../src/pages/admin/AdminOrdersPage');
const { AdminSalesFunnelPage } = require('../../../src/pages/admin/AdminSalesFunnelPage');

// General page objects
const { HomePage } = require('../../../src/pages/general/HomePage');

/**
 * Page Object Factory with lazy loading and caching
 */
class PageObjectFactory {
    constructor(page) {
        this.page = page;
        this._cache = new Map();

        console.log('[PageObjectFactory] Initialized with lazy loading and caching');
    }

    /**
     * Get shop-related page objects
     * @returns {Object} Shop page objects
     */
    get shop() {
        if (!this._cache.has('shop')) {
            console.log('[PageObjectFactory] Creating shop page objects');
            this._cache.set('shop', {
                product: new ProductPage(this.page),
                cart: new CartPage(this.page),
                checkout: new CheckoutPage(this.page),
                confirmation: new ConfirmationPage(this.page),
                paypal: new PayPalPage(this.page)
            });
        }
        return this._cache.get('shop');
    }

    /**
     * Get account-related page objects
     * @returns {Object} Account page objects
     */
    get account() {
        if (!this._cache.has('account')) {
            console.log('[PageObjectFactory] Creating account page objects');
            this._cache.set('account', {
                login: new LoginPage(this.page),
                dashboard: new DashboardPage(this.page)
            });
        }
        return this._cache.get('account');
    }

    /**
     * Get admin-related page objects
     * @returns {Object} Admin page objects
     */
    get admin() {
        if (!this._cache.has('admin')) {
            console.log('[PageObjectFactory] Creating admin page objects');
            this._cache.set('admin', {
                login: new AdminLoginPage(this.page),
                orders: new AdminOrdersPage(this.page),
                salesFunnel: new AdminSalesFunnelPage(this.page)
            });
        }
        return this._cache.get('admin');
    }

    /**
     * Get sales funnel-related page objects
     * @returns {Object} Sales funnel page objects
     */
    get salesFunnel() {
        if (!this._cache.has('salesFunnel')) {
            console.log('[PageObjectFactory] Creating sales funnel page objects');
            this._cache.set('salesFunnel', {
                initialCheckout: new SalesFunnelInitialCheckoutPage(this.page),
                payment: new SalesFunnelPaymentPage(this.page),
                upsell: new SalesFunnelUpsellPage(this.page),
                confirmation: new SalesFunnelConfirmationPage(this.page)
            });
        }
        return this._cache.get('salesFunnel');
    }

    /**
     * Get general page objects
     * @returns {Object} General page objects
     */
    get general() {
        if (!this._cache.has('general')) {
            console.log('[PageObjectFactory] Creating general page objects');
            this._cache.set('general', {
                home: new HomePage(this.page)
            });
        }
        return this._cache.get('general');
    }

    /**
     * Get all page objects in the legacy format for backward compatibility
     * This method maintains compatibility with existing tests that expect
     * the flat structure from critical-flow-fixture and purchase-fixtures
     *
     * @returns {Object} All page objects in legacy flat structure
     */
    getAll() {
        console.log('[PageObjectFactory] Providing legacy flat structure for backward compatibility');

        return {
            // Shop pages - maintain existing naming
            productPage: this.shop.product,
            cartPage: this.shop.cart,
            checkoutPage: this.shop.checkout,
            confirmationPage: this.shop.confirmation,
            paypalPage: this.shop.paypal,

            // Account pages - maintain existing naming
            loginPage: this.account.login,
            dashboardPage: this.account.dashboard,

            // Admin pages - maintain existing naming
            adminLoginPage: this.admin.login,
            adminOrdersPage: this.admin.orders,
            adminSalesFunnelPage: this.admin.salesFunnel,

            // General pages - maintain existing naming
            homePage: this.general.home,

            // Sales funnel pages - maintain existing naming
            salesFunnelInitialCheckoutPage: this.salesFunnel.initialCheckout,
            salesFunnelPaymentPage: this.salesFunnel.payment,
            salesFunnelUpsellPage: this.salesFunnel.upsell,
            salesFunnelConfirmationPage: this.salesFunnel.confirmation
        };
    }

    /**
     * Get a specific page object by path
     * @param {string} path - Dot notation path (e.g., 'shop.product', 'admin.login')
     * @returns {Object} The requested page object
     */
    getByPath(path) {
        const parts = path.split('.');
        let current = this;

        for (const part of parts) {
            current = current[part];
            if (!current) {
                throw new Error(`Page object not found at path: ${path}`);
            }
        }

        return current;
    }

    /**
     * Check if a page object group is cached
     * @param {string} group - Group name (shop, account, admin, etc.)
     * @returns {boolean} True if cached
     */
    isCached(group) {
        return this._cache.has(group);
    }

    /**
     * Clear cache for a specific group or all groups
     * @param {string} [group] - Group to clear, or undefined to clear all
     */
    clearCache(group = null) {
        if (group) {
            this._cache.delete(group);
            console.log(`[PageObjectFactory] Cleared cache for group: ${group}`);
        } else {
            this._cache.clear();
            console.log('[PageObjectFactory] Cleared all cache');
        }
    }

    /**
     * Get cache statistics for debugging
     * @returns {Object} Cache statistics
     */
    getCacheStats() {
        return {
            size: this._cache.size,
            groups: Array.from(this._cache.keys()),
            totalPageObjects: Array.from(this._cache.values()).reduce((total, group) => {
                return total + Object.keys(group).length;
            }, 0)
        };
    }

    /**
     * Preload specific page object groups
     * @param {string[]} groups - Groups to preload
     */
    preload(groups = []) {
        console.log(`[PageObjectFactory] Preloading groups: ${groups.join(', ')}`);

        for (const group of groups) {
            if (this[group]) {
                // Access the getter to trigger creation
                this[group];
            } else {
                console.warn(`[PageObjectFactory] Unknown group for preloading: ${group}`);
            }
        }
    }

    /**
     * Get available page object groups
     * @returns {string[]} Available group names
     */
    getAvailableGroups() {
        return ['shop', 'account', 'admin', 'salesFunnel', 'general'];
    }

    // Individual page object getters for direct access (used by helpers)

    /**
     * Get product page
     * @returns {ProductPage} Product page object
     */
    getProductPage() {
        return this.shop.product;
    }

    /**
     * Get cart page
     * @returns {CartPage} Cart page object
     */
    getCartPage() {
        return this.shop.cart;
    }

    /**
     * Get checkout page
     * @returns {CheckoutPage} Checkout page object
     */
    getCheckoutPage() {
        return this.shop.checkout;
    }

    /**
     * Get confirmation page
     * @returns {ConfirmationPage} Confirmation page object
     */
    getConfirmationPage() {
        return this.shop.confirmation;
    }

    /**
     * Get PayPal page
     * @returns {PayPalPage} PayPal page object
     */
    getPayPalPage() {
        return this.shop.paypal;
    }

    /**
     * Get admin login page
     * @returns {AdminLoginPage} Admin login page object
     */
    getAdminLoginPage() {
        return this.admin.login;
    }

    /**
     * Get admin orders page
     * @returns {AdminOrdersPage} Admin orders page object
     */
    getAdminOrdersPage() {
        return this.admin.orders;
    }

    /**
     * Get admin sales funnel page
     * @returns {AdminSalesFunnelPage} Admin sales funnel page object
     */
    getAdminSalesFunnelPage() {
        return this.admin.salesFunnel;
    }

    /**
     * Get sales funnel initial checkout page
     * @returns {SalesFunnelInitialCheckoutPage} Sales funnel initial checkout page object
     */
    getSalesFunnelInitialCheckoutPage() {
        return this.salesFunnel.initialCheckout;
    }

    /**
     * Get sales funnel payment page
     * @returns {SalesFunnelPaymentPage} Sales funnel payment page object
     */
    getSalesFunnelPaymentPage() {
        return this.salesFunnel.payment;
    }

    /**
     * Get sales funnel upsell page
     * @returns {SalesFunnelUpsellPage} Sales funnel upsell page object
     */
    getSalesFunnelUpsellPage() {
        return this.salesFunnel.upsell;
    }

    /**
     * Get sales funnel confirmation page
     * @returns {SalesFunnelConfirmationPage} Sales funnel confirmation page object
     */
    getSalesFunnelConfirmationPage() {
        return this.salesFunnel.confirmation;
    }

    /**
     * Get login page
     * @returns {LoginPage} Login page object
     */
    getLoginPage() {
        return this.account.login;
    }

    /**
     * Get dashboard page
     * @returns {DashboardPage} Dashboard page object
     */
    getDashboardPage() {
        return this.account.dashboard;
    }

    /**
     * Get home page
     * @returns {HomePage} Home page object
     */
    getHomePage() {
        return this.general.home;
    }
}

module.exports = { PageObjectFactory };
