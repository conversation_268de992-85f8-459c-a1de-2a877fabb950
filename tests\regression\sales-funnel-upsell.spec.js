/**
 * @fileoverview Test implementation for TC-003: Purchase Funnel with <PERSON>sell
 * @tags @regression @sales_funnel @upsell
 */
const { test, expect } = require('../fixtures/workflows/sales-funnel-flow-fixture');

test.describe('Critical Flow: Sales Funnel with Upsell', () => {
    test('TC-003: Complete a sales funnel flow including an upsell', async ({
        page, context, salesFunnelPages, testData
    }) => {
        // 1. Admin login
        await test.step('Login to admin panel', async () => {
            await salesFunnelPages.adminLogin.login(process.env.ADMIN_USER, process.env.ADMIN_PASSWORD);
        });

        // 2. Navigate to sales funnel items
        await test.step('Navigate to sales funnel items', async () => {
            await salesFunnelPages.adminSalesFunnel.navigate();

            // Get sales funnel link
            const funnelLink = await salesFunnelPages.adminSalesFunnel.getFunnelLink('demo-dsv-1');
            expect(funnelLink).toBeTruthy();

            // Store for checkout
            testData.funnelLink = funnelLink;
        });

        // 3. Open sales funnel in new incognito window
        await test.step('Open sales funnel in new context', async () => {
            // Create new incognito context
            const checkoutContext = await context.browser().newContext();
            const checkoutPage = await checkoutContext.newPage();

            // Store page and context for later steps
            testData.checkoutContext = checkoutContext;
            testData.checkoutPage = checkoutPage;

            // Navigate to funnel
            await checkoutPage.goto(testData.funnelLink);

            // Create the initial checkout page object and import the class from our fixture
            const { SalesFunnelInitialCheckoutPage } = require('../../src/pages/shop');
            salesFunnelPages.initialCheckout = new SalesFunnelInitialCheckoutPage(checkoutPage);
            await salesFunnelPages.initialCheckout.waitForPageLoad();
        });

        // 4. Fill customer information on the initial checkout page
        await test.step('Fill customer information', async () => {

            // Fill customer email
            await salesFunnelPages.initialCheckout.fillEmail(testData.customer.email.replace('test', '0917-with-upsell'));

            // Fill billing address
            await salesFunnelPages.initialCheckout.fillBillingAddress({
                firstName: testData.customer.firstName,
                lastName: testData.customer.lastName,
                phone: testData.customer.phone,
                address1: testData.customer.address1,
                address2: testData.customer.address2,
                city: testData.customer.city,
                postcode: testData.customer.postcode,
                country: testData.customer.country
            });

            // Use same address for shipping
            await salesFunnelPages.initialCheckout.useSameAddressForShipping(true);

            // Continue to shipping
            await salesFunnelPages.initialCheckout.continueToShipping();
        });

        // 5. Fill shipping and payment information
        await test.step('Fill shipping and payment information', async () => {
            // Create the payment page object and import the class from our fixture
            const { SalesFunnelPaymentPage } = require('../../src/pages/shop');
            salesFunnelPages.payment = new SalesFunnelPaymentPage(testData.checkoutPage);
            await salesFunnelPages.payment.waitForPageLoad();

            // Get order summary
            const orderSummary = await salesFunnelPages.payment.getOrderSummary();
            console.log('Order summary:', orderSummary);

            // Select shipping method (first available)
            await salesFunnelPages.payment.selectShippingMethod();

            // Continue to payment
            await salesFunnelPages.payment.continueToPayment();

            // Select payment method
            await salesFunnelPages.payment.selectPaymentMethod('creditCard');

            // Fill credit card information
            await salesFunnelPages.payment.fillCreditCardInfo({
                number: testData.creditCard.number,
                expiry: testData.creditCard.expiry,
                cvc: testData.creditCard.cvc
            });

            // Complete the order
            await salesFunnelPages.payment.completeOrder();
        });

        // 6. Handle upsell if present
        await test.step('Handle upsell if present', async () => {
            // Create the upsell page object and import the class from our fixture
            const { SalesFunnelUpsellPage } = require('../../src/pages/shop');
            salesFunnelPages.upsell = new SalesFunnelUpsellPage(testData.checkoutPage);

            // Check if we're on an upsell page
            const isUpsellPage = await salesFunnelPages.upsell.isUpsellPage();

            if (isUpsellPage) {
                console.log('Upsell page detected');
                await salesFunnelPages.upsell.waitForPageLoad();

                // Get upsell product details
                const upsellProduct = await salesFunnelPages.upsell.getProductDetails();
                console.log('Upsell product:', upsellProduct);

                // Accept the upsell
                await salesFunnelPages.upsell.acceptUpsell();
                testData.upsellAccepted = true;
            } else {
                console.log('No upsell page detected, continuing to confirmation');
                testData.upsellAccepted = false;
            }
        });

        // 7. Verify order confirmation
        await test.step('Verify order confirmation', async () => {
            // Create the confirmation page object and import the class from our fixture
            const { SalesFunnelConfirmationPage } = require('../../src/pages/shop');
            salesFunnelPages.confirmation = new SalesFunnelConfirmationPage(testData.checkoutPage);

            // Wait for confirmation page to load
            await salesFunnelPages.confirmation.waitForOrderConfirmation();

            // Get order number
            const orderNumber = await salesFunnelPages.confirmation.getOrderNumber();
            expect(orderNumber).toBeTruthy();
            console.log(`Order number: ${orderNumber}`);

            // Get order details
            const orderDetails = await salesFunnelPages.confirmation.getOrderDetails();
            console.log('Order details:', orderDetails);

            // Verify initial product
            expect(orderDetails.initialProduct.name).toBeTruthy();

            // If upsell was accepted, verify upsell product
            if (testData.upsellAccepted) {
                expect(orderDetails.upsellProduct).toBeTruthy();
                if (orderDetails.upsellProduct) {
                    expect(orderDetails.upsellProduct.name).toBeTruthy();
                }
            }

            // Verify total price
            expect(orderDetails.total).toBeTruthy();

            // Get customer details
            const customerDetails = await salesFunnelPages.confirmation.getCustomerDetails();
            console.log('Customer details:', customerDetails);

            // Get shipping details
            const shippingDetails = await salesFunnelPages.confirmation.getShippingDetails();
            console.log('Shipping details:', shippingDetails);

            // Verify Dark Spot Vanish is in the order
            const hasMainProduct = await salesFunnelPages.confirmation.verifyProductInOrder('Dark Spot');
            expect(hasMainProduct).toBe(true);
        });
    });
});
