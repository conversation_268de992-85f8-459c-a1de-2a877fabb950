const { test, expect } = require('@playwright/test');
const { ProductPage } = require('../../src/pages/shop/ProductPage');
const { TestHelper } = require('../common/helpers/test-helper');
const PercyHelper = {
    captureSnapshot: async (page, name) => {
        console.log(`Taking snapshot: ${name}`);
        await page.screenshot({ path: `./screenshots/${name.replace(/\s+/g, '-')}.png` });
    },
    captureResponsiveSnapshots: async (page, name) => {
        console.log(`Taking responsive snapshots: ${name}`);
        // Capture desktop view
        await page.setViewportSize({ width: 1280, height: 800 });
        await page.screenshot({ path: `./screenshots/${name.replace(/\s+/g, '-')}-desktop.png` });
        
        // Capture tablet view
        await page.setViewportSize({ width: 768, height: 1024 });
        await page.screenshot({ path: `./screenshots/${name.replace(/\s+/g, '-')}-tablet.png` });
        
        // Capture mobile view
        await page.setViewportSize({ width: 375, height: 667 });
        await page.screenshot({ path: `./screenshots/${name.replace(/\s+/g, '-')}-mobile.png` });
    }
};
require('dotenv').config();

test.describe('Dark Spot Vanish Product Page Comparison', () => {
    let baselinePage;
    let shopifyPage;

    test.beforeEach(async ({ browser }) => {
        const baselineContext = await browser.newContext();
        const shopifyContext = await browser.newContext();
        
        const baselinePg = await baselineContext.newPage();
        const shopifyPg = await shopifyContext.newPage();
        
        baselinePage = new ProductPage(baselinePg);
        shopifyPage = new ProductPage(shopifyPg);
    });

    test('Compare Product Titles @visual', async ({ page }) => {
        // Navigate to both pages
        await baselinePage.navigateToBaseline();
        await shopifyPage.navigateToShopify();

        // Take Percy snapshots
        await PercyHelper.captureSnapshot(baselinePage.page, 'Baseline - Product Title View');
        await PercyHelper.captureSnapshot(shopifyPage.page, 'Shopify - Product Title View');

        // Compare titles
        const baselineTitle = await baselinePage.getProductTitle();
        const shopifyTitle = await shopifyPage.getProductTitle();

        expect(baselineTitle).toBe(shopifyTitle);
    });

    test('Compare Mobile Layout @visual', async ({ page }) => {
        const productPage = new ProductPage(page);
        
        // Test baseline version
        await productPage.navigateToBaseline();
        await PercyHelper.captureResponsiveSnapshots(page, 'Baseline Version');

        // Test Shopify version
        await productPage.navigateToShopify();
        await PercyHelper.captureResponsiveSnapshots(page, 'Shopify Version');
    });

    test('Compare Product Sections @visual', async ({ page }) => {
        const productPage = new ProductPage(page);

        // Capture baseline version sections
        await productPage.navigateToBaseline();
        await PercyHelper.captureSnapshot(page, 'Baseline - Key Ingredients');
        await PercyHelper.captureSnapshot(page, 'Baseline - Guarantee Section');

        // Capture Shopify version sections
        await productPage.navigateToShopify();
        await PercyHelper.captureSnapshot(page, 'Shopify - Key Ingredients');
        await PercyHelper.captureSnapshot(page, 'Shopify - Guarantee Section');
    });

    test('Compare Pricing Structure', async ({ page }) => {
        await baselinePage.navigateToBaseline();
        await shopifyPage.navigateToShopify();

        const baselinePrice = await baselinePage.getProductPrice();
        const shopifyPrice = await shopifyPage.getProductPrice();

        // Log prices for debugging
        await TestHelper.logTestInfo({ title: 'Price Comparison' }, 
            `Baseline: ${baselinePrice}, Shopify: ${shopifyPrice}`);

        // Take screenshots of pricing sections
        await PercyHelper.captureSnapshot(baselinePage.page, 'Baseline - Pricing Section');
        await PercyHelper.captureSnapshot(shopifyPage.page, 'Shopify - Pricing Section');
    });
});

// Special handling for BrowserStack integration
if (process.env.BROWSERSTACK_USERNAME) {
    test.beforeAll(async () => {
        console.log('Running tests on BrowserStack');
    });

    test.afterAll(async () => {
        console.log('Completed BrowserStack test run');
    });
}