/**
 * @fileoverview Visual comparison tests between baseline and Shopify versions
 */

const { test, expect } = require('@playwright/test');
const { ProductPage } = require('../../../../src/pages/shop/ProductPage');
const { TestHelper } = require('../../../common/helpers/test-helper');
const { PRODUCT_CONTENT_RULES } = require('../../../common/fixtures/content-rules');

test.describe('Visual Comparison Tests @shopify @visual', () => {
    let baselinePage;
    let shopifyPage;
    let testHelper;

    test.beforeEach(async ({ browser }, testInfo) => {
        // Create separate contexts for baseline and Shopify
        const baselineContext = await browser.newContext();
        const shopifyContext = await browser.newContext();

        baselinePage = new ProductPage(await baselineContext.newPage());
        shopifyPage = new ProductPage(await shopifyContext.newPage());
        
        testHelper = new TestHelper();
        await Promise.all([
            testHelper.initTest(baselinePage.page, testInfo),
            testHelper.initTest(shopifyPage.page, testInfo)
        ]);
    });

    test.afterEach(async ({ }, testInfo) => {
        if (process.env.BROWSERSTACK_SESSION_ID) {
            await testHelper.browserStack.downloadArtifacts(process.env.BROWSERSTACK_SESSION_ID);
        }
    });

    for (const [productHandle] of Object.entries(PRODUCT_CONTENT_RULES)) {
        test(`Layout comparison - ${productHandle}`, async ({ }, testInfo) => {
            // Navigate to both versions
            await Promise.all([
                baselinePage.navigateToProduct(productHandle),
                shopifyPage.navigateToProduct(productHandle)
            ]);

            // Initial full page layout comparison
            await testHelper.comparePages(
                baselinePage.page,
                shopifyPage.page,
                `${productHandle}-layout`,
                {
                    testName: testInfo.title,
                    type: 'layout',
                    section: 'full-page'
                }
            );

            // Compare product gallery layout
            await testHelper.comparePages(
                baselinePage.page,
                shopifyPage.page,
                `${productHandle}-gallery-layout`,
                {
                    testName: testInfo.title,
                    type: 'layout',
                    section: 'gallery'
                }
            );

            // Compare variant selector layout
            if (await baselinePage.hasElement(baselinePage.selectors.variantSelector)) {
                await testHelper.comparePages(
                    baselinePage.page,
                    shopifyPage.page,
                    `${productHandle}-variants-layout`,
                    {
                        testName: testInfo.title,
                        type: 'layout',
                        section: 'variants'
                    }
                );
            }

            // Compare add to cart section layout
            await testHelper.comparePages(
                baselinePage.page,
                shopifyPage.page,
                `${productHandle}-cart-layout`,
                {
                    testName: testInfo.title,
                    type: 'layout',
                    section: 'add-to-cart'
                }
            );
        });

        test(`Responsive design - ${productHandle}`, async ({ }, testInfo) => {
            const viewports = [
                { width: 1920, height: 1080, name: 'desktop' },
                { width: 1024, height: 768, name: 'tablet' },
                { width: 375, height: 812, name: 'mobile' }
            ];

            for (const viewport of viewports) {
                // Set viewport for both pages
                await Promise.all([
                    baselinePage.page.setViewportSize(viewport),
                    shopifyPage.page.setViewportSize(viewport)
                ]);

                // Navigate to both versions
                await Promise.all([
                    baselinePage.navigateToProduct(productHandle),
                    shopifyPage.navigateToProduct(productHandle)
                ]);

                // Full page responsive comparison
                await testHelper.comparePages(
                    baselinePage.page,
                    shopifyPage.page,
                    `${productHandle}-${viewport.name}`,
                    {
                        testName: testInfo.title,
                        type: 'responsive',
                        viewport: viewport.name,
                        section: 'full-page'
                    }
                );

                // Compare critical sections in responsive mode
                const sections = ['gallery', 'variants', 'add-to-cart'];
                for (const section of sections) {
                    if (await baselinePage.hasElement(baselinePage.selectors[section])) {
                        await testHelper.comparePages(
                            baselinePage.page,
                            shopifyPage.page,
                            `${productHandle}-${viewport.name}-${section}`,
                            {
                                testName: testInfo.title,
                                type: 'responsive',
                                viewport: viewport.name,
                                section
                            }
                        );
                    }
                }
            }
        });

        test(`Interaction sequences - ${productHandle}`, async ({ }, testInfo) => {
            // Define interaction sequences
            const sequences = {
                gallery: [
                    async (page) => await page.hover(page.selectors.productImages),
                    async (page) => await page.click(page.selectors.productImages)
                ],
                variants: [
                    async (page) => await page.hover(page.selectors.variantSelector),
                    async (page) => await page.click(page.selectors.variantSelector),
                    async (page) => await page.selectOption(page.selectors.variantSelector, '1')
                ],
                addToCart: [
                    async (page) => await page.hover(page.selectors.addToCartButton),
                    async (page) => await page.fill(page.selectors.quantityInput, '2'),
                    async (page) => await page.click(page.selectors.addToCartButton)
                ]
            };

            // Navigate to both versions
            await Promise.all([
                baselinePage.navigateToProduct(productHandle),
                shopifyPage.navigateToProduct(productHandle)
            ]);

            // Compare interaction sequences
            for (const [sequence, interactions] of Object.entries(sequences)) {
                // Analyze baseline sequence
                const baselineAnalysis = await testHelper.analyzeSequence(
                    baselinePage.page,
                    interactions,
                    `${productHandle}-${sequence}-baseline`,
                    {
                        testName: testInfo.title,
                        type: 'interaction',
                        sequence
                    }
                );

                // Analyze Shopify sequence
                const shopifyAnalysis = await testHelper.analyzeSequence(
                    shopifyPage.page,
                    interactions,
                    `${productHandle}-${sequence}-shopify`,
                    {
                        testName: testInfo.title,
                        type: 'interaction',
                        sequence
                    }
                );

                // Compare sequence analyses
                expect(shopifyAnalysis.steps.length, `${sequence} sequence should have same number of steps`)
                    .toBe(baselineAnalysis.steps.length);

                for (let i = 0; i < baselineAnalysis.steps.length; i++) {
                    const baselineStep = baselineAnalysis.steps[i];
                    const shopifyStep = shopifyAnalysis.steps[i];

                    expect(shopifyStep.success, `Step ${i + 1} of ${sequence} sequence should succeed`)
                        .toBe(baselineStep.success);
                    expect(shopifyStep.duration, `Step ${i + 1} of ${sequence} sequence should have similar duration`)
                        .toBeWithin(baselineStep.duration * 0.5, baselineStep.duration * 1.5);
                }
            }
        });
    }
}); 