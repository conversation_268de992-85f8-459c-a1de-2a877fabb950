const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

// Define product data based on the ancient_roots product schema
const PRODUCT_DATA = {
    ancient_roots_olive_oil: {
        name: "Ancient Roots Olive Oil",
        slug: "aeons-ancient-roots-olive-oil",
        url_path: "products/aeons-ancient-roots-olive-oil",
        prices: {
            one_time: {
                minimum: 39.99,
                medium: 99.99,
                maximum: 179.99
            },
            subscription: {
                minimum: 35.99,
                medium: 89.99,
                maximum: 161.99
            }
        },
        options: {
            purchase_types: {
                one_time: "One-Time Purchase",
                subscription: "Subscribe & Save"
            },
            quantities: {
                minimum: {
                    fullName: "1 Item",
                    numberOfItems: 1,
                    savings: null
                },
                medium: {
                    fullName: "3 Items",
                    numberOfItems: 3,
                    savings: "15%"
                },
                maximum: {
                    fullName: "6 Items",
                    numberOfItems: 6,
                    savings: "25%"
                }
            },
            subscription_frequencies: [
                "1 Month",
                "2 Months",
                "3 Months",
                "4 Months",
                "5 Months",
                "6 Months"
            ]
        },
        meta: {
            category: "Olive Oil",
            subtitle: "The Original Superfood: Upgraded",
            reviews_count: 0,
            breadcrumbs: ["Home", "Products", "Ancient Roots Olive Oil"]
        },
        content: {
            description: "Our Ancient Roots Olive Oil is harvested from centuries-old olive groves growing in mineral-rich volcanic soil. Cold-pressed within hours of harvest to preserve its exceptional nutritional profile.",
            badges: [
                {
                    name: "Organic",
                    icon: "/images/badges/organic.svg"
                },
                {
                    name: "Cold Pressed",
                    icon: "/images/badges/cold-pressed.svg"
                }
            ],
            trust_badges: [
                {
                    text: "Made in Italy",
                    icon: "/images/trust-badges/made-in-italy.svg"
                },
                {
                    text: "Sustainably Sourced",
                    icon: "/images/trust-badges/sustainable.svg"
                }
            ],
            subscription_benefits: [
                "Save up to 25% on every order",
                "Free shipping on orders over £50",
                "Cancel or pause anytime",
                "Exclusive subscriber benefits"
            ],
            testimonial: {
                quote: "This olive oil has transformed my cooking. The flavor is incredible!",
                author: "Sarah M.",
                role: "Verified Customer"
            },
            details: [
                {
                    title: "Source",
                    content: "Harvested from ancient olive groves in volcanic soil"
                },
                {
                    title: "Process",
                    content: "Cold-pressed within hours of harvest"
                }
            ],
            features: {
                title: "Ancient Wisdom Meets Modern Science",
                subtitle: "The Original Superfood: Upgraded",
                benefits: [
                    "Rich in polyphenols and antioxidants",
                    "Supports heart and brain health",
                    "Anti-inflammatory properties",
                    "Enhances nutrient absorption"
                ]
            },
            certifications: [
                {
                    name: "Organic Certified",
                    icon: "/images/certifications/organic.svg"
                },
                {
                    name: "PDO Certified",
                    icon: "/images/certifications/pdo.svg"
                }
            ],
            faq: [
                {
                    question: "What makes this olive oil special?",
                    answer: "Our olive oil comes from ancient groves growing in volcanic soil, which gives it unique properties and flavor."
                },
                {
                    question: "How should I store the olive oil?",
                    answer: "Store in a cool, dark place away from direct sunlight and heat."
                }
            ],
            related_products: [
                {
                    name: "Sunrise Spark",
                    subtitle: "Morning Drink",
                    description: "Inspired by the morning ritual of the Hadza tribe",
                    url: "/products/aeons-sunrise-spark"
                },
                {
                    name: "Sunset Soothe",
                    subtitle: "Evening Drink",
                    description: "Inspired by a 5,000-year old Chinese ritual",
                    url: "/products/aeons-sunset-soothe"
                }
            ]
        }
    },
    sunrise_spark: {
        name: "Sunrise Spark",
        slug: "aeons-sunrise-spark",
        url_path: "products/aeons-sunrise-spark",
        prices: {
            one_time: {
                minimum: 34.99,
                medium: 89.99,
                maximum: 159.99
            },
            subscription: {
                minimum: 31.49,
                medium: 80.99,
                maximum: 143.99
            }
        },
        options: {
            purchase_types: {
                one_time: "One-Time Purchase",
                subscription: "Subscribe & Save"
            },
            quantities: {
                minimum: {
                    fullName: "1 Item",
                    numberOfItems: 1,
                    savings: null
                },
                medium: {
                    fullName: "3 Items",
                    numberOfItems: 3,
                    savings: "15%"
                },
                maximum: {
                    fullName: "6 Items",
                    numberOfItems: 6,
                    savings: "25%"
                }
            },
            subscription_frequencies: [
                "1 Month",
                "2 Months",
                "3 Months",
                "4 Months",
                "5 Months",
                "6 Months"
            ]
        },
        meta: {
            category: "Morning Drink",
            subtitle: "Inspired by the Morning Ritual of the Hadza Tribe",
            reviews_count: 0,
            breadcrumbs: ["Home", "Products", "Sunrise Spark"]
        },
        content: {
            description: "Start your day with our energizing morning drink, inspired by ancient wisdom and crafted with modern science.",
            badges: [
                {
                    name: "Natural",
                    icon: "/images/badges/natural.svg"
                },
                {
                    name: "Plant-Based",
                    icon: "/images/badges/plant-based.svg"
                }
            ],
            trust_badges: [
                {
                    text: "Ethically Sourced",
                    icon: "/images/trust-badges/ethical.svg"
                },
                {
                    text: "Sustainably Made",
                    icon: "/images/trust-badges/sustainable.svg"
                }
            ],
            subscription_benefits: [
                "Save up to 25% on every order",
                "Free shipping on orders over £50",
                "Cancel or pause anytime",
                "Exclusive subscriber benefits"
            ],
            testimonial: {
                quote: "This has become an essential part of my morning routine. I feel more energized and focused throughout the day.",
                author: "Michael R.",
                role: "Verified Customer"
            },
            details: [
                {
                    title: "Ingredients",
                    content: "Natural herbs and adaptogens inspired by traditional morning rituals"
                },
                {
                    title: "Benefits",
                    content: "Supports natural energy, focus, and vitality"
                }
            ],
            features: {
                title: "Traditional Wisdom for Modern Vitality",
                subtitle: "Start Your Day with Natural Energy",
                benefits: [
                    "Supports sustained energy levels",
                    "Enhances mental clarity",
                    "Promotes natural vitality",
                    "Adaptogenic support"
                ]
            },
            certifications: [
                {
                    name: "Vegan Certified",
                    icon: "/images/certifications/vegan.svg"
                },
                {
                    name: "Non-GMO",
                    icon: "/images/certifications/non-gmo.svg"
                }
            ],
            faq: [
                {
                    question: "When is the best time to take Sunrise Spark?",
                    answer: "For optimal results, take Sunrise Spark first thing in the morning or before your morning workout."
                },
                {
                    question: "Is Sunrise Spark caffeine-free?",
                    answer: "Yes, Sunrise Spark provides natural energy without caffeine, using adaptogenic herbs and traditional botanicals."
                }
            ],
            related_products: [
                {
                    name: "Ancient Roots Olive Oil",
                    subtitle: "Extra Virgin Olive Oil",
                    description: "The Original Superfood: Upgraded",
                    url: "/products/aeons-ancient-roots-olive-oil"
                },
                {
                    name: "Sunset Soothe",
                    subtitle: "Evening Drink",
                    description: "Inspired by a 5,000-year old Chinese ritual",
                    url: "/products/aeons-sunset-soothe"
                }
            ]
        }
    }
};

// Save the data to YAML file
try {
    console.log('Saving product data to YAML file...');
    const yamlStr = yaml.dump(PRODUCT_DATA, {
        indent: 2,
        lineWidth: -1,
        quotingType: '"'
    });

    const outputPath = path.join(__dirname, '..', 'tests', 'data', 'brands', 'aeons', 'products.yml');
    fs.writeFileSync(outputPath, yamlStr, 'utf8');
    console.log('Product data saved successfully!');
} catch (error) {
    console.error('Error saving product data:', error);
}