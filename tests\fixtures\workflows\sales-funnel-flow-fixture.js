/**
 * @fileoverview Sales Funnel Flow Fixture
 *
 * Specialized fixture for sales funnel tests that extends the enhanced
 * unified fixture with sales funnel-specific functionality and helpers.
 * 
 * Features:
 * - Standardized sales funnel flow methods
 * - Admin panel integration for funnel management
 * - Support for upsell and downsell flows
 * - Backend API integration for order completion
 * - Email verification for funnel notifications
 * - Error handling and retry logic
 * - Full backward compatibility with existing sales funnel tests
 */

const { test: enhancedTest, expect } = require('../enhanced-unified-fixture');
const { SalesFunnelFlowHelper } = require('./sales-funnel-flow-helper');

/**
 * Sales funnel flow fixture with standardized sales funnel workflows
 */
const salesFunnelFlowFixture = enhancedTest.extend({
    /**
     * Sales funnel flow helper with standardized methods
     */
    salesFunnelFlow: async ({ pageObjectFactory, testDataHelper, emailHelper, browserStackHelper, deviceHelper }, use) => {
        console.log('[SalesFunnelFlowFixture] Initializing sales funnel flow helper');
        
        const salesFunnelFlow = new SalesFunnelFlowHelper(
            pageObjectFactory,
            testDataHelper,
            emailHelper,
            browserStackHelper,
            deviceHelper
        );
        
        await use(salesFunnelFlow);
    },

    /**
     * Enhanced test data specifically for sales funnel flows
     */
    salesFunnelTestData: async ({ testDataHelper }, use) => {
        // Provide default sales funnel test data
        const testData = testDataHelper.getSalesFunnelTestData();
        await use(testData);
    },

    /**
     * Sales funnel page objects with lazy loading
     */
    salesFunnelPages: async ({ pageObjectFactory }, use) => {
        console.log('[SalesFunnelFlowFixture] Initializing sales funnel page objects');
        
        const salesFunnelPages = {
            // Admin pages
            adminLogin: pageObjectFactory.getAdminLoginPage(),
            adminSalesFunnel: pageObjectFactory.getAdminSalesFunnelPage(),
            adminOrders: pageObjectFactory.getAdminOrdersPage(),
            
            // Sales funnel pages
            initialCheckout: pageObjectFactory.getSalesFunnelInitialCheckoutPage(),
            payment: pageObjectFactory.getSalesFunnelPaymentPage(),
            upsell: pageObjectFactory.getSalesFunnelUpsellPage(),
            confirmation: pageObjectFactory.getSalesFunnelConfirmationPage()
        };
        
        await use(salesFunnelPages);
    },

    /**
     * Sales funnel API helper for backend operations
     */
    salesFunnelApi: async ({ testDataHelper }, use) => {
        const SalesFunnelApi = require('../../../src/api/endpoints/SalesFunnelApi');
        const api = new SalesFunnelApi();
        
        // Initialize with test data configuration
        const config = testDataHelper.getApiConfig();
        if (config) {
            api.setConfig(config);
        }
        
        await use(api);
    },

    /**
     * Subscription API helper for subscription-related operations
     */
    subscriptionApi: async ({ testDataHelper }, use) => {
        const SubscriptionApi = require('../../../src/api/endpoints/SubscriptionApi');
        const api = new SubscriptionApi();
        
        // Initialize with test data configuration
        const config = testDataHelper.getApiConfig();
        if (config) {
            api.setConfig(config);
        }
        
        await use(api);
    },

    /**
     * Database utilities for sales funnel tests
     */
    dbUtils: async ({ testDataHelper }, use) => {
        const { DatabaseUtils } = require('../../../src/utils/database/database-utils');
        
        // Initialize database connection with test configuration
        const dbConfig = testDataHelper.getDatabaseConfig();
        await DatabaseUtils.createConnection(dbConfig);
        
        await use(DatabaseUtils);
        
        // Clean up connection
        await DatabaseUtils.closeConnection();
    },

    /**
     * Enhanced funnel configuration for different test scenarios
     */
    funnelConfig: async ({ testDataHelper }, use) => {
        const config = testDataHelper.getFunnelConfig();
        await use(config);
    },

    /**
     * Backward compatibility: Legacy sales funnel pages structure
     */
    salesFunnelPagesLegacy: async ({ salesFunnelPages }, use) => {
        // Provide the legacy structure for backward compatibility
        await use(salesFunnelPages);
    }
});

// Export the sales funnel flow fixture with expect
module.exports = {
    test: salesFunnelFlowFixture,
    expect: enhancedTest.expect
};
