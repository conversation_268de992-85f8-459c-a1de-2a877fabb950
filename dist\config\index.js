"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.platformConfigs = void 0;
const dotenv = __importStar(require("dotenv"));
// Load environment variables
dotenv.config();
// Platform configurations
exports.platformConfigs = {
    'chrome-windows': {
        name: 'chrome-windows',
        browserName: 'chromium',
        os: 'Windows',
        osVersion: '11',
        viewport: { width: 1920, height: 1080 }
    },
    'android-chrome': {
        name: 'android-chrome',
        browserName: 'chromium',
        os: 'android',
        osVersion: '13.0',
        deviceName: 'Samsung Galaxy S23',
        realMobile: true,
        viewport: { width: 393, height: 851 }
    },
    'ios-safari': {
        name: 'ios-safari',
        browserName: 'webkit',
        os: 'ios',
        osVersion: '16',
        deviceName: 'iPhone 14',
        realMobile: true,
        viewport: { width: 390, height: 844 }
    }
};
// Environment-specific configurations
const environments = {
    dev: {
        baseURL: 'https://aeons-dev.info'
    },
    stage: {
        baseURL: 'https://aeonstest.info'
    },
    prod: {
        baseURL: 'https://aeons.co.uk'
    }
};
// Brand-specific environment URLs
const brandEnvironmentURLs = {
    aeons: {
        dev: 'https://aeons-dev.info',
        stage: 'https://aeonstest.info',
        prod: 'https://aeons.co.uk'
    },
    dss: {
        dev: 'https://dss-dev.info',
        stage: 'https://dss.crm-test.info',
        prod: 'https://drsisterskincare.com'
    },
    ypn: {
        dev: 'https://ypn-dev.info',
        stage: 'https://ypntest.info',
        prod: 'https://yourpetnutrition.com'
    }
};
// Brand-specific configurations
const brandConfigs = {
    aeons: {
        testDir: './tests/regression/aeons'
    },
    dss: {
        testDir: './tests/regression/dss'
    },
    ypn: {
        testDir: './tests/shopify/ypn'
    }
};
// Create configuration
class ConfigurationManager {
    constructor() {
        var _a;
        const env = (process.env.TEST_ENV || 'stage');
        const brand = (process.env.BRAND || 'aeons');
        const testType = (process.env.TEST_TYPE || 'smoke');
        const dataSet = (process.env.TEST_DATA_SET || 'default');
        // Get brand-specific URL for the current environment if available
        const brandSpecificURL = (_a = brandEnvironmentURLs[brand]) === null || _a === void 0 ? void 0 : _a[env];
        // Merge configurations
        const baseConfig = {
            ...environments[env],
            ...brandConfigs[brand],
            brand,
            environment: env,
            testType,
            dataSet,
            testDir: brandConfigs[brand].testDir || './tests',
            // Use brand-specific URL if available, otherwise fall back to environment default
            baseURL: brandSpecificURL || environments[env].baseURL || 'https://aeonstest.info',
            testDataConfig: {
                dataSet: dataSet,
                brand: brand
            }
        };
        // BrowserStack configuration
        const browserstackConfig = {
            username: process.env.BROWSERSTACK_USERNAME || '',
            accessKey: process.env.BROWSERSTACK_ACCESS_KEY || '',
            debug: true,
            networkLogs: true,
            consoleLogs: 'verbose',
            buildName: process.env.BUILD_NAME || `${brand}_${testType}_${env}`,
            projectName: brand.toUpperCase()
        };
        this.config = {
            testDir: baseConfig.testDir,
            timeout: 60000,
            workers: 1,
            use: {
                baseURL: baseConfig.baseURL,
                screenshot: 'only-on-failure',
                trace: 'on-first-retry',
                video: 'on-first-retry',
                testDataManager: {
                    dataSet: baseConfig.dataSet,
                    brand: baseConfig.brand
                }
            },
            projects: this.generateProjects(baseConfig, browserstackConfig)
        };
    }
    generateProjects(baseConfig, bsConfig) {
        return Object.values(exports.platformConfigs).map(platform => ({
            name: platform.name,
            testMatch: this.getTestPattern(baseConfig.testType),
            use: {
                ...platform,
                'browserstack.username': bsConfig.username,
                'browserstack.accessKey': bsConfig.accessKey,
                'browserstack.debug': bsConfig.debug,
                'browserstack.networkLogs': bsConfig.networkLogs,
                'browserstack.consoleLogs': bsConfig.consoleLogs,
                'browserstack.buildName': bsConfig.buildName,
                'browserstack.projectName': bsConfig.projectName
            }
        }));
    }
    getTestPattern(testType) {
        const patterns = {
            smoke: '.*smoke.*\\.spec\\.js',
            visual: '.*visual.*\\.spec\\.js',
            regression: '.*regression.*\\.spec\\.js'
        };
        // Get environment-specific test pattern
        const env = process.env.TEST_ENV;
        if (testType === 'smoke' && env) {
            console.log(`Configuring test pattern for ${env} environment`);
            // Return the base pattern but we'll use the grep in the command line
            // to further filter to the specific environment test tag
        }
        return new RegExp(patterns[testType]);
    }
    static getInstance() {
        if (!ConfigurationManager.instance) {
            ConfigurationManager.instance = new ConfigurationManager();
        }
        return ConfigurationManager.instance;
    }
    getConfig() {
        return this.config;
    }
}
exports.default = ConfigurationManager.getInstance().getConfig();
