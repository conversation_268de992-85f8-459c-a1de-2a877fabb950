/**
 * @fileoverview Test implementation for TC-005: Cancel Abandon
 * @tags @regression @abandoned_cart @high_priority
 */
const { test, expect } = require('../fixtures/enhanced-unified-fixture');
const { AdminLoginPage } = require('../../src/pages/admin/AdminLoginPage.js');
const { AdminSalesFunnelPage } = require('../../src/pages/admin/AdminSalesFunnelPage.js');
const SalesFunnelApi = require('../../src/api/endpoints/SalesFunnelApi.js');
const { SalesFunnelInitialCheckoutPage, SalesFunnelPaymentPage } = require('../../src/pages/shop');

test.describe('Critical Flow: Cancel Abandon', () => {
    test('TC-005: Cancel abandon cart and verify order state', async ({
        page,
        context,
        pageObjects,
        dbUtils,
        emailUtils,
        testData
    }) => {
        // Create API client
        const salesFunnelApi = new SalesFunnelApi();

        // Customize customer email for this test
        const customerEmail = testData.customer.email.replace('test', 'cancel-abandon');

        // 1. Admin login
        await test.step('Login to admin panel', async () => {
            const adminLoginPage = new AdminLoginPage(page);
            await adminLoginPage.login(process.env.ADMIN_USER, process.env.ADMIN_PASSWORD);
        });

        // 2. Navigate to sales funnel items
        await test.step('Navigate to sales funnel items', async () => {
            const adminSalesFunnelPage = new AdminSalesFunnelPage(page);
            await adminSalesFunnelPage.navigate();

            // Get sales funnel link
            const funnelLink = await adminSalesFunnelPage.getFunnelLink('demo-dsv-1');
            expect(funnelLink).toBeTruthy();

            // Store for checkout
            testData.funnelLink = funnelLink;
        });

        // 3. Open sales funnel in new incognito window
        await test.step('Open sales funnel in new context', async () => {
            // Create new incognito context
            const checkoutContext = await context.browser().newContext();
            const checkoutPage = await checkoutContext.newPage();

            // Navigate to funnel
            await checkoutPage.goto(testData.funnelLink);

            // Create sales funnel checkout page objects with the new page
            const salesFunnelCheckoutPageObj = new SalesFunnelInitialCheckoutPage(checkoutPage);
            const salesFunnelPaymentPageObj = new SalesFunnelPaymentPage(checkoutPage);

            // Wait for the page to load
            await salesFunnelCheckoutPageObj.waitForPageLoad();

            // Store for later steps
            testData.checkoutContext = checkoutContext;
            testData.checkoutPage = checkoutPage;
            testData.checkoutPageObj = salesFunnelCheckoutPageObj;
            testData.paymentPageObj = salesFunnelPaymentPageObj;
        });

        // 4. Fill checkout form
        await test.step('Fill checkout form', async () => {
            // Fill customer email
            await testData.checkoutPageObj.fillEmail(customerEmail);

            // Fill billing address
            await testData.checkoutPageObj.fillBillingAddress({
                firstName: testData.customer.firstName,
                lastName: testData.customer.lastName,
                phone: testData.customer.phone,
                address1: testData.customer.address1,
                address2: testData.customer.address2,
                city: testData.customer.city,
                postcode: testData.customer.postcode,
                country: testData.customer.country
            });

            // Use same address for shipping
            await testData.checkoutPageObj.useSameAddressForShipping(true);

            // Continue to shipping
            await testData.checkoutPageObj.continueToShipping();
        });

        // 5. Complete checkout to payment step
        await test.step('Complete checkout to payment step', async () => {
            // Try to select shipping method, but don't fail if it's not available
            try {
                console.log('Attempting to select shipping method...');
                await testData.paymentPageObj.selectShippingMethod('tracked_48');
                console.log('Successfully selected shipping method');

                // Continue to payment section
                await testData.paymentPageObj.continueToPayment();
            } catch (error) {
                console.warn(`Error selecting shipping method: ${error.message}`);
                console.log('Continuing without shipping method selection');
            }

            // Select payment method (credit card)
            await testData.paymentPageObj.selectPaymentMethod('creditCard');
        });

        // 6. Close the checkout window (abandon)
        await test.step('Abandon the checkout', async () => {
            // Close the checkout page to simulate abandonment
            await testData.checkoutPage.close();

            // Wait a moment for the abandonment to register
            await page.waitForTimeout(2000);
        });

        // 7. Process abandoned carts via API
        await test.step('Process abandoned carts via API', async () => {
            const result = await salesFunnelApi.processAbandonedCarts('DSS', '-1minute');

            expect(result.success).toBeTruthy();
            expect(result.message).toContain('processed');

            // Extract order number if available
            if (result.message.match(/order (\d+)/)) {
                const orderNumberMatch = result.message.match(/order (\d+)/);
                testData.orderNumber = orderNumberMatch ? orderNumberMatch[1] : null;
                console.log(`Processed abandoned order #${testData.orderNumber}`);
            }
        });

        // 8. Verify order status in database
        await test.step('Verify order status in database', async () => {
            try {
                console.log(`[Test] Verifying order status in database for email: ${customerEmail}`);

                // Use customer email to find order if order number not available
                if (!testData.orderNumber) {
                    console.log('[Test] No order number available, searching by customer email');
                    const orders = await dbUtils.getOrdersByCustomerEmail(customerEmail);

                    if (orders && orders.length > 0) {
                        console.log(`[Test] Found ${orders.length} orders for customer email`);
                        testData.order = orders[0]; // Most recent order
                        console.log(`[Test] Using most recent order: ${JSON.stringify(testData.order)}`);
                    } else {
                        console.warn('[Test] No orders found for customer email');
                        throw new Error('No orders found for customer email');
                    }
                } else {
                    console.log(`[Test] Searching for order by number: ${testData.orderNumber}`);
                    testData.order = await dbUtils.getOrderByNumber(testData.orderNumber);

                    if (!testData.order) {
                        console.warn(`[Test] Order #${testData.orderNumber} not found in database`);
                        throw new Error(`Order #${testData.orderNumber} not found in database`);
                    } else {
                        console.log(`[Test] Order details: ${JSON.stringify(testData.order)}`);
                    }
                }

                // Verify order exists and has correct status
                expect(testData.order).toBeTruthy();
                expect(testData.order.state).toBe('cancelled');
                expect(testData.order.payment_state).toBe('cancelled');

                console.log('[Test] Order verification successful');
            } catch (error) {
                console.error(`[Test] Error verifying order status: ${error.message}`);
                console.error(error.stack);
                throw error;
            }
        });

        // 9. Execute console command for call center
        await test.step('Execute call center command', async () => {
            // This would simulate running the console command:
            // app:abandon-carts:call-center DSS --last-updated-before=-1second -e prod -v
            const callCenterResult = await salesFunnelApi.callCenterAbandonedCarts('DSS', '-1second');

            expect(callCenterResult.success).toBeTruthy();
            expect(callCenterResult.message).toContain('ConvertKit');
        });
    });
});
