/**
 * @fileoverview Gemini AI analysis prompts
 */

const prompts = {
    visualAnalysis: `
        Analyze this screenshot for visual issues and UI elements. Consider:
        1. Layout and alignment
        2. Color consistency
        3. Text readability
        4. Element spacing
        5. Visual hierarchy
        6. Interactive elements (buttons, forms)
        7. Responsive design issues
        8. Visual bugs or glitches

        Provide a detailed analysis focusing on:
        - Major visual issues that affect user experience
        - Minor visual inconsistencies
        - Suggestions for improvement
        - Accessibility concerns
    `,

    regressionAnalysis: `
        Compare this screenshot with baseline expectations. Focus on:
        1. Unexpected visual changes
        2. Layout shifts
        3. Missing or altered elements
        4. Style inconsistencies
        5. Functional implications of visual changes

        Provide analysis of:
        - Critical differences that may affect functionality
        - Visual regressions that impact user experience
        - Acceptable vs concerning changes
    `,

    responsiveAnalysis: `
        Analyze responsive design implementation across different viewports. Consider:
        1. Layout adaptation
        2. Content scaling
        3. Element reflow
        4. Touch targets on mobile
        5. Navigation patterns
        6. Image scaling and quality
        7. Text readability across devices

        Provide insights on:
        - Responsive breakpoint handling
        - Mobile-specific issues
        - Cross-device consistency
        - Viewport-specific optimizations
    `,

    contentAnalysis: `
        Analyze content for consistency and quality. Check for:
        1. Text accuracy and grammar
        2. Brand voice consistency
        3. Required content presence
        4. Formatting consistency
        5. Link validity
        6. Image alt text
        7. SEO elements

        Report on:
        - Content errors or inconsistencies
        - Missing required elements
        - Formatting issues
        - SEO impact
    `,

    errorAnalysis: `
        Analyze test failure and related artifacts. Consider:
        1. Error messages and stack traces
        2. Browser console logs
        3. Network requests
        4. Visual state at failure
        5. User interactions before failure
        6. System state

        Provide:
        - Root cause analysis
        - Impact assessment
        - Reproduction steps
        - Suggested fixes
    `,

    shopifyComparison: `
        Compare baseline and Shopify screenshots for visual differences. Focus on:
        1. Layout consistency
        2. Content placement
        3. Styling differences
        4. Component behavior
        5. Responsive design implementation
        6. Interactive elements
        7. Brand consistency

        Analyze:
        - Critical visual differences
        - UX impact
        - Brand guideline adherence
        - Potential customer impact
    `,

    performanceAnalysis: `
        Analyze performance metrics and identify issues. Consider:
        1. Page load times
        2. Resource loading
        3. Network requests
        4. CPU utilization
        5. Memory usage
        6. Animation performance
        7. Input responsiveness

        Report on:
        - Performance bottlenecks
        - Resource optimization opportunities
        - User experience impact
        - Recommended improvements
    `
};

module.exports = { prompts }; 