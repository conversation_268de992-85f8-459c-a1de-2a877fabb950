# BrowserStack Credentials
BROWSERSTACK_USERNAME=your_username
BROWSERSTACK_ACCESS_KEY=your_access_key

# Gemini AI Configuration
GEMINI_API_KEY=your_gemini_api_key

# Test Configuration
BASE_URL=https://staging.example.com
BUILD_NAME=Visual_Test_${DATE}
BUILD_ID=${BUILD_NAME}_${RANDOM}

# Test Environment
NODE_ENV=test
CI=false

# Visual Testing Configuration
VISUAL_BASELINE_BRANCH=main
VISUAL_ANALYSIS_TIMEOUT=30000
SCREENSHOT_WAIT_TIMEOUT=5000

# BrowserStack Local
BROWSERSTACK_LOCAL=false
BROWSERSTACK_LOCAL_IDENTIFIER=${BUILD_ID}

# Reporting
REPORT_PORTAL_ENABLED=false
REPORT_PORTAL_ENDPOINT=
REPORT_PORTAL_PROJECT=
REPORT_PORTAL_TOKEN=

# Performance Thresholds
PERFORMANCE_BUDGET_LCP=2500
PERFORMANCE_BUDGET_FID=100
PERFORMANCE_BUDGET_CLS=0.1

# Debug Options
DEBUG=false
PWDEBUG=0 