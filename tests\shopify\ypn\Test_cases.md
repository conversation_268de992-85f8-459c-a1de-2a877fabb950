# YourPetNutrition (YPN) Test Cases

## Overview
This document contains test cases for the YourPetNutrition Shopify website (https://yourpetnutrition.myshopify.com/). The test cases focus on functionality, usability, and bug verification.

## Test Environment
- **URL**: https://yourpetnutrition.myshopify.com/
- **Password**: 12345
- **Browsers**: Chrome, Firefox, Safari, Edge
- **Devices**: Desktop, Tablet, Mobile

## 1. Authentication Tests

### TC-1.1: Store Password Protection
**Description**: Verify that the store is password protected and requires the correct password to access.
**Steps**:
1. Navigate to https://yourpetnutrition.myshopify.com/
2. Verify the password protection page is displayed
3. Enter incorrect password
4. Verify access is denied
5. Enter correct password "12345"
6. Verify access is granted and user is redirected to the homepage

**Expected Result**: Only users with the correct password can access the store.
**Status**: To be tested

## 2. Navigation and Layout Tests

### TC-2.1: Main Navigation Menu
**Description**: Verify that the main navigation menu works correctly.
**Steps**:
1. Access the website with the correct password
2. Click on each navigation item (Home, Story, Shop Dogs, Shop Cats, Contact, Blog)
3. Verify each page loads correctly

**Expected Result**: All navigation links work correctly and lead to the appropriate pages.
**Status**: To be tested

### TC-2.2: Footer Navigation
**Description**: Verify that the footer navigation links work correctly.
**Steps**:
1. Access the website with the correct password
2. Scroll to the footer
3. Click on 'Shop' link in the footer
4. Verify the shop page loads correctly with all product images

**Expected Result**: All footer navigation links work correctly and product images are displayed properly.
**Status**: Bug found - The 'Shop' link in the footer redirects to a page with missing/empty image placeholder

### TC-2.3: Language Selection
**Description**: Verify that the language selection in the footer works correctly.
**Steps**:
1. Access the website with the correct password
2. Scroll to the footer
3. Check if language selection is available 
4. If available, select a different language (e.g., 'Deutsch')
5. Verify the correct language is selected and shown

**Expected Result**: Language selection is available and works correctly.
**Status**: Bug found - Language selection is missing in the footer

### TC-2.4: Responsive Design - Desktop
**Description**: Verify that the website displays correctly on desktop devices.
**Steps**:
1. Access the website on a desktop browser
2. Verify the layout is appropriate for desktop
3. Check that all elements are properly aligned and visible

**Expected Result**: Website displays correctly on desktop with proper layout and alignment.
**Status**: To be tested

### TC-2.5: Responsive Design - Tablet
**Description**: Verify that the website displays correctly on tablet devices.
**Steps**:
1. Access the website on a tablet or using tablet emulation
2. Verify the layout adjusts appropriately for tablet screens
3. Check that all elements are properly aligned and visible

**Expected Result**: Website displays correctly on tablets with proper layout and alignment.
**Status**: To be tested

### TC-2.6: Responsive Design - Mobile
**Description**: Verify that the website displays correctly on mobile devices.
**Steps**:
1. Access the website on a mobile device or using mobile emulation
2. Verify the layout adjusts appropriately for mobile screens
3. Check that all elements are properly aligned and visible
4. Test the mobile menu functionality
5. Check social media images are not extending beyond viewport width

**Expected Result**: Website displays correctly on mobile with proper layout and alignment.
**Status**: Bug found - Social media images extend beyond viewport width on mobile devices

## 3. Story Page Tests

### TC-3.1: Story Page Video
**Description**: Verify that the video on the Story page loads and plays correctly.
**Steps**:
1. Navigate to the Story page
2. Verify the video is present
3. Verify the video is the correct one (Your Pet Nutrition video)
4. Click play and verify the video plays correctly

**Expected Result**: The correct Your Pet Nutrition video is displayed and plays properly.
**Status**: Bug found - Wrong video is shown (Dr Sister video instead of Your Pet Nutrition video)

## 4. Product Listing Tests

### TC-4.1: Dog Products Display
**Description**: Verify that all dog products are displayed correctly on the Shop Dogs page.
**Steps**:
1. Navigate to the Shop Dogs page
2. Verify all dog products are displayed (Canine Prime, Relax + Restore, Denta Soft, Flexi Protect)
3. Verify each product shows correct name, image, price, and description

**Expected Result**: All dog products are displayed correctly with accurate information.
**Status**: To be tested

### TC-4.2: Cat Products Display
**Description**: Verify that all cat products are displayed correctly on the Shop Cats page.
**Steps**:
1. Navigate to the Shop Cats page
2. Verify all cat products are displayed (Feline 40, Relax + Restore Cats)
3. Verify each product shows correct name, image, price, and description

**Expected Result**: All cat products are displayed correctly with accurate information.
**Status**: To be tested

## 5. Product Detail Page Tests - Canine Prime

### TC-5.1: Canine Prime - Product Description
**Description**: Verify that the Canine Prime product detail page displays correct description.
**Steps**:
1. Navigate to the Shop Dogs page
2. Click on the Canine Prime product
3. Verify the product name, images, and description are displayed correctly

**Expected Result**: Canine Prime product detail page displays correct information.
**Status**: To be tested

### TC-5.2: Canine Prime - Product Video
**Description**: Verify that the product video on the Canine Prime page loads and plays correctly.
**Steps**:
1. Navigate to the Canine Prime product page
2. Scroll down to find the product video
3. Verify the video is present
4. Click play and verify the video plays correctly

**Expected Result**: Product video is displayed and plays properly.
**Status**: Bug found - Product video is missing

### TC-5.3: Canine Prime - Purchase Options
**Description**: Verify that purchase options on Canine Prime page work correctly.
**Steps**:
1. Navigate to the Canine Prime product page
2. Verify "ONE TIME PURCHASE" and "SUBSCRIBE AND SAVE" radio buttons are present
3. Select "ONE TIME PURCHASE" option
4. Verify the radio button shows selected state (colored dot)
5. Select "SUBSCRIBE AND SAVE" option
6. Verify the radio button shows selected state
7. Verify no duplicate text appears in the radio button label

**Expected Result**: Purchase options work correctly with proper visual feedback.
**Status**: Previously had issues with radio button selection and duplicate text

### TC-5.4: Canine Prime - Quantity Selection
**Description**: Verify that quantity selection on Canine Prime page works correctly.
**Steps**:
1. Navigate to the Canine Prime product page
2. Verify quantity options (1 jar, 3 jars, 6 jars) are displayed
3. Verify each option shows appropriate price, discount information, and shipping info
4. Select "3 jars" option
5. Verify orange frame appears on hover
6. Verify the price updates accordingly
7. Select "6 jars" option
8. Verify orange frame appears on hover
9. Verify the price updates accordingly

**Expected Result**: Quantity selection works correctly with proper visual feedback and price updates.
**Status**: Previously had issues with missing information and hover effects

### TC-5.5: Canine Prime - Ingredients Section Responsiveness
**Description**: Verify that the ingredients section displays correctly on mobile devices.
**Steps**:
1. Navigate to the Canine Prime product page on a mobile device
2. Scroll to the "Vitamins and minerals" section in the ingredients
3. Verify the image is clearly visible

**Expected Result**: Ingredients section and images display properly on mobile.
**Status**: Bug found - "Vitamins and minerals" image is almost not visible on mobile devices

## 6. Product Detail Page Tests - Relax and Restore (Dogs)

### TC-6.1: Relax and Restore - Product Description
**Description**: Verify that the Relax and Restore product detail page displays correctly.
**Steps**:
1. Navigate to the Shop Dogs page
2. Click on the Relax and Restore product
3. Verify the product name, images, and description are displayed correctly

**Expected Result**: Relax and Restore product detail page displays correct information.
**Status**: To be tested

### TC-6.2: Relax and Restore - Product Video
**Description**: Verify that the product video on the Relax and Restore page is present.
**Steps**:
1. Navigate to the Relax and Restore product page
2. Scroll down to find the product video
3. Verify the video is present
4. Click play and verify the video plays correctly

**Expected Result**: Product video is displayed and plays properly.
**Status**: Bug found - Product video is missing

### TC-6.3: Relax and Restore - Purchase Options
**Description**: Verify that purchase options on Relax and Restore page work correctly.
**Steps**:
1. Navigate to the Relax and Restore product page
2. Verify "ONE TIME PURCHASE" and "SUBSCRIBE AND SAVE" radio buttons are present
3. Select "ONE TIME PURCHASE" option
4. Verify the radio button shows selected state (colored dot)
5. Select "SUBSCRIBE AND SAVE" option
6. Verify the radio button shows selected state
7. Verify no duplicate text appears in the radio button label

**Expected Result**: Purchase options work correctly with proper visual feedback.
**Status**: Previously had issues with radio button selection and duplicate text

### TC-6.4: Relax and Restore - Subscription Frequency
**Description**: Verify that subscription frequency dropdown works correctly.
**Steps**:
1. Navigate to the Relax and Restore product page
2. Select "SUBSCRIBE AND SAVE" option
3. Verify the frequency dropdown appears
4. Open the dropdown and verify months are in correct order (1 month first)
5. Verify 1 Month is selected by default
6. Select a different option (e.g., 2 months)
7. Verify selection is applied correctly

**Expected Result**: Subscription frequency dropdown works correctly with proper ordering and default selection.
**Status**: Previously had issues with incorrect order and default selection

### TC-6.5: Relax and Restore - Review Section
**Description**: Verify that the review section is present on the product page.
**Steps**:
1. Navigate to the Relax and Restore product page
2. Scroll down to find the review section
3. Verify review section is present and displays correctly

**Expected Result**: Review section is displayed properly.
**Status**: Bug found - Review section was missing

## 7. Product Detail Page Tests - Denta Soft

### TC-7.1: Denta Soft - Product Description
**Description**: Verify that the Denta Soft product detail page displays correctly.
**Steps**:
1. Navigate to the Shop Dogs page
2. Click on the Denta Soft product
3. Verify the product name, images, and description are displayed correctly

**Expected Result**: Denta Soft product detail page displays correct information.
**Status**: To be tested

### TC-7.2: Denta Soft - About Section Image
**Description**: Verify that the image in the 'About Denta Soft' section is displayed.
**Steps**:
1. Navigate to the Denta Soft product page
2. Scroll to the 'About Denta Soft' section
3. Verify the section image is displayed correctly

**Expected Result**: 'About Denta Soft' section image is displayed properly.
**Status**: Bug found - Image is missing in 'About Denta Soft' section

### TC-7.3: Denta Soft - How to Use Section
**Description**: Verify correct weight information in the 'How to Use' section.
**Steps**:
1. Navigate to the Denta Soft product page
2. Scroll to the 'How to Use' section
3. Verify correct weight ranges are displayed (0>5Kg, 5>10Kg, 10>25Kg, 25+Kg)

**Expected Result**: Correct weight ranges are displayed in the 'How to Use' section.
**Status**: Bug found - Incorrect text (0>5Kg on every weight type) in 'How to Use' section

### TC-7.4: Denta Soft - Ingredients Section Spacing
**Description**: Verify correct spacing in the ingredients section.
**Steps**:
1. Navigate to the Denta Soft product page
2. Scroll to the ingredients section in FAQ
3. Verify proper spacing between "Spinach Leaf Extract" and its description
4. Verify proper spacing between "Tapioca & Potato Starch" and its description

**Expected Result**: Proper spacing between ingredient names and descriptions.
**Status**: Bug found - Missing spacing between ingredient names and descriptions

### TC-7.5: Denta Soft - Review Section
**Description**: Verify that the review section is present on the product page.
**Steps**:
1. Navigate to the Denta Soft product page
2. Scroll down to find the review section
3. Verify review section is present and displays correctly

**Expected Result**: Review section is displayed properly.
**Status**: Bug found - Review section was missing

## 8. Product Detail Page Tests - Flexi Protect

### TC-8.1: Flexi Protect - Product Description
**Description**: Verify that the Flexi Protect product detail page displays correctly.
**Steps**:
1. Navigate to the Shop Dogs page
2. Click on the Flexi Protect product
3. Verify the product name, images, and description are displayed correctly

**Expected Result**: Flexi Protect product detail page displays correct information.
**Status**: To be tested

### TC-8.2: Flexi Protect - About Section Image
**Description**: Verify that the image in the 'About Flexi Protect' section is displayed.
**Steps**:
1. Navigate to the Flexi Protect product page
2. Scroll to the 'About Flexi Protect' section
3. Verify the section image is displayed correctly

**Expected Result**: 'About Flexi Protect' section image is displayed properly.
**Status**: Bug found - Image is missing in 'About Flexi Protect' section

### TC-8.3: Flexi Protect - How to Use Section
**Description**: Verify correct weight information in the 'How to Use' section.
**Steps**:
1. Navigate to the Flexi Protect product page
2. Scroll to the 'How to Use' section
3. Verify correct weight ranges are displayed (0>5Kg, 5>10Kg, 10>25Kg, 25+Kg)

**Expected Result**: Correct weight ranges are displayed in the 'How to Use' section.
**Status**: Bug found - Incorrect text (0>5Kg on every weight type) in 'How to Use' section

### TC-8.4: Flexi Protect - What's Inside Section
**Description**: Verify that the 'What's Inside Flexi Protect' section is present.
**Steps**:
1. Navigate to the Flexi Protect product page
2. Scroll to find the 'What's Inside Flexi Protect' section
3. Verify the section is present and displays correctly

**Expected Result**: 'What's Inside Flexi Protect' section is displayed properly.
**Status**: Bug fixed - Section was previously missing

### TC-8.5: Flexi Protect - FAQ Active Ingredients
**Description**: Verify that the content of "What are the active ingredients?" in FAQ is present.
**Steps**:
1. Navigate to the Flexi Protect product page
2. Scroll to the FAQ section
3. Find "What are the active ingredients?" question
4. Verify the answer content is present and displays correctly

**Expected Result**: Active ingredients information is displayed properly in FAQ.
**Status**: Bug fixed - Content was previously missing

## 9. Product Detail Page Tests - Feline 40

### TC-9.1: Feline 40 - Product Description
**Description**: Verify that the Feline 40 product detail page displays complete description.
**Steps**:
1. Navigate to the Shop Cats page
2. Click on the Feline 40 product
3. Verify the product description is complete and not truncated

**Expected Result**: Complete product description is displayed.
**Status**: Bug fixed - Description was previously incomplete

### TC-9.2: Feline 40 - Why Feline 40 Section Image
**Description**: Verify that the image in the 'Why Feline 40' section is displayed.
**Steps**:
1. Navigate to the Feline 40 product page
2. Scroll to the 'Why Feline 40' section
3. Verify the section image is displayed correctly

**Expected Result**: 'Why Feline 40' section image is displayed properly.
**Status**: Bug found - Image is missing in 'Why Feline 40' section

### TC-9.3: Feline 40 - Product Video
**Description**: Verify that the product video on the Feline 40 page is present.
**Steps**:
1. Navigate to the Feline 40 product page
2. Scroll down to find the product video
3. Verify the video is present
4. Click play and verify the video plays correctly

**Expected Result**: Product video is displayed and plays properly.
**Status**: Bug found - Product video is missing

### TC-9.4: Feline 40 - Mushroom Blend Text Alignment
**Description**: Verify proper text alignment in the Ingredients section.
**Steps**:
1. Navigate to the Feline 40 product page
2. Scroll to the Ingredients section
3. Check the 'Mushroom Blend' text and image
4. Verify text is not overlapping with the mushroom image

**Expected Result**: 'Mushroom Blend' text does not overlap with the image.
**Status**: Bug fixed - Text was previously overlapping with image

## 10. Product Detail Page Tests - Relax + Restore Cats

### TC-10.1: Relax + Restore Cats - Product Description
**Description**: Verify that the Relax + Restore Cats product detail page displays correctly.
**Steps**:
1. Navigate to the Shop Cats page
2. Click on the Relax + Restore Cats product
3. Verify the product name, images, and description are displayed correctly

**Expected Result**: Relax + Restore Cats product detail page displays correct information.
**Status**: To be tested

### TC-10.2: Relax + Restore Cats - Section Headers Size
**Description**: Verify that section headers in "What Makes Relax+Restore work so well" are properly sized.
**Steps**:
1. Navigate to the Relax + Restore Cats product page
2. Scroll to the "What Makes Relax+Restore work so well" section
3. Verify that headers "#1 Natural Nutrition", "#2 Dual System Activation", "#3 Universally Loved Taste" are appropriately sized and visible

**Expected Result**: Section headers are properly sized and clearly visible.
**Status**: Bug found - Headers are too small

### TC-10.3: Relax + Restore Cats - Key Ingredients Formatting
**Description**: Verify proper spacing in the 'These Are The Key Ingredients' section.
**Steps**:
1. Navigate to the Relax + Restore Cats product page
2. Scroll to the 'These Are The Key Ingredients' section
3. Verify proper spacing between ingredient names and descriptions (e.g., "L-Tryptophan -", "Passiflora Incarnata -")

**Expected Result**: Proper spacing between ingredient names and descriptions.
**Status**: Bug fixed - Previously had backslash formatting issues

## 11. Cart Functionality Tests

### TC-11.1: Add to Cart - Canine Prime
**Description**: Verify that Canine Prime can be added to the cart.
**Steps**:
1. Navigate to the Canine Prime product page
2. Click the "Add to cart" button
3. Verify the product is added to the cart
4. Verify the cart icon updates to show item count

**Expected Result**: Canine Prime is successfully added to the cart.
**Status**: To be tested

### TC-11.2: Add to Cart - Different Quantities
**Description**: Verify that different quantities can be added to the cart.
**Steps**:
1. Navigate to any product page
2. Select a different quantity (e.g., 3 jars)
3. Click the "Add to cart" button
4. Verify the correct quantity is added to the cart
5. Verify the cart total reflects the correct quantity and price

**Expected Result**: Different quantities can be added to the cart successfully.
**Status**: To be tested

### TC-11.3: Add to Cart - Subscribe and Save
**Description**: Verify that products with subscription option can be added to the cart.
**Steps**:
1. Navigate to any product page
2. Select the "SUBSCRIBE AND SAVE" option
3. Select a delivery frequency if applicable
4. Click the "Add to cart" button
5. Verify the product with subscription is added to the cart
6. Verify the cart shows the subscription details

**Expected Result**: Products with subscription option can be added to the cart successfully.
**Status**: To be tested

## 12. Cross-Browser Compatibility Tests

### TC-12.1: Chrome Compatibility
**Description**: Verify that the website works correctly in Chrome.
**Steps**:
1. Access the website in Chrome
2. Test key functionality (navigation, product display, add to cart)

**Expected Result**: Website works correctly in Chrome.
**Status**: To be tested

### TC-12.2: Firefox Compatibility
**Description**: Verify that the website works correctly in Firefox.
**Steps**:
1. Access the website in Firefox
2. Test key functionality (navigation, product display, add to cart)

**Expected Result**: Website works correctly in Firefox.
**Status**: To be tested

### TC-12.3: Safari Compatibility
**Description**: Verify that the website works correctly in Safari.
**Steps**:
1. Access the website in Safari
2. Test key functionality (navigation, product display, add to cart)

**Expected Result**: Website works correctly in Safari.
**Status**: To be tested

### TC-12.4: Edge Compatibility
**Description**: Verify that the website works correctly in Edge.
**Steps**:
1. Access the website in Edge
2. Test key functionality (navigation, product display, add to cart)

**Expected Result**: Website works correctly in Edge.
**Status**: To be tested

## 13. Mobile Compatibility Tests

### TC-13.1: iOS Compatibility
**Description**: Verify that the website works correctly on iOS devices.
**Steps**:
1. Access the website on an iOS device (iPhone/iPad)
2. Test key functionality (navigation, product display, add to cart)

**Expected Result**: Website works correctly on iOS devices.
**Status**: To be tested

### TC-13.2: Android Compatibility
**Description**: Verify that the website works correctly on Android devices.
**Steps**:
1. Access the website on an Android device
2. Test key functionality (navigation, product display, add to cart)

**Expected Result**: Website works correctly on Android devices.
**Status**: To be tested

## Summary of Current Issues

1. **Footer - Language Selection**: Language selection is missing in the footer.
2. **Footer - Shop Link**: The 'Shop' link in the footer redirects to a page with missing/empty image placeholder.
3. **Mobile Responsiveness - Social Media Icons**: Social media images extend beyond viewport width on mobile devices.
4. **Story Page - Video**: Wrong video is shown (Dr Sister video instead of Your Pet Nutrition video).
5. **Canine Prime - Product Video**: Product video is missing.
6. **Canine Prime - Ingredients Section**: "Vitamins and minerals" image is almost not visible on mobile devices.
7. **Relax and Restore - Product Video**: Product video is missing.
8. **Denta Soft - About Section**: Image is missing in 'About Denta Soft' section.
9. **Denta Soft - How to Use Section**: Incorrect text (0>5Kg on every weight type) in 'How to Use' section.
10. **Denta Soft - Ingredients Section**: Missing spacing between ingredient names and descriptions.
11. **Flexi Protect - About Section**: Image is missing in 'About Flexi Protect' section.
12. **Flexi Protect - How to Use Section**: Incorrect text (0>5Kg on every weight type) in 'How to Use' section.
13. **Feline 40 - Why Feline 40 Section**: Image is missing in 'Why Feline 40' section.
14. **Feline 40 - Product Video**: Product video is missing.
15. **Relax + Restore Cats - Section Headers**: Headers in "What Makes Relax+Restore work so well" section are too small.

## Fixed Issues

1. Social media images no longer extending beyond viewport width on desktop.
2. "FREE SHIPPING", "ON ALL ORDERS..." text no longer overlaps with truck icon on mobile.
3. Products are no longer stacked too close together on mobile.
4. Quantity buttons now show correct information and discounts.
5. Radio button selection is now visually indicated.
6. Spacing issues in Relax + Restore product sections.
7. Feline 40 product description is now complete.
8. "What's Inside Flexi Protect" section is now present.
9. Content of "What are the active ingredients?" in Flexi Protect FAQ is now present.
10. 'Mushroom Blend' text no longer overlaps with mushroom image in Feline 40.
11. Price no longer overlaps with 'Add to cart' button on mobile.
12. Delivery frequency dropdown no longer overlaps discount price on mobile.
