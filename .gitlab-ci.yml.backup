# GitLab CI/CD configuration for Browserstack Playwright tests

stages:
  - test
  - report

# Default variables that can be overridden by the webhook trigger
variables:
  NODE_VERSION: "16"
  TEST_ENV: "stage"        # Default environment (stage or dev)
  BROWSER: "chromium"      # Default browser to run tests with
  DEVICE: "desktop"        # Default device (desktop or mobile)
  COMMIT_SHA: $CI_COMMIT_SHA
  TIMESTAMP: $CI_COMMIT_TIMESTAMP

# Base job template
.test_job_template: &test_job_definition
  image: node:${NODE_VERSION}
  stage: test
  before_script:
    - npm ci
  artifacts:
    paths:
      - test-results/
      - playwright-report/
    reports:
      junit: test-results/junit-report.xml
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "pipeline"
      when: always

# Run tests based on environment variable
run_tests:
  <<: *test_job_definition
  script: 
    - echo "Running tests for environment: $TEST_ENV"
    - npm run ci:test

# Generate and publish test report
generate_report:
  image: node:${NODE_VERSION}
  stage: report
  script: 
    - echo "Generating test report"
    - npm ci
    - npm run report
  artifacts:
    paths:
      - playwright-report/
    expire_in: 1 week
  needs:
    - run_tests
  rules:
    - if: $CI_PIPELINE_SOURCE == "pipeline"
      when: always

# Job that runs when manually triggered
manual_test:
  stage: test
  image: mcr.microsoft.com/playwright:v1.40.0-focal
  before_script:
    - npm ci
    - npx playwright install --with-deps
  script: 
    - echo "Running tests for ${TEST_ENV} environment with ${BROWSER} browser on ${DEVICE} device"
    - echo "Build information: Commit SHA - ${COMMIT_SHA}, Timestamp - ${TIMESTAMP}"
    - 'if [ "$DEVICE" = "mobile" ]; then DEVICE_PROJECT="samsung-galaxy"; else DEVICE_PROJECT="${BROWSER}"; fi'
    - 'node ./ci/run-env-tests.js $TEST_ENV $DEVICE_PROJECT'
  artifacts:
    when: always
    paths:
      - test-results/
      - playwright-report/
    reports:
      junit: test-results/results.xml
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "trigger"
      when: always
    - if: $CI_PIPELINE_SOURCE == "web"
      when: always
