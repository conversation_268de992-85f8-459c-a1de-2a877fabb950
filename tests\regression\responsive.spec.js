/**
 * @fileoverview Responsive design tests using BrowserStack
 */

const { test, expect } = require('../fixtures/unified-fixture');
const { BrowserStackHelper } = require('../../src/utils/browserstack/browserstack-helper');

test.describe('Responsive Design Tests', () => {
    let bsHelper;
    let testProducts;

    test.beforeEach(async ({ page, testDataManager }, testInfo) => {
        bsHelper = new BrowserStackHelper();

        // Get products from centralized data management
        // Note: Using DSS brand products as the original test was for drsisterskincare.com
        testProducts = [
            'dark_spot_vanish',
            'younger_you_skin_cream',
            'relax_restore'  // Using available DSS product instead of inner_beauty
        ];
    });

    test.afterEach(async ({ }, testInfo) => {
        if (process.env.BROWSERSTACK_SESSION_ID) {
            await bsHelper.downloadArtifacts(process.env.BROWSERSTACK_SESSION_ID);
        }
    });

    const breakpoints = [
        { width: 320, height: 568, name: 'mobile-small' },    // iPhone SE
        { width: 375, height: 667, name: 'mobile-medium' },   // iPhone 8
        { width: 414, height: 896, name: 'mobile-large' },    // iPhone 11 Pro Max
        { width: 768, height: 1024, name: 'tablet' },         // iPad
        { width: 1024, height: 768, name: 'tablet-landscape' },// iPad landscape
        { width: 1280, height: 800, name: 'desktop' },        // Standard desktop
        { width: 1920, height: 1080, name: 'desktop-large' }  // Large desktop
    ];

    for (const productKey of testProducts) {
        test(`Responsive layout - ${productKey}`, async ({ page, testDataManager }, testInfo) => {
            // Get product data and construct URL using centralized management
            const product = testDataManager.getProduct(productKey);
            const baseUrl = testDataManager.getBaseUrl();
            const url = `${baseUrl}${product.urlPath}`;

            for (const viewport of breakpoints) {
                console.log(`Testing ${product} at ${viewport.width}x${viewport.height}`);

                await page.setViewportSize(viewport);
                await page.goto(url);
                await bsHelper.waitForVisualStability(page);

                // Take full page screenshot
                await bsHelper.takeScreenshot(page, `${productKey}-${viewport.name}-full`, {
                    testName: testInfo.title,
                    fullPage: true,
                    type: 'responsive',
                    viewport
                });

                // Check critical elements
                const elements = {
                    header: '.site-header',
                    navigation: '.site-nav',
                    productTitle: '.product-title',
                    productGallery: '.product-gallery',
                    addToCart: '.add-to-cart-form',
                    footer: '.site-footer'
                };

                for (const [name, selector] of Object.entries(elements)) {
                    const element = await page.$(selector);
                    expect(element, `${name} should be present`).toBeTruthy();

                    // Check visibility and positioning
                    const isVisible = await element.isVisible();
                    expect(isVisible, `${name} should be visible`).toBeTruthy();

                    // Take element screenshot
                    await element.scrollIntoViewIfNeeded();
                    await bsHelper.takeScreenshot(page, `${productKey}-${viewport.name}-${name}`, {
                        testName: testInfo.title,
                        type: 'responsive-element',
                        viewport,
                        element: name
                    });

                    // Check element dimensions
                    const box = await element.boundingBox();
                    expect(box.width).toBeLessThanOrEqual(viewport.width);

                    // Check for overflow issues
                    const hasOverflow = await page.evaluate((sel) => {
                        const el = document.querySelector(sel);
                        const style = window.getComputedStyle(el);
                        return {
                            hasHorizontalScroll: el.scrollWidth > el.clientWidth,
                            isOverflowVisible: style.overflow === 'visible' &&
                                (el.scrollWidth > el.clientWidth || el.scrollHeight > el.clientHeight)
                        };
                    }, selector);

                    expect(hasOverflow.hasHorizontalScroll,
                        `${name} should not have horizontal scroll`
                    ).toBeFalsy();
                }

                // Test interactive elements
                const interactions = [
                    {
                        name: 'mobile-menu',
                        setup: async () => {
                            if (viewport.width < 768) {
                                await page.click('.mobile-nav-trigger');
                            }
                        }
                    },
                    {
                        name: 'product-gallery',
                        setup: async () => {
                            await page.click('.product-gallery__thumbnail:first-child');
                        }
                    },
                    {
                        name: 'quantity-selector',
                        setup: async () => {
                            await page.fill('.quantity-selector input', '2');
                        }
                    }
                ];

                for (const interaction of interactions) {
                    await interaction.setup();
                    await bsHelper.waitForVisualStability(page);

                    await bsHelper.takeScreenshot(
                        page,
                        `${productKey}-${viewport.name}-${interaction.name}`,
                        {
                            testName: testInfo.title,
                            type: 'responsive-interaction',
                            viewport,
                            interaction: interaction.name
                        }
                    );
                }

                // Test scroll positions
                const scrollPositions = ['top', 'middle', 'bottom'];
                for (const position of scrollPositions) {
                    await page.evaluate((pos) => {
                        const height = document.documentElement.scrollHeight;
                        window.scrollTo(0, pos === 'top' ? 0 :
                            pos === 'middle' ? height / 2 : height);
                    }, position);

                    await bsHelper.waitForVisualStability(page);
                    await bsHelper.takeScreenshot(
                        page,
                        `${productKey}-${viewport.name}-scroll-${position}`,
                        {
                            testName: testInfo.title,
                            type: 'responsive-scroll',
                            viewport,
                            scrollPosition: position
                        }
                    );
                }
            }
        });

        test(`Touch interactions - ${productKey}`, async ({ page, testDataManager }, testInfo) => {
            const deviceInfo = await bsHelper.getDeviceInfo(page);
            const isMobile = deviceInfo.device !== 'desktop';

            if (!isMobile) {
                test.skip();
                return;
            }

            // Get product data and construct URL using centralized management
            const product = testDataManager.getProduct(productKey);
            const baseUrl = testDataManager.getBaseUrl();
            const url = `${baseUrl}${product.urlPath}`;

            await page.goto(url);
            await bsHelper.waitForVisualStability(page);

            // Test touch-specific interactions
            const touchInteractions = [
                {
                    name: 'gallery-swipe',
                    action: async () => {
                        await page.evaluate(() => {
                            const gallery = document.querySelector('.product-gallery');
                            gallery.dispatchEvent(new TouchEvent('touchstart', {
                                bubbles: true,
                                touches: [{ clientX: 300, clientY: 200 }]
                            }));
                            gallery.dispatchEvent(new TouchEvent('touchmove', {
                                bubbles: true,
                                touches: [{ clientX: 100, clientY: 200 }]
                            }));
                            gallery.dispatchEvent(new TouchEvent('touchend', {
                                bubbles: true
                            }));
                        });
                    }
                },
                {
                    name: 'pinch-zoom',
                    action: async () => {
                        await page.evaluate(() => {
                            const image = document.querySelector('.product-gallery__image');
                            image.dispatchEvent(new TouchEvent('touchstart', {
                                bubbles: true,
                                touches: [
                                    { clientX: 100, clientY: 100 },
                                    { clientX: 200, clientY: 200 }
                                ]
                            }));
                        });
                    }
                }
            ];

            for (const interaction of touchInteractions) {
                await interaction.action();
                await bsHelper.waitForVisualStability(page);

                await bsHelper.takeScreenshot(page, `${productKey}-touch-${interaction.name}`, {
                    testName: testInfo.title,
                    type: 'touch-interaction',
                    interaction: interaction.name
                });
            }
        });
    }
});
