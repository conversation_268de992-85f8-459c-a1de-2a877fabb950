/**
 * @fileoverview Purchase Flow Fixture
 *
 * Specialized fixture for purchase flow tests that extends the enhanced
 * unified fixture with purchase-specific functionality and helpers.
 * 
 * Features:
 * - Standardized purchase flow methods
 * - Support for one-time and subscription purchases
 * - PayPal and credit card payment flows
 * - Email verification integration
 * - Error handling and retry logic
 * - Full backward compatibility with existing purchase tests
 */

const { test: enhancedTest, expect } = require('../enhanced-unified-fixture');
const { PurchaseFlowHelper } = require('./purchase-flow-helper');

/**
 * Purchase flow fixture with standardized purchase workflows
 */
const purchaseFlowFixture = enhancedTest.extend({
    /**
     * Purchase flow helper with standardized methods
     */
    purchaseFlow: async ({ pageObjectFactory, testDataHelper, emailHelper, browserStackHelper, deviceHelper }, use) => {
        console.log('[PurchaseFlowFixture] Initializing purchase flow helper');
        
        const purchaseFlow = new PurchaseFlowHelper(
            pageObjectFactory,
            testDataHelper,
            emailHelper,
            browserStackHelper,
            deviceHelper
        );
        
        await use(purchaseFlow);
    },

    /**
     * Enhanced test data specifically for purchase flows
     */
    purchaseTestData: async ({ testDataHelper }, use) => {
        // Provide default purchase test data
        const testData = testDataHelper.getPurchaseTestData();
        await use(testData);
    },

    /**
     * PayPal-specific test data and configuration
     */
    paypalTestData: async ({ testDataHelper }, use) => {
        const testData = testDataHelper.getPayPalTestData();
        await use(testData);
    },

    /**
     * Subscription-specific test data and configuration
     */
    subscriptionTestData: async ({ testDataHelper }, use) => {
        const testData = testDataHelper.getSubscriptionTestData();
        await use(testData);
    },

    /**
     * Mobile-optimized test data and configuration
     */
    mobileTestData: async ({ testDataHelper }, use) => {
        const testData = testDataHelper.getMobileTestData();
        await use(testData);
    }
});

// Export the purchase flow test fixture
module.exports = {
    test: purchaseFlowFixture,
    expect
};
