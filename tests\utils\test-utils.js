/**
 * @fileoverview Utility functions for tests.
 */
const path = require('path');

/**
 * Constructs the path to the baseline screenshot for a given test.
 * @param {string} projectName - The name of the project (e.g., 'dss-shopify').
 * @param {string} testName - The name of the test.
 * @returns {string} The path to the baseline screenshot.
 */
function getBaselineScreenshotPath(projectName, testName) {
    return path.join(process.cwd(), 'baseline_screenshots', projectName, `${testName}.png`);
}

/**
 * Wait for page to be visually stable
 * @param {Page} page Playwright page object
 */
async function waitForPageStability(page) {
    // Wait for network idle
    await page.waitForLoadState('networkidle');

    // Wait for animations to complete
    await page.evaluate(() => {
        return Promise.all(
            document.getAnimations().map(animation => animation.finished)
        );
    });

    // Wait for fonts to load
    await page.evaluate(() => document.fonts.ready);

    // Wait for images to load
    await page.evaluate(() => {
        return Promise.all(
            Array.from(document.images)
                .filter(img => !img.complete)
                .map(img => new Promise(resolve => {
                    img.onload = img.onerror = resolve;
                }))
        );
    });

    // Wait for any loading indicators to disappear
    await page.waitForFunction(() => {
        return !document.querySelector('.loading') &&
            !document.querySelector('[aria-busy="true"]');
    });

    // Wait a bit more for any dynamic content
    await page.waitForTimeout(500);
}

module.exports = { getBaselineScreenshotPath, waitForPageStability }; 