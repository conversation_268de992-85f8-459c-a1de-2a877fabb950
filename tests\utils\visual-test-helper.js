/**
 * @fileoverview Helper for managing visual test artifacts and Gemini analysis
 */

const { createPlaywrightTestConfig } = require('browserstack-node-sdk');
const { AnalysisOrchestrator } = require('../../utils/gemini/service/AnalysisOrchestrator');
const { PageStabilityHelper } = require('./test-config');
const path = require('path');
const fs = require('fs').promises;

class VisualTestHelper {
    constructor() {
        this.analysisOrchestrator = new AnalysisOrchestrator();
        this.screenshotsDir = path.join(process.cwd(), 'test-results', 'screenshots');
        this.artifactsDir = path.join(process.cwd(), 'test-results', 'browserstack');
        this.bsConfig = createPlaywrightTestConfig();
    }

    /**
     * Take screenshot during test execution
     * @param {Page} page Playwright page object
     * @param {string} name Screenshot name
     * @param {Object} options Screenshot options
     * @returns {Promise<Object>} Screenshot metadata
     */
    async takeScreenshot(page, name, options = {}) {
        // Wait for visual stability
        await PageStabilityHelper.waitForPageStability(page);

        // Use BrowserStack SDK to take screenshot
        await this.bsConfig.takeScreenshot(page, {
            name,
            fullPage: options.fullPage,
            ...options
        });

        // Return metadata for tracking
        return {
            name,
            timestamp: new Date().toISOString(),
            testInfo: options.testInfo
        };
    }

    /**
     * Collect and analyze test results
     * @param {Object} testInfo Test information
     * @param {Array<Object>} screenshotMetadata Screenshot metadata
     * @returns {Promise<Object>} Analysis results
     */
    async collectAndAnalyzeResults(testInfo) {
        try {
            // Wait for BrowserStack session to complete
            await this.bsConfig.waitForSessionCompletion();

            // Collect all artifacts from BrowserStack
            const artifacts = await this.collectTestArtifacts(testInfo);

            // Download artifacts locally
            const localArtifacts = await this.downloadArtifacts(artifacts, testInfo);

            // Send to Gemini for analysis
            const analysisResults = await this.analysisOrchestrator.analyzeTestResults({
                testInfo,
                artifacts: localArtifacts,
                testResult: testInfo.status
            });

            // Save analysis results
            await this.saveAnalysisResults(analysisResults, testInfo);

            return analysisResults;
        } catch (error) {
            console.error('Error in test result collection and analysis:', error);
            throw error;
        }
    }

    /**
     * Collect test artifacts from BrowserStack
     * @param {Object} testInfo Test information
     * @returns {Promise<Object>} Test artifacts
     */
    async collectTestArtifacts(testInfo) {
        const sessionId = testInfo.attachments.find(a => a.name === 'browserstack-session')?.body;
        if (!sessionId) {
            throw new Error('No BrowserStack session ID found');
        }

        // Get session data from BrowserStack
        const sessionData = await this.bsConfig.getSessionData(sessionId);

        return {
            sessionId,
            screenshots: sessionData.screenshots,
            video: sessionData.video_url,
            logs: sessionData.logs,
            console: sessionData.console_logs_url,
            network: sessionData.network_logs_url,
            deviceInfo: sessionData.device_info
        };
    }

    /**
     * Download artifacts locally
     * @param {Object} artifacts BrowserStack artifacts
     * @param {Object} testInfo Test information
     * @returns {Promise<Object>} Local artifact paths
     */
    async downloadArtifacts(artifacts, testInfo) {
        const testDir = path.join(this.artifactsDir, testInfo.title);
        await fs.mkdir(testDir, { recursive: true });

        const localPaths = {
            screenshots: [],
            video: null,
            logs: null,
            console: null,
            network: null
        };

        try {
            // Download screenshots
            if (artifacts.screenshots?.length) {
                const screenshotsDir = path.join(testDir, 'screenshots');
                await fs.mkdir(screenshotsDir, { recursive: true });

                for (const [index, screenshot] of artifacts.screenshots.entries()) {
                    const localPath = path.join(screenshotsDir, `screenshot_${index}.png`);
                    await this.downloadFile(screenshot.url, localPath);
                    localPaths.screenshots.push(localPath);
                }
            }

            // Download video
            if (artifacts.video) {
                localPaths.video = path.join(testDir, 'session.mp4');
                await this.downloadFile(artifacts.video, localPaths.video);
            }

            // Download logs
            if (artifacts.logs) {
                localPaths.logs = path.join(testDir, 'session.log');
                await this.downloadFile(artifacts.logs, localPaths.logs);
            }

            // Download console logs
            if (artifacts.console) {
                localPaths.console = path.join(testDir, 'console.log');
                await this.downloadFile(artifacts.console, localPaths.console);
            }

            // Download network logs
            if (artifacts.network) {
                localPaths.network = path.join(testDir, 'network.log');
                await this.downloadFile(artifacts.network, localPaths.network);
            }

            return localPaths;
        } catch (error) {
            console.error('Error downloading artifacts:', error);
            throw error;
        }
    }

    /**
     * Download file from URL
     * @param {string} url File URL
     * @param {string} localPath Local path
     */
    async downloadFile(url, localPath) {
        const response = await fetch(url);
        const buffer = await response.arrayBuffer();
        await fs.writeFile(localPath, Buffer.from(buffer));
    }

    /**
     * Save analysis results
     * @param {Object} results Analysis results
     * @param {Object} testInfo Test information
     */
    async saveAnalysisResults(results, testInfo) {
        const reportPath = path.join(
            this.artifactsDir,
            'analysis',
            `${testInfo.title}-analysis.json`
        );

        await fs.mkdir(path.dirname(reportPath), { recursive: true });
        await fs.writeFile(reportPath, JSON.stringify(results, null, 2));
    }
}

module.exports = { VisualTestHelper }; 