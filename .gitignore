# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json

# Playwright
test-results/
playwright-report/
playwright/.cache/
**/test-results/
**/playwright-report/
**/playwright/.cache/

# BrowserStack
browserstack.err
local.log
browserstack-sdk.log
BrowserStackLocal
browserstack-local.log

# Test artifacts
screenshots/
videos/
traces/
*.trace.json

# Environment variables
.env
.env.local
.env.*.local
.env.development
.env.test
.env.production

# IDE
.idea/
.vscode/
*.swp
*.swo
*.sublime-*
.project
.settings/

# OS
.DS_Store
Thumbs.db
*.tmp
*.temp
._*

# Logs
logs/
*.log
log/

# Coverage
coverage/
.nyc_output/

# Cursor
.cursor/
.cursorrules
/docs/
/run-smoke-test.ps1
/run-test.bat

docs/Other project
docs/Other project/ARCHITECTURE.md