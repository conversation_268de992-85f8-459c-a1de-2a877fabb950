Feature: Product Purchase
              # In order to buy products
              # As a customer
              # I need to be able to add products to the cart and complete the checkout process

        Background:
            Given I load brand configuration
              And I load product data
              And I am on the product page
             Then I verify Google Tag Manager is present

        @smoke_one_time
        Scenario: Successful one-time purchase with normal card
             When I select "One-Time Purchase"
              And I select flavor "classic"
              And I set the quantity to "medium"
              And I add the product to the cart
              And I proceed to checkout
             Then I verify Google Tag Manager is present
              And I fill in the shipping information with "default" user data
              And I use the same address for billing
             Then The shipping method "Domestic tracked" should be selected
              And I verify the shipping cost
             When I enter "stripe_valid" payment details
              And I complete the purchase
              And I wait for the order confirmation page to load
             Then I verify Google Tag Manager is present
              And I verify the order details are correct

        @flavor @ancient_roots
        Scenario: Successful purchase with different flavors for Ancient Roots
             When I select "One-Time Purchase"
             Then The product should have flavor options
              And I select flavor "truffle"
              And I set the quantity to "medium"
              And I add the product to the cart
              And I proceed to checkout
             Then I verify Google Tag Manager is present
              And I fill in the shipping information with "default" user data
              And I use the same address for billing
             Then The shipping method "Domestic tracked" should be selected
              And I verify the shipping cost
             When I enter "stripe_valid" payment details
              And I complete the purchase
              And I wait for the order confirmation page to load
             Then I verify Google Tag Manager is present
              And I verify the order details are correct
              And The flavor on confirmation page should be "Truffle"

        @3ds
        Scenario: Successful one-time purchase with 3d secure card
             When I select "One-Time Purchase"
              And I select flavor "classic"
              And I set the quantity to "medium"
              And I add the product to the cart
              And I proceed to checkout
             Then I verify Google Tag Manager is present
              And I fill in the shipping information with "default" user data
              And I use the same address for billing
             Then The shipping method "Domestic tracked" should be selected
              And I verify the shipping cost
             When I enter "stripe_valid_3dsecure" payment details
              And I complete the purchase
             Then I should see the 3DS page
              And I press the "Complete" button
              And I wait for the order confirmation page to load
             Then I verify Google Tag Manager is present
              And I verify the order details are correct
              And The selected flavor should be "classic"

        @coupon
        Scenario: Successful one-time purchase with normal card and coupon
             When I select "One-Time Purchase"
              And I select flavor "classic"
              And I set the quantity to "medium"
              And I add the product to the cart
              And I apply a valid coupon code "AEONS15"
              And The order total should be calculated correctly
              And I proceed to checkout
             Then I verify Google Tag Manager is present
              And I fill in the shipping information with "default" user data
              And I use the same address for billing
             Then The shipping method "Domestic tracked" should be selected
              And I verify the shipping cost
             When I enter "stripe_valid" payment details
              And I complete the purchase
              And I wait for the order confirmation page to load
             Then I verify Google Tag Manager is present
              And I verify the order details are correct
              And I verify the order confirmation email
              And The selected flavor should be "classic"

        @negative
        Scenario: Unsuccessful one-time purchase with expired card
             When I select "One-Time Purchase"
              And I select flavor "classic"
              And I set the quantity to "medium"
              And I add the product to the cart
              And I proceed to checkout
             Then I verify Google Tag Manager is present
              And I fill in the shipping information with "default" user data
              And I use the same address for billing
             Then The shipping method "Domestic tracked" should be selected
              And I verify the shipping cost
             When I enter "stripe_expired" payment details
             Then I should see an error message indicating the card has expired

        @subscription
        Scenario: Successful subscription purchase with normal card and 60 day supply
             When I select "Subscribe & Save"
              And I select flavor "classic"
              And I set the quantity to "medium"
              And I set the supply to "2 Months"
              And I add the product to the cart
             Then I verify the cart contains the correct product details
              And I proceed to checkout
             Then I verify Google Tag Manager is present
              And I fill in the shipping information with "default" user data
              And I use the same address for billing
             Then The shipping method "Domestic tracked" should be selected
              And I verify the shipping cost
             When I enter "stripe_valid" payment details
              And I complete the purchase
              And I wait for the order confirmation page to load
             Then I verify Google Tag Manager is present
              And I verify the order details are correct
              And I verify the order confirmation email
              And The selected flavor should be "classic"

        @quantity
        Scenario: Change product quantity
             When I select "One-Time Purchase"
              And I select flavor "classic"
              And I set the quantity to "medium"
              And I add the product to the cart
              And I update the quantity to "5"
             Then Total price should be updated correctly

        @quantityMax
        Scenario: Change product quantity to max
             When I select "One-Time Purchase"
              And I select flavor "classic"
              And I set the quantity to "maximum"
              And I add the product to the cart
              And I update the quantity to "50"
             Then Total price should be updated correctly

        @paypal @positive
        Scenario: Successful purchase with valid PayPal credentials
             When I select "One-Time Purchase"
              And I select flavor "classic"
              And I set the quantity to "medium"
              And I add the product to the cart
              And I proceed to checkout
              And I fill in the shipping information with "default" user data
              And I choose to pay with PayPal
              And I am redirected to the PayPal sandbox page
              And I log in to PayPal sandbox with "valid" credentials
             Then I should see the correct payment amount in PayPal
              And I confirm the PayPal payment
             Then I should be redirected back to the merchant site
              And the PayPal payment should be successful
              And I wait for the order confirmation page to load
             Then I verify Google Tag Manager is present
              And I verify the order details are correct
              And I verify the order confirmation email
              And The selected flavor should be "classic"

        @paypal @negative
        Scenario: Failed purchase with invalid PayPal credentials
             When I select "One-Time Purchase"
              And I select flavor "classic"
              And I set the quantity to "medium"
              And I add the product to the cart
              And I proceed to checkout
              And I fill in the shipping information with "default" user data
              And I choose to pay with PayPal
              And I am redirected to the PayPal sandbox page
              And I log in to PayPal sandbox with "invalid" credentials
             Then I should see a PayPal login error message

        @flavor
        Scenario: Successful purchase with different flavors
             When I select "One-Time Purchase"
              And I select flavor "lemon"
              And I set the quantity to "medium"
              And I add the product to the cart
              And I proceed to checkout
             Then I verify Google Tag Manager is present
              And I fill in the shipping information with "default" user data
              And I use the same address for billing
             Then The shipping method "Domestic tracked" should be selected
              And I verify the shipping cost
             When I enter "stripe_valid" payment details
              And I complete the purchase
              And I wait for the order confirmation page to load
             Then I verify Google Tag Manager is present
              And I verify the order details are correct
              And The selected flavor should be "lemon"