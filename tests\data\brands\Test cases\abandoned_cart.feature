Feature: Abandoned Cart Recovery
              As an online store owner
              I want to track abandoned carts and send recovery emails
              So that I can recover potentially lost sales

        Background:
            Given I load brand configuration
              And I load product data

        @abandoned_cart_smoke @smoke
        Scenario: Customer abandons cart and receives recovery email
            Given I am on the product page
             When I select the "medium" quantity option
              And I select "One-Time Purchase"
              And I add the product to the cart
             When I proceed to checkout
             Then I verify Google Tag Manager is present
              And I fill in the shipping information with "default" user data
              And I abandon the checkout
             Then I should see that the order status is "abandoned" in the database
              And I should receive an abandoned cart email within "1" hour

        @abandoned_cart @email
        Scenario: Customer abandons multiple carts
            Given I am on the product page
             When I select the "medium" quantity option
              And I select "One-Time Purchase" purchase
              And I click add to cart
             When I proceed to checkout
             Then I verify Google Tag Manager is present
              And I fill in the shipping information with "default" user data
              And I abandon the checkout
              And I repeat the process "2" more times
             Then I should see that cart was cancelled "3" times with different order ids
              And I should receive "3" abandoned cart emails

        @abandoned_cart @recovery
        Scenario: Customer recovers abandoned cart through email
            Given I have an abandoned cart
              And I received an abandoned cart recovery email
             When I click the recovery link in the email
             Then I should be redirected to my cart
              And my cart should contain the previously selected items
              And the order status should be "pending" in the database

        @abandoned_cart @expiry
        Scenario: Abandoned cart expires after 24 hours
            Given I have an abandoned cart from "25" hours ago
             When the abandoned cart cleanup job runs
             Then I should see that the order status is "expired" in the database
              And I should not receive any more recovery emails

        @abandoned_cart @recovery_limit
        Scenario: Customer reaches abandoned cart limit
            Given I have abandoned "5" carts in the past "24" hours
             When I try to abandon another cart
             Then I should see a message "You have reached the maximum number of abandoned carts"
              And I should be required to complete the purchase