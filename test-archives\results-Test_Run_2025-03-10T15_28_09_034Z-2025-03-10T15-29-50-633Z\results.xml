<testsuites id="" name="" tests="4" failures="2" skipped="2" errors="0" time="100.88708100000001">
<testsuite name="regression\aeons\regression\copy-verification.spec.js" timestamp="2025-03-10T15:28:16.438Z" hostname="chromium" tests="4" failures="2" skipped="2" time="81.696" errors="0">
<testcase name="Aeons Website Content Verification › Text Content Verification Tests @text › Verify Total Harmony updated copy" classname="regression\aeons\regression\copy-verification.spec.js" time="41.047">
<failure message="copy-verification.spec.js:50:9 Verify Total Harmony updated copy" type="FAILURE">
<![CDATA[  [chromium] › regression\aeons\regression\copy-verification.spec.js:50:9 › Aeons Website Content Verification › Text Content Verification Tests @text › Verify Total Harmony updated copy › Verify updated Ashwagandha description 

    Error: Timed out 15000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Ashwagandha: Revered for centuries for its ability to help support mental clarity and relaxation.')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - expect.toBeVisible with timeout 15000ms
      - waiting for locator('text=Ashwagandha: Revered for centuries for its ability to help support mental clarity and relaxation.')


      62 |             await testWithBrowserStack.step('Verify updated Ashwagandha description', async () => {
      63 |                 const ashwagandhaText = page.locator('text=Ashwagandha: Revered for centuries for its ability to help support mental clarity and relaxation.');
    > 64 |                 await expect(ashwagandhaText).toBeVisible();
         |                                               ^
      65 |             });
      66 |             
      67 |             await testWithBrowserStack.step('Verify updated Sage description', async () => {
        at C:\development\Malaberg\Projects\browserstack-playwright\tests\regression\aeons\regression\copy-verification.spec.js:64:47
        at C:\development\Malaberg\Projects\browserstack-playwright\tests\regression\aeons\regression\copy-verification.spec.js:62:40

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\regression-aeons-regressio-046df--Total-Harmony-updated-copy-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[Registered session test_1741620489036

[[ATTACHMENT|regression-aeons-regressio-046df--Total-Harmony-updated-copy-chromium\test-failed-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Aeons Website Content Verification › Text Content Verification Tests @text › Verify updated product titles for Olive Oil and Bone Broth" classname="regression\aeons\regression\copy-verification.spec.js" time="40.649">
<failure message="copy-verification.spec.js:90:9 Verify updated product titles for Olive Oil and Bone Broth" type="FAILURE">
<![CDATA[  [chromium] › regression\aeons\regression\copy-verification.spec.js:90:9 › Aeons Website Content Verification › Text Content Verification Tests @text › Verify updated product titles for Olive Oil and Bone Broth › Verify Ancient Roots Olive Oil title 

    Error: Timed out 15000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('h1.product__title, h1.product-title, h1.product-single__title').first()
    Expected string: "Ancient Roots Olive Oil"
    Received: <element(s) not found>
    Call log:
      - expect.toContainText with timeout 15000ms
      - waiting for locator('h1.product__title, h1.product-title, h1.product-single__title').first()


       95 |                 // Check page title contains updated product name
       96 |                 const productTitle = page.locator('h1.product__title, h1.product-title, h1.product-single__title').first();
    >  97 |                 await expect(productTitle).toContainText('Ancient Roots Olive Oil');
          |                                            ^
       98 |                 
       99 |                 // Also verify meta title if accessible
      100 |                 const pageTitle = await page.title();
        at C:\development\Malaberg\Projects\browserstack-playwright\tests\regression\aeons\regression\copy-verification.spec.js:97:44
        at C:\development\Malaberg\Projects\browserstack-playwright\tests\regression\aeons\regression\copy-verification.spec.js:91:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\regression-aeons-regressio-10701-or-Olive-Oil-and-Bone-Broth-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[Registered session test_1741620489036

[[ATTACHMENT|regression-aeons-regressio-10701-or-Olive-Oil-and-Bone-Broth-chromium\test-failed-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Aeons Website Content Verification › Text Content Verification Tests @text › Verify Sunrise product page copy updates" classname="regression\aeons\regression\copy-verification.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Aeons Website Content Verification › Text Content Verification Tests @text › Verify Sunrise category page copy updates" classname="regression\aeons\regression\copy-verification.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>