/**
 * Page object for the sales funnel upsell page
 */
class SalesFunnelUpsellPage {
    /**
     * @param {import('@playwright/test').Page} page
     */
    constructor(page) {
        this.page = page;
        this.selectors = {
            // Upsell product details
            product: {
                container: '.upsell-product, .product-presentation',
                title: '.upsell-title, .product-title, h1',
                price: '.upsell-price, .product-price, .price',
                description: '.upsell-description, .product-description'
            },
            
            // Upsell action buttons
            buttons: {
                accept: '.add-upsell, .accept-upsell, button:has-text("Add to my order"), .upsell-accept',
                decline: '.decline-upsell, button:has-text("No thanks"), .upsell-decline, .skip-upsell'
            }
        };
    }

    /**
     * Wait for the upsell page to load
     * @returns {Promise<boolean>} True if upsell page was detected, false otherwise
     */
    async waitForPageLoad() {
        console.log('Checking for upsell page');
        
        try {
            // First check if the URL contains 'upsell'
            const currentUrl = this.page.url();
            const isUpsellUrl = currentUrl.includes('/upsell/');
            
            if (!isUpsellUrl) {
                console.log('Not on an upsell page based on URL');
                return false;
            }
            
            console.log('Upsell URL detected, waiting for content to load');
            
            // Wait for network idle to ensure page is fully loaded
            await this.page.waitForLoadState('networkidle');
            
            // Take a screenshot for debugging
            await this.page.screenshot({ path: `sales-funnel-upsell-${Date.now()}.png` });
            
            // Wait for upsell product container to be visible
            const productContainer = this.page.locator(this.selectors.product.container);
            const isProductVisible = await productContainer.isVisible({ timeout: 10000 })
                .catch(() => false);
                
            if (!isProductVisible) {
                console.log('Upsell product container not found');
                return false;
            }
            
            console.log('Upsell page loaded successfully');
            return true;
        } catch (error) {
            console.error(`Error waiting for upsell page: ${error.message}`);
            return false;
        }
    }

    /**
     * Check if current page is an upsell page
     * @returns {Promise<boolean>} True if on an upsell page
     */
    async isUpsellPage() {
        try {
            const currentUrl = this.page.url();
            if (!currentUrl.includes('/upsell/')) {
                return false;
            }
            
            // Check for accept/decline buttons
            const acceptButton = this.page.locator(this.selectors.buttons.accept);
            const declineButton = this.page.locator(this.selectors.buttons.decline);
            
            return (await acceptButton.isVisible() || await declineButton.isVisible());
        } catch (error) {
            console.error(`Error checking if upsell page: ${error.message}`);
            return false;
        }
    }

    /**
     * Get the upsell product details
     * @returns {Promise<{name: string, price: string}>}
     */
    async getProductDetails() {
        console.log('Getting upsell product details');
        
        try {
            const name = await this.page.locator(this.selectors.product.title).textContent();
            const price = await this.page.locator(this.selectors.product.price).textContent();
            
            const details = {
                name: name?.trim() || '',
                price: price?.trim() || ''
            };
            
            console.log('Upsell product details:', details);
            return details;
        } catch (error) {
            console.error(`Error getting upsell product details: ${error.message}`);
            return {
                name: 'Upsell Product Unavailable',
                price: 'Price Unavailable'
            };
        }
    }

    /**
     * Accept the upsell offer
     * @returns {Promise<boolean>} True if accepted successfully
     */
    async acceptUpsell() {
        console.log('Accepting upsell offer');
        
        try {
            // Take screenshot before accepting
            await this.page.screenshot({ path: `before-accept-upsell-${Date.now()}.png` });
            
            const acceptButton = this.page.locator(this.selectors.buttons.accept);
            
            if (await acceptButton.isVisible()) {
                await acceptButton.click();
                console.log('Clicked accept upsell button');
                
                // Wait for navigation to confirmation page
                await Promise.race([
                    this.page.waitForURL('**/confirmation/**', { timeout: 30000 }),
                    this.page.waitForURL('**/thank-you/**', { timeout: 30000 })
                ]);
                
                // Take screenshot after accepting
                await this.page.screenshot({ path: `after-accept-upsell-${Date.now()}.png` });
                
                console.log(`Upsell accepted, navigated to: ${this.page.url()}`);
                return true;
            } else {
                console.warn('Accept upsell button not found');
                return false;
            }
        } catch (error) {
            console.error(`Error accepting upsell: ${error.message}`);
            return false;
        }
    }

    /**
     * Decline the upsell offer
     * @returns {Promise<boolean>} True if declined successfully
     */
    async declineUpsell() {
        console.log('Declining upsell offer');
        
        try {
            // Take screenshot before declining
            await this.page.screenshot({ path: `before-decline-upsell-${Date.now()}.png` });
            
            const declineButton = this.page.locator(this.selectors.buttons.decline);
            
            if (await declineButton.isVisible()) {
                await declineButton.click();
                console.log('Clicked decline upsell button');
                
                // Wait for navigation to confirmation page
                await Promise.race([
                    this.page.waitForURL('**/confirmation/**', { timeout: 30000 }),
                    this.page.waitForURL('**/thank-you/**', { timeout: 30000 })
                ]);
                
                // Take screenshot after declining
                await this.page.screenshot({ path: `after-decline-upsell-${Date.now()}.png` });
                
                console.log(`Upsell declined, navigated to: ${this.page.url()}`);
                return true;
            } else {
                console.warn('Decline upsell button not found');
                return false;
            }
        } catch (error) {
            console.error(`Error declining upsell: ${error.message}`);
            return false;
        }
    }
}

module.exports = { SalesFunnelUpsellPage }; 