/**
 * Export all shop page objects for easy importing
 */

// Standard checkout pages
const { ProductPage } = require('./ProductPage');
const { CartPage } = require('./CartPage');
const { CheckoutPage } = require('./CheckoutPage');
const { ConfirmationPage } = require('./ConfirmationPage');
const { PayPalPage } = require('./PayPalPage');

// Sales funnel pages
const { SalesFunnelInitialCheckoutPage } = require('./SalesFunnelInitialCheckoutPage');
const { SalesFunnelPaymentPage } = require('./SalesFunnelPaymentPage');
const { SalesFunnelUpsellPage } = require('./SalesFunnelUpsellPage');
const { SalesFunnelConfirmationPage } = require('./SalesFunnelConfirmationPage');

module.exports = {
    // Standard checkout
    ProductPage,
    CartPage,
    CheckoutPage,
    ConfirmationPage,
    PayPalPage,
    
    // Sales funnel
    SalesFunnelInitialCheckoutPage,
    SalesFunnelPaymentPage,
    SalesFunnelUpsellPage,
    SalesFunnelConfirmationPage
}; 