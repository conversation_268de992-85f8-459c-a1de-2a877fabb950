const fs = require('fs');
const path = require('path');
const axios = require('axios');
require('dotenv').config();

// Notion API configuration
const NOTION_API_KEY = process.env.NOTION_API_KEY;
const TEST_CASE_DATABASE_ID = '1e5a8ba3-b0b5-81a4-bfa2-e3b89d6b21d6';

// Read the test cases from the JSON file
const testCasesFilePath = path.join(__dirname, '..', 'docs', 'apex-shopify-migration-notion-import.json');
const testCasesData = JSON.parse(fs.readFileSync(testCasesFilePath, 'utf8'));

// Configure axios for Notion API
const notionClient = axios.create({
  baseURL: 'https://api.notion.com/v1',
  headers: {
    'Authorization': `Bearer ${NOTION_API_KEY}`,
    'Content-Type': 'application/json',
    'Notion-Version': '2022-06-28'
  }
});

// Function to add a test case to Notion
async function addTestCaseToNotion(testCase) {
  try {
    const response = await notionClient.post('/pages', {
      parent: { database_id: TEST_CASE_DATABASE_ID },
      properties: testCase.properties
    });
    
    console.log(`Successfully added test case: ${testCase.properties.ID.title[0].text.content}`);
    return response.data;
  } catch (error) {
    console.error(`Error adding test case: ${testCase.properties.ID.title[0].text.content}`);
    console.error(error.response ? error.response.data : error.message);
    return null;
  }
}

// Main function to add all test cases
async function addAllTestCases() {
  console.log('Starting to add test cases to Notion...');
  
  for (const testCase of testCasesData.test_cases) {
    await addTestCaseToNotion(testCase);
    // Add a small delay between requests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('Finished adding test cases to Notion.');
}

// Execute the main function
addAllTestCases().catch(error => {
  console.error('An error occurred:', error);
});
