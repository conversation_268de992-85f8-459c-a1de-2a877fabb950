/**
 * BrowserStack Cleanup Utility
 * 
 * This utility provides functions to clean up BrowserStack sessions
 * and ensure that only one session is active at a time.
 */

// Fix ES Module loading issue by using dynamic import
let fetchModule;
async function getFetch() {
    if (!fetchModule) {
        try {
            fetchModule = require('node-fetch');
        } catch (e) {
            const module = await import('node-fetch');
            fetchModule = module.default;
        }
    }
    return fetchModule;
}

/**
 * BrowserStack Cleanup Utility
 */
class BrowserStackCleanup {
    /**
     * Constructor
     */
    constructor() {
        this.browserStackApiUrl = 'https://api.browserstack.com/automate/';
        this.auth = Buffer.from(
            `${process.env.BROWSERSTACK_USERNAME || ''}:${process.env.BROWSERSTACK_ACCESS_KEY || ''}`
        ).toString('base64');
    }

    /**
     * Fetch wrapper with authentication
     * @param {string} url - API URL
     * @param {Object} options - Fetch options
     * @returns {Promise<Response>}
     * @private
     */
    async _fetch(url, options = {}) {
        const fetch = await getFetch();
        return fetch(url, {
            ...options,
            headers: {
                'Authorization': `Basic ${this.auth}`,
                'Content-Type': 'application/json',
                ...(options.headers || {})
            }
        });
    }

    /**
     * Get all active BrowserStack sessions
     * @returns {Promise<Array>} - Array of active sessions
     */
    async getActiveSessions() {
        try {
            const response = await this._fetch(`${this.browserStackApiUrl}sessions.json`);
            const sessions = await response.json();
            
            // Filter to only running sessions
            const activeSessions = sessions.filter(session => 
                session.automation_session.status === 'running'
            );
            
            console.log(`Found ${activeSessions.length} active BrowserStack sessions`);
            return activeSessions;
        } catch (error) {
            console.error('Error fetching BrowserStack sessions:', error);
            return [];
        }
    }

    /**
     * Close a BrowserStack session
     * @param {string} sessionId - BrowserStack session ID
     * @returns {Promise<boolean>} - Success status
     */
    async closeSession(sessionId) {
        if (!sessionId) return false;

        try {
            console.log(`Closing BrowserStack session: ${sessionId}`);
            const response = await this._fetch(
                `${this.browserStackApiUrl}sessions/${sessionId}.json`,
                {
                    method: 'PUT',
                    body: JSON.stringify({
                        status: 'completed'
                    })
                }
            );

            const result = await response.json();
            return result.status === 'completed';
        } catch (error) {
            console.error(`Error closing BrowserStack session ${sessionId}:`, error);
            return false;
        }
    }

    /**
     * Clean up all active BrowserStack sessions
     * @returns {Promise<number>} - Number of sessions closed
     */
    async cleanupAllSessions() {
        const activeSessions = await this.getActiveSessions();
        let closedCount = 0;

        for (const session of activeSessions) {
            const sessionId = session.automation_session.hashed_id;
            const success = await this.closeSession(sessionId);
            
            if (success) {
                closedCount++;
            }
        }

        console.log(`Closed ${closedCount} BrowserStack sessions`);
        return closedCount;
    }

    /**
     * Get BrowserStack plan information
     * @returns {Promise<Object>} - Plan information
     */
    async getPlanInfo() {
        try {
            const response = await this._fetch(`${this.browserStackApiUrl}plan.json`);
            return await response.json();
        } catch (error) {
            console.error('Error fetching BrowserStack plan:', error);
            return {
                parallel_sessions_max_allowed: 1,
                parallel_sessions_running: 0
            };
        }
    }
}

module.exports = { BrowserStackCleanup };
