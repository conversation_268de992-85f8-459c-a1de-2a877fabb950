/**
 * @fileoverview Unified Global teardown for test runs
 * 
 * This file combines functionality from both:
 * - tests/global-teardown.js
 * - tests/common/setup/global-teardown.js
 * 
 * It handles session management, artifact collection, Gemini analysis,
 * test summary generation, result archiving, and cleanup.
 */
const fs = require('fs').promises;
const path = require('path');
const { SessionManager } = require('../src/utils/session-manager');
const { EnhancedBrowserStackHelper } = require('../src/utils/browserstack/enhanced-browserstack-helper');

// Import Gemini analyzer if available
let AnalysisOrchestrator;
try {
    AnalysisOrchestrator = require('../src/utils/gemini/service/AnalysisOrchestrator');
} catch (e) {
    console.warn('Gemini Analysis Orchestrator not available. Visual testing will be skipped.');
}

/**
 * Unified global teardown runs once after all tests.
 * This function cleans up resources, collects artifacts,
 * archives results, and triggers visual analysis with Gemini if configured.
 * @param {Object} config - Playwright configuration
 */
async function globalTeardown(config) {
    console.log('Unified global teardown starting...');
    
    try {
        // Get session manager and helper
        const sessionManager = SessionManager.getInstance();
        const bsHelper = new EnhancedBrowserStackHelper();
        
        // Get test ID for this run
        const testId = process.env.TEST_ID;
        
        if (testId) {
            // Get all screenshot URLs for Gemini analysis
            const screenshots = await sessionManager.getScreenshots(testId);
            console.log(`Found ${screenshots.length} screenshots for analysis`);
            
            // Get the session data
            const sessionData = await sessionManager.getSessionData(testId);
            let browserStackArtifacts = null;
            
            // Get BrowserStack session artifacts
            if (sessionData && sessionData.metadata && sessionData.metadata.browserstackSessionId) {
                const browserstackSessionId = sessionData.metadata.browserstackSessionId;
                console.log(`Retrieving artifacts for BrowserStack session: ${browserstackSessionId}`);
                browserStackArtifacts = await bsHelper.getSessionArtifacts(browserstackSessionId);
            }
            
            // Generate comprehensive test summary
            await generateTestSummary(screenshots, browserStackArtifacts, sessionData);
            
            // Archive test results
            await archiveTestResults();
            
            // Clean up temporary files
            await cleanupTempFiles();
        }
        
        // Clean up all sessions
        await sessionManager.cleanupAllSessions();
        
        console.log('Unified global teardown complete');
    } catch (error) {
        console.error('Error during global teardown:', error);
    }
}

/**
 * Generates a comprehensive test summary with Gemini analysis
 * @param {Array} screenshots - Screenshots for analysis
 * @param {Object} browserStackArtifacts - BrowserStack artifacts
 * @param {Object} sessionData - Session data
 */
async function generateTestSummary(screenshots, browserStackArtifacts, sessionData) {
    try {
        // Get metadata
        const metadataPath = path.join(process.cwd(), 'test-results', 'metadata.json');
        let metadata = {};

        try {
            metadata = JSON.parse(await fs.readFile(metadataPath, 'utf8'));
        } catch (error) {
            console.warn('Warning: Could not read metadata file');
        }
        
        // Prepare Gemini analysis if needed
        let analysisResults = null;
        if (screenshots.length > 0 && process.env.GEMINI_API_KEY && AnalysisOrchestrator) {
            console.log('Preparing Gemini analysis...');
            
            const analysisOrchestrator = new AnalysisOrchestrator.AnalysisOrchestrator({
                includeVisualAnalysis: true,
                includePerformanceAnalysis: process.env.INCLUDE_PERFORMANCE_ANALYSIS === 'true'
            });
            
            // Prepare artifacts for analysis
            const artifacts = {
                platform: process.env.PLATFORM || 'unknown',
                browserStackSessionId: sessionData?.metadata?.browserstackSessionId,
                screenshots: screenshots.map(s => s.url),
                performanceData: browserStackArtifacts ? {
                    videoUrl: browserStackArtifacts.videoUrl,
                    logsUrl: browserStackArtifacts.logsUrl
                } : null
            };
            
            try {
                // Run Gemini analysis
                analysisResults = await analysisOrchestrator.analyze({
                    status: 'completed',
                    errors: []
                }, artifacts);
                
                console.log('Gemini analysis complete');
            } catch (error) {
                console.error('Error during Gemini analysis:', error);
            }
        }
        
        // Prepare summary
        const summary = {
            timestamp: new Date().toISOString(),
            testId: process.env.TEST_ID,
            metadata: metadata,
            browserStack: {
                buildName: process.env.BUILD_NAME || 'Release_1.0',
                buildTag: process.env.BUILD_TAG,
                sessionId: sessionData?.metadata?.browserstackSessionId,
                artifacts: browserStackArtifacts
            },
            screenshots: screenshots,
            testResults: await aggregateTestResults(path.join(process.cwd(), 'test-results')),
            geminiAnalysis: analysisResults
        };

        // Write summary
        await fs.writeFile(
            path.join(process.cwd(), 'test-results', 'summary.json'),
            JSON.stringify(summary, null, 2)
        );
        
        console.log('Test summary generated');
    } catch (error) {
        console.warn('Warning: Error generating test summary:', error);
    }
}

/**
 * Aggregates test results from report files
 * @param {string} resultsPath - Path to test results
 * @returns {Object} Aggregated results
 */
async function aggregateTestResults(resultsPath) {
    const results = {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        duration: 0
    };

    try {
        const files = await fs.readdir(resultsPath);
        
        for (const file of files) {
            if (file.endsWith('.json') && file !== 'metadata.json' && file !== 'summary.json') {
                const filePath = path.join(resultsPath, file);
                try {
                    const content = JSON.parse(await fs.readFile(filePath, 'utf8'));
                    
                    // Check if this is a test results file (has test count properties)
                    if (content.total !== undefined || content.passed !== undefined) {
                        results.total += content.total || 0;
                        results.passed += content.passed || 0;
                        results.failed += content.failed || 0;
                        results.skipped += content.skipped || 0;
                        results.duration += content.duration || 0;
                    }
                } catch (e) {
                    console.warn(`Warning: Could not parse results file ${file}:`, e);
                }
            }
        }
    } catch (error) {
        console.warn('Warning: Error aggregating test results:', error);
    }

    return results;
}

/**
 * Archives test results
 */
async function archiveTestResults() {
    try {
        const archiveDir = path.join(process.cwd(), 'test-archives');
        await fs.mkdir(archiveDir, { recursive: true });

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const buildName = process.env.BUILD_NAME ? `-${process.env.BUILD_NAME}` : '';
        const archivePath = path.join(archiveDir, `results${buildName}-${timestamp}`);

        await fs.mkdir(archivePath, { recursive: true });
        
        // Copy test results to archive
        const sourceDir = path.join(process.cwd(), 'test-results');
        const files = await fs.readdir(sourceDir);
        
        for (const file of files) {
            const sourcePath = path.join(sourceDir, file);
            const targetPath = path.join(archivePath, file);
            
            const stats = await fs.stat(sourcePath);
            if (stats.isDirectory()) {
                await fs.cp(sourcePath, targetPath, { recursive: true });
            } else {
                await fs.copyFile(sourcePath, targetPath);
            }
        }

        console.log(`Test results archived to: ${archivePath}`);
    } catch (error) {
        console.warn('Warning: Error archiving test results:', error);
    }
}

/**
 * Cleans up temporary files
 */
async function cleanupTempFiles() {
    try {
        const tempDirs = [
            path.join(process.cwd(), 'test-results', 'traces'),
            path.join(process.cwd(), 'test-results', 'videos')
        ];

        for (const dir of tempDirs) {
            try {
                const exists = await fs.access(dir).then(() => true).catch(() => false);
                if (exists) {
                    const files = await fs.readdir(dir);
                    for (const file of files) {
                        if (file.includes('temp-') || file.endsWith('.tmp')) {
                            const filePath = path.join(dir, file);
                            await fs.unlink(filePath);
                            console.log(`Removed temporary file: ${filePath}`);
                        }
                    }
                }
            } catch (dirError) {
                console.warn(`Warning: Error cleaning up directory ${dir}:`, dirError);
            }
        }

        console.log('Temporary files cleaned up');
    } catch (error) {
        console.warn('Warning: Error cleaning up temporary files:', error);
    }
}

module.exports = globalTeardown; 