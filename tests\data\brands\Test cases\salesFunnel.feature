Feature: Sales Funnel Flows and Error Handling

        Background:
            Given I load brand configuration
              And I load product data

        @funnel @high-priority
        Scenario: Basic funnel flow with successful upsell
            Given I am on the sales funnel page "total-harmony-basic"
              And I verify the funnel product details
             When I proceed to checkout
              And I fill in the shipping information with "default" user data
              And I use the same address for billing
             Then The shipping method "Domestic tracked" should be selected
              And I verify the shipping cost
             When I enter "stripe_valid" payment details
              And I complete the purchase
             Then I should be redirected to the upsell page
             When I accept the upsell offer
             Then I wait for the order confirmation page to load
              And I verify the order details are correct
              And I verify the order confirmation email

        @funnel @regression @shipping
        Scenario Outline: Verify free shipping threshold with different funnel combinations
            Given I am on the sales funnel page "<funnel_id>"
             When I proceed to checkout
              And I fill in the shipping information with "default" user data
              And I use the same address for billing
             Then The shipping method "Domestic tracked" should be selected
              And I verify the shipping cost is "<initial_shipping>"
             When I enter "stripe_valid" payment details
              And I complete the purchase
             Then I should be redirected to the upsell page
             When I accept the upsell offer
             Then I wait for the order confirmation page to load
              And I verify the shipping cost is "<final_shipping>"

        Examples:
                  | funnel_id           | initial_shipping | final_shipping |
                  | total-harmony-basic | £2.95            | FREE           |
                  | ancient-roots-small | £2.95            | £2.95          |

        @funnel @negative @payment
        Scenario: Handle expired card in funnel checkout
            Given I am on the sales funnel page "total-harmony-basic"
             When I proceed to checkout
              And I fill in the shipping information with "default" user data
              And I use the same address for billing
             Then The shipping method "Domestic tracked" should be selected
             When I enter "stripe_expired" payment details
              And I complete the purchase
             Then I should see an error message indicating the card has expired
              And I should remain on the checkout page

        @funnel @back-button @regression
        Scenario: Handle browser back button during funnel flow
            Given I am on the sales funnel page "total-harmony-basic"
             When I proceed to checkout
              And I fill in the shipping information with "default" user data
              And I complete the purchase
             Then I should be redirected to the upsell page
             When I navigate back in browser
             Then I should be redirected to the upsell page
             When I accept the upsell offer
             Then I wait for the order confirmation page to load
              And I verify the order details are correct

        @ignore @funnel @compatibility @high-priority
        Scenario: Verify product combination restrictions
            Given I am on the sales funnel page "natures-gift-basic"
             When I proceed to checkout
              And I fill in the shipping information with "default" user data
              And I complete the purchase
             Then I should be redirected to the upsell page
              And I verify dietary restriction warnings are displayed
             When I accept the upsell offer
             Then I wait for the order confirmation page to load
              And I verify the product instructions contain all warnings

        @funnel @duplicate-prevention @regression
        Scenario: Prevent duplicate upsell submissions
            Given I am on the sales funnel page "total-harmony-basic"
             When I proceed to checkout
              And I fill in the shipping information with "default" user data
              And I complete the purchase
             Then I should be redirected to the upsell page
             When I click the accept button multiple times
             Then I wait for the order confirmation page to load
              And I verify only one upsell product is in the order

        #todo: add test for auto order conclusion
        #todo: add test for Warehouse aggregate shipments ref https://app.asana.com/0/1207569183609754/1208535720778210/f
