{"mcpServers": {"playwright-mcp": {"command": "npx", "args": ["@executeautomation/mcp-playwright@latest"], "disabled": false, "alwaysAllow": []}, "context7": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"], "disabled": false}, "web-eval-agent": {"command": "uvx", "args": ["--from", "git+https://github.com/Operative-Sh/web-eval-agent.git", "webEvalAgent"], "env": {"OPERATIVE_API_KEY": "op-eCKRtXoF5JCrJO9McypYCHx0Wt_ikAPaYrDIQVdkIAM"}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "alwaysAllow": []}, "mcp-perplexity-search": {"command": "npx", "args": ["-y", "mcp-perplexity-search"], "env": {"PERPLEXITY_API_KEY": "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"}}, "github.com/makenotion/notion-mcp-server": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "npx", "args": ["-y", "@notionhq/notion-mcp-server"], "env": {"OPENAPI_MCP_HEADERS": "{\"Authorization\": \"Bearer 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\", \"Notion-Version\": \"2022-06-28\" }"}, "transportType": "stdio"}, "github.com/pashpashpash/mcp-server-asana": {"autoApprove": ["asana_list_workspaces"], "disabled": false, "timeout": 60, "command": "node", "args": ["C:\\Users\\<USER>\\OneDrive\\Documents\\Cline\\MCP\\mcp-server-asana\\dist\\index.js"], "env": {"ASANA_ACCESS_TOKEN": "********************************************************************"}, "transportType": "stdio"}, "Framelink Figma MCP": {"command": "npx", "args": ["-y", "figma-developer-mcp", "--figma-api-key=*********************************************", "--st<PERSON>"], "disabled": true, "alwaysAllow": []}}}