/*
 * config/base.config.js
 * Base configuration for Playwright tests.
 * This file defines default configuration settings which are extended by brand-specific configurations (e.g., Aeons)
 * Author: [Your Name]
 * Date: [Today's Date]
 */

const path = require('path');

module.exports = {
  // Global test timeout in milliseconds
  timeout: 60000,

  // Retries for failed tests
  retries: 0,

  // Global 'use' options for tests
  use: {
    // Base URL for tests; can be overridden by brand-specific configs
    baseURL: 'https://example.com',
    // Timeout for individual actions
    actionTimeout: 30000,
    // Run tests in headless mode
    headless: true,
    // Default viewport size
    viewport: { width: 1280, height: 720 },
    // Enable trace on first retry
    trace: 'on-first-retry',
    // Capture screenshots only on failure
    screenshot: 'only-on-failure',
    // Retain video on failure
    video: 'retain-on-failure'
  },

  // Reporter configurations
  reporter: [
    ['list'],
    ['html', { outputFolder: 'playwright-report', open: 'never' }],
    ['json', { outputFile: 'test-results/results.json' }]
  ],
}; 