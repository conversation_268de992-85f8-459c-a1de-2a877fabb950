/**
 * Universal test runner for BrowserStack and local tests
 * Provides a simplified interface for running tests
 */
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Load environment variables
try {
  require('dotenv').config();
} catch (error) {
  console.warn('Warning: Could not load .env file');
}

// Parse command line arguments
const args = process.argv.slice(2);
let options = {
  testPath: null,
  platform: process.env.PLATFORM || 'windows-chrome',
  brand: process.env.BRAND || 'aeons',
  env: process.env.TEST_ENV || 'stage',
  tags: null,
  browserstack: process.env.USE_BROWSERSTACK === 'false',
  workers: process.env.TEST_WORKERS || '1',
  dataSet: process.env.TEST_DATA_SET || 'default',
  debug: false
};

// Parse command line arguments
for (let i = 0; i < args.length; i++) {
  const arg = args[i];

  if (arg.startsWith('--')) {
    const [key, value] = arg.slice(2).split('=');
    switch (key) {
      case 'platform':
        options.platform = value;
        break;
      case 'brand':
        options.brand = value;
        break;
      case 'env':
        options.env = value;
        break;
      case 'tag':
      case 'tags':
        options.tags = value;
        break;
      case 'workers':
        options.workers = value;
        break;
      case 'dataset':
        options.dataSet = value;
        break;
      case 'browserstack':
        options.browserstack = value === 'false';
        break;
      case 'debug':
        options.debug = value === 'true';
        break;
      default:
        console.warn(`Unknown option: ${key}`);
    }
  } else {
    options.testPath = arg;
  }
}

// Validate required arguments
if (!options.testPath) {
  console.error('Error: Test path is required');
  console.log('Usage: node run-test.js <test-path> [options]');
  console.log('Options:');
  console.log('  --platform=<platform>     Platform to run tests on (e.g., windows-chrome, samsung-galaxy-s23)');
  console.log('  --brand=<brand>           Brand to test (default: aeons)');
  console.log('  --env=<env>               Environment to test (stage, dev, prod) (default: stage)');
  console.log('  --tags=<tags>             Test tags to filter (e.g., @smoke, @visual)');
  console.log('  --workers=<count>         Number of worker processes (default: 1)');
  console.log('  --dataset=<dataset>       Test data set to use (default: default)');
  console.log('  --browserstack=<bool>     Use BrowserStack (true/false)');
  console.log('  --debug=<bool>            Enable debug mode (true/false)');
  process.exit(1);
}

// Ensure test path exists
if (!fs.existsSync(options.testPath)) {
  console.error(`Error: Test path not found: ${options.testPath}`);
  process.exit(1);
}

// Validate platform against available platforms
const supportedPlatforms = [
  'windows-chrome', 'mac-safari', 'firefox', 'samsung-galaxy-s23', 'iphone-14'
];

if (!supportedPlatforms.includes(options.platform)) {
  console.error(`Error: Invalid platform: ${options.platform}`);
  console.log('Available platforms:');
  console.log('Desktop:', supportedPlatforms.filter(p => !p.includes('galaxy') && !p.includes('iphone')).join(', '));
  console.log('Mobile:', supportedPlatforms.filter(p => p.includes('galaxy') || p.includes('iphone')).join(', '));
  process.exit(1);
}

// Determine if it's a mobile platform
const isMobile = options.platform.includes('galaxy') ||
                options.platform.includes('iphone') ||
                options.platform.includes('android') ||
                options.platform.includes('ios');

// Generate a unique build name
const timestamp = new Date().toISOString().replace(/[:.]/g, '_');
const buildName = `${options.platform}_${options.env}_${timestamp}`;

// Build the command
let command, cmdArgs;

if (options.browserstack) {
  command = 'npx';
  cmdArgs = [
    'browserstack-node-sdk',
    'playwright',
    'test',
    options.testPath,
    `--workers=${options.workers}`,
    `--project="${options.platform}"`
  ];
} else {
  command = 'npx';
  cmdArgs = [
    'playwright',
    'test',
    options.testPath,
    `--workers=${options.workers}`,
    `--project="${options.platform}"`
  ];
}

// Check if we're running a single test file and remove any tag filtering
const isSingleTestFile = options.testPath && !options.testPath.includes('*') && fs.existsSync(options.testPath) && fs.statSync(options.testPath).isFile();
if (isSingleTestFile) {
  console.log('Single test file detected. Will run all tests in this file regardless of tags.');
  // We'll set a flag to skip adding grep filters later
  options.skipGrepForSingleFile = true;
}

// Extract tags from test file if no tags are explicitly provided
if (!options.tags && fs.existsSync(options.testPath)) {
  try {
    console.log(`[DEBUG] Reading file content from: ${options.testPath}`);
    const fileContent = fs.readFileSync(options.testPath, 'utf8');
    console.log(`[DEBUG] File content length: ${fileContent.length} characters`);

    // Log the first 500 characters of the file for debugging
    console.log(`[DEBUG] File content preview: ${fileContent.substring(0, 500)}...`);

    // Try multiple tag formats with detailed logging
    console.log('[DEBUG] Attempting to match tags with different patterns:');

    const pattern1 = /@tags\s+(.+)/;
    const match1 = fileContent.match(pattern1);
    console.log(`[DEBUG] Pattern 1 (${pattern1}): ${match1 ? 'MATCHED' : 'no match'}`);
    if (match1) console.log(`[DEBUG] Pattern 1 match result: ${JSON.stringify(match1)}`);

    const pattern2 = /\* @tags\s+(.+)/;
    const match2 = fileContent.match(pattern2);
    console.log(`[DEBUG] Pattern 2 (${pattern2}): ${match2 ? 'MATCHED' : 'no match'}`);
    if (match2) console.log(`[DEBUG] Pattern 2 match result: ${JSON.stringify(match2)}`);

    const pattern3 = /\/\/ @(\w+)/;
    const match3 = fileContent.match(pattern3);
    console.log(`[DEBUG] Pattern 3 (${pattern3}): ${match3 ? 'MATCHED' : 'no match'}`);
    if (match3) console.log(`[DEBUG] Pattern 3 match result: ${JSON.stringify(match3)}`);

    // Try a more general pattern to find any tags
    const pattern4 = /@(\w+)/g;
    const match4 = [...fileContent.matchAll(pattern4)];
    console.log(`[DEBUG] Pattern 4 (${pattern4}): ${match4.length > 0 ? 'MATCHED' : 'no match'}`);
    if (match4.length > 0) {
      console.log(`[DEBUG] Pattern 4 found ${match4.length} tags:`);
      match4.forEach((m, i) => console.log(`[DEBUG]   Tag ${i+1}: ${m[0]}`));
    }

    // Use the combined approach
    const tagMatch = match1 || match2 || match3;

    if (tagMatch && tagMatch[1]) {
      options.tags = tagMatch[1].trim();
      console.log(`[DEBUG] Successfully extracted tags from file: ${options.tags}`);
    } else {
      console.log('[DEBUG] No tags found with standard patterns. Checking for any @tag in the file.');

      // If no standard tag format is found, look for any @tag in the file
      if (match4.length > 0) {
        // Extract the first tag found (without the @ symbol)
        const firstTag = match4[0][1];
        console.log(`[DEBUG] Using first found tag: @${firstTag}`);
        options.tags = `@${firstTag}`;
      } else {
        console.log('[DEBUG] No tags found in file at all. Will run all tests in the file.');
      }
    }
  } catch (error) {
    console.warn(`[DEBUG] Error reading file: ${error.message}`);
    console.warn(`[DEBUG] Error stack: ${error.stack}`);
    console.log('[DEBUG] Will run all tests in the file due to error.');
  }
}

// If we're running a single test file and we've set the skipGrepForSingleFile flag,
// we'll skip adding any grep filters to ensure all tests in the file run
console.log(`[DEBUG] skipGrepForSingleFile: ${options.skipGrepForSingleFile ? 'true' : 'false'}`);
console.log(`[DEBUG] options.tags: ${options.tags || 'not set'}`);

if (options.skipGrepForSingleFile) {
  console.log('[DEBUG] Skipping tag filtering for single test file to ensure all tests run.');

  // IMPORTANT: For single test files, we'll force run all tests by using a pattern that matches everything
  // This is a workaround for the "No tests found" issue
  console.log('[DEBUG] Adding a universal grep pattern to match all tests');
  cmdArgs.push('--grep', '.*');
} else {
  // For multiple test files or when skipGrepForSingleFile is not set,
  // use the standard approach with tag filtering
  if (options.tags) {
    console.log(`[DEBUG] Processing tags: ${options.tags}`);

    // Extract individual tags
    const tagsArray = options.tags.split(/\s+/);
    console.log(`[DEBUG] Split tags into array: ${JSON.stringify(tagsArray)}`);

    // For a single tag, just use it directly
    if (tagsArray.length === 1) {
      console.log(`[DEBUG] Adding grep filter for single tag: ${tagsArray[0]}`);
      cmdArgs.push('--grep', tagsArray[0]);
    }
    // For multiple tags, use the first one and note the limitation
    else {
      console.log(`[DEBUG] Multiple tags found. Using only the first tag (${tagsArray[0]}) for filtering.`);
      console.log('[DEBUG] To use multiple tags, specify them explicitly with --tags.');
      cmdArgs.push('--grep', tagsArray[0]);
    }
  } else {
    console.log('[DEBUG] No tags specified. Adding a universal grep pattern to match all tests.');
    cmdArgs.push('--grep', '.*');
  }
}

// Enable debug mode if requested
if (options.debug) {
  cmdArgs.push('--debug');
}

// Set up environment variables
const env = {
  ...process.env,
  PLATFORM: options.platform,
  BRAND: options.brand,
  TEST_ENV: options.env,
  TEST_DATA_SET: options.dataSet,
  BUILD_NAME: buildName,
  // BrowserStack specific settings
  BROWSERSTACK_SDK_ENABLED: options.browserstack ? 'true' : 'false',
  // Debug settings
  BROWSERSTACK_DEBUG: options.debug ? 'true' : 'false',
  // Set mobile flag
  IS_MOBILE: isMobile ? 'true' : 'false'
};

// FINAL SAFETY CHECK: Make sure we have a grep pattern that will match something
// This is a last-resort fallback to prevent "No tests found" errors
let hasGrepPattern = false;
for (let i = 0; i < cmdArgs.length; i++) {
  if (cmdArgs[i] === '--grep') {
    hasGrepPattern = true;

    // If we're running a single file, we need to ensure the grep pattern will match something
    if (options.skipGrepForSingleFile) {
      // According to Playwright docs, we can use a regex lookahead to create a pattern
      // that will match either the tag OR anything (to ensure at least one test runs)
      console.log('[DEBUG] Modifying grep pattern to ensure tests run in single file');

      // Get the original pattern
      const originalPattern = cmdArgs[i+1];

      // If it's a tag (starts with @), create a pattern that matches either the tag OR anything
      if (originalPattern && originalPattern.startsWith('@')) {
        // Create a pattern that matches either the tag OR anything
        // This uses regex lookahead to create an OR condition
        cmdArgs[i+1] = `(?=${originalPattern})|(?=.*)`;
        console.log(`[DEBUG] Modified grep pattern to: ${cmdArgs[i+1]}`);
      }
    }
    break;
  }
}

// If no grep pattern was added and we're running a single file, add a universal one
if (!hasGrepPattern && options.skipGrepForSingleFile) {
  console.log('[DEBUG] No grep pattern found. Adding universal pattern as fallback.');
  cmdArgs.push('--grep', '.*');
}

// Log execution details
console.log('==================================================');
console.log('Universal test runner');
console.log('==================================================');
console.log(`Test path:     ${options.testPath}`);
console.log(`Platform:      ${options.platform}`);
console.log(`Device type:   ${isMobile ? 'Mobile' : 'Desktop'}`);
console.log(`Brand:         ${options.brand}`);
console.log(`Environment:   ${options.env}`);
console.log(`Data set:      ${options.dataSet}`);
console.log(`Tags:          ${options.tags || 'none'}`);
console.log(`Using:         ${options.browserstack ? 'BrowserStack SDK' : 'Local Playwright'}`);
console.log(`Build name:    ${buildName}`);
console.log(`Workers:       ${options.workers}`);
console.log(`Debug mode:    ${options.debug ? 'enabled' : 'disabled'}`);
console.log('--------------------------------------------------');
console.log(`Command: ${command} ${cmdArgs.join(' ')}`);
console.log('==================================================');

// Run the command
const child = spawn(command, cmdArgs, {
  env,
  stdio: 'inherit',
  shell: true
});

child.on('close', (code) => {
  console.log(`Test exited with code ${code}`);
  process.exit(code);
});
