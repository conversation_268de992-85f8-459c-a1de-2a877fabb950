# Production dataset overrides for aeons brand
products:
  # Override specific product data for production environment
  ancient_roots_olive_oil:
    # You can override any product properties as needed
    # For example, production might have different prices
    flavors:
      classic:
        prices:
          one_time:
            minimum: 
              price: 59.95
              quantity: 1
              savings: null
            medium:
              price: 152.87
              quantity: 3
              savings: "15%"
            maximum:
              price: 269.77
              quantity: 6
              savings: "25%"
          subscription:
            minimum:
              price: 47.96
              quantity: 1
              savings: "20%"
            medium:
              price: 122.30
              quantity: 3
              savings: "32%"
            maximum:
              price: 215.82
              quantity: 6
              savings: "40%"

test_data:
  # Override specific test data for production environment
  test_users:
    default:
      firstName: "Test"
      lastName: "User"
      email: "<EMAIL>"
      phone: "9876543210"
      address: "456 Production Rd"
      city: "London"
      postcode: "W1A 1AA"
      country: "GB"
  
  # Add any other test data overrides as needed
  payment_methods:
    # Payment method overrides for production if needed
  
  shipping_methods:
    # Shipping method overrides for production if needed
  
  funnel_configs:
    # Funnel configuration overrides for production if needed 