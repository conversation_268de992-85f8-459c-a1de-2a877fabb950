/**
 * @fileoverview HomePage page object for main site homepage
 */
class HomePage {
  /**
   * Initialize page object
   * @param {import('@playwright/test').Page} page Playwright page object
   */
  constructor(page) {
    this.page = page;
    this.initializeSelectors();
  }

  /**
   * Initialize page selectors
   * @private
   */
  initializeSelectors() {
    this.selectors = {
      header: {
        logo: '[data-testid="header-logo"]',
        nav: '[data-testid="main-nav"]',
        cart: '[data-testid="cart-icon"]',
        search: '[data-testid="search-icon"]',
        menu: '[data-testid="menu-icon"]'
      },
      hero: {
        container: '[data-testid="hero-section"]',
        title: '[data-testid="hero-title"]',
        subtitle: '[data-testid="hero-subtitle"]',
        cta: '[data-testid="hero-cta"]'
      },
      products: {
        grid: '[data-testid="products-grid"]',
        card: '[data-testid="product-card"]',
        title: '[data-testid="product-title"]',
        price: '[data-testid="product-price"]',
        image: '[data-testid="product-image"]',
        addToCart: '[data-testid="add-to-cart-button"]'
      },
      footer: {
        container: '[data-testid="footer"]',
        links: '[data-testid="footer-links"]',
        social: '[data-testid="social-links"]',
        newsletter: '[data-testid="newsletter-signup"]'
      }
    };
  }

  /**
   * Navigate to home page
   * @param {string} [baseUrl] Optional base URL, defaults to environment variable or fallback
   * @returns {Promise<void>}
   */
  async goto(baseUrl) {
    const url = baseUrl || process.env.BASE_URL || 'https://aeonstest.info';
    await this.page.goto(url);
    await this.waitForLoad();
  }

  /**
   * Wait for page to be fully loaded
   * @returns {Promise<void>}
   */
  async waitForLoad() {
    try {
      // Wait for the main navigation as a baseline indicator that the page has loaded
      await this.page.waitForSelector(this.selectors.header.nav, { 
        timeout: 15000, 
        state: 'attached' 
      });
    } catch (error) {
      console.warn('Main navigation not found within 15 seconds, proceeding without it.');
    }

    // Wait for network to be idle
    await this.page.waitForLoadState('networkidle', { timeout: 30000 }).catch(e => {
      console.warn('Network did not reach idle state within timeout, continuing anyway');
    });
  }

  /**
   * Get main navigation items
   * @returns {Promise<string[]>} Array of navigation item texts
   */
  async getNavigationItems() {
    const navItems = this.page.locator(`${this.selectors.header.nav} li`);
    return await navItems.evaluateAll(items => items.map(item => item.textContent.trim()));
  }

  /**
   * Click on a navigation item by text
   * @param {string} text The text of the navigation item to click
   */
  async clickNavigationItem(text) {
    await this.page.click(`${this.selectors.header.nav} li:has-text("${text}")`);
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Get featured products from the homepage
   * @returns {Promise<Array<{title: string, price: string, image: string}>>}
   */
  async getFeaturedProducts() {
    const productCards = this.page.locator(this.selectors.products.card);
    const count = await productCards.count();
    
    const products = [];
    for (let i = 0; i < count; i++) {
      const card = productCards.nth(i);
      products.push({
        title: await card.locator(this.selectors.products.title).textContent(),
        price: await card.locator(this.selectors.products.price).textContent(),
        image: await card.locator(this.selectors.products.image).getAttribute('src')
      });
    }
    
    return products;
  }

  /**
   * Open cart
   */
  async openCart() {
    await this.page.click(this.selectors.header.cart);
    await this.page.waitForSelector('[data-testid="cart-drawer"]');
  }

  /**
   * Verify Google Tag Manager is loaded
   */
  async verifyGoogleTagManager() {
    await this.page.waitForFunction(() => {
      return window.dataLayer !== undefined;
    });
  }

  /**
   * Search for a product
   * @param {string} searchTerm The term to search for
   */
  async search(searchTerm) {
    await this.page.click(this.selectors.header.search);
    await this.page.waitForSelector('[data-testid="search-input"]');
    await this.page.fill('[data-testid="search-input"]', searchTerm);
    await this.page.press('[data-testid="search-input"]', 'Enter');
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Subscribe to newsletter
   * @param {string} email Email address to subscribe
   */
  async subscribeToNewsletter(email) {
    await this.page.fill(`${this.selectors.footer.newsletter} input[type="email"]`, email);
    await this.page.click(`${this.selectors.footer.newsletter} button[type="submit"]`);
    await this.page.waitForSelector('[data-testid="newsletter-success"]');
  }
}

module.exports = { HomePage };
