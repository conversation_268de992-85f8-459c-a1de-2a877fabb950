/**
 * @fileoverview Aeons basic smoke test
 * @tags @smoke @basic
 */
const { test, expect } = require('../fixtures/unified-fixture');

test.describe('Aeons Smoke Tests', () => {
    let testData;

    test.beforeEach(async ({ testDataManager }) => {
        // Initialize test data using centralized TestDataManager
        testData = {
            product: testDataManager.getProduct('ancient_roots_olive_oil'),
            user: testDataManager.getUser('default'),
            paymentMethod: testDataManager.getPaymentMethod('stripe_valid')
        };

        console.log('Test data initialized:', {
            product: testData.product.name,
            user: `${testData.user.firstName} ${testData.user.lastName}`,
            paymentMethod: `****${testData.paymentMethod.cardNumber.slice(-4)}`,
            baseUrl: testDataManager.getBaseUrl()
        });
    });

    test('Aeons Smoke Test', async ({ page, bsHelper, testDataManager }) => {
        await test.step('Navigate to product page', async () => {
            // Open product page using centralized URL management
            const baseUrl = testDataManager.getBaseUrl();
            const productUrl = `${baseUrl}${testData.product.urlPath}`;
            console.log('Opening URL:', productUrl);
            await page.goto(productUrl);
            await page.waitForLoadState('networkidle');

            // Take screenshot after page loads
            await bsHelper.takeScreenshot(page, 'product-page-loaded');

            // Verify product title and name
            const productTitle = page.locator('.main p.title');
            await expect(productTitle).toContainText(testData.product.name);
        });

        await test.step('Select product options', async () => {
            // Select minimum quantity
            const minQuantity = testData.product.options.quantities.minimum.numberOfItems;
            console.log('Selecting minimum quantity:', minQuantity);
            await page.locator(`[data-value="${minQuantity}"]`).click();

            // Get price data - handle both simple and complex product structures
            let minPrice, medPrice;
            if (testData.product.flavors) {
                // Complex structure with flavors (like ancient_roots_olive_oil)
                const defaultFlavor = Object.keys(testData.product.flavors)[0];
                minPrice = testData.product.flavors[defaultFlavor].prices.one_time.minimum.price;
                medPrice = testData.product.flavors[defaultFlavor].prices.one_time.medium.price;
            } else {
                // Simple structure (like newer products)
                minPrice = testData.product.prices.one_time.minimum;
                medPrice = testData.product.prices.one_time.medium;
            }

            // Verify minimum price
            const priceElement = page.locator('#product-price');
            console.log('Verifying minimum price:', minPrice);
            await expect(priceElement).toContainText(`£${minPrice.toFixed(2)}`);

            // Take screenshot of minimum quantity selection
            await bsHelper.takeScreenshot(page, 'min-quantity-selection');

            // Select medium quantity
            const medQuantity = testData.product.options.quantities.medium.numberOfItems;
            console.log('Selecting medium quantity:', medQuantity);
            await page.locator(`[data-value="${medQuantity}"]`).click();

            // Verify medium price
            console.log('Verifying medium price:', medPrice);
            await expect(priceElement).toContainText(`£${medPrice.toFixed(2)}`);

            // Take screenshot of medium quantity selection
            await bsHelper.takeScreenshot(page, 'med-quantity-selection');

            // Select one-time purchase
            const purchaseType = testData.product.options.purchase_types.one_time;
            console.log('Selecting purchase type:', purchaseType);
            await page.locator(`[data-purchase-type="${purchaseType}"]`).click();

            // Wait for visual stability after all selections
            await bsHelper.waitForVisualStability(page);
        });

        await test.step('Add to cart and proceed to checkout', async () => {
            // Click add to cart
            await page.locator('#add-to-cart-button').click();

            // Wait for cart drawer
            await page.waitForSelector('#cart-drawer', { state: 'visible', timeout: 10000 });

            // Take screenshot of cart drawer
            await bsHelper.takeScreenshot(page, 'cart-drawer');

            // Verify cart details
            const medQuantity = testData.product.options.quantities.medium.numberOfItems;
            const purchaseType = testData.product.options.purchase_types.one_time;

            await expect(page.locator('.cart-item-name')).toContainText(testData.product.name);
            await expect(page.locator('.cart-item-quantity')).toHaveValue(medQuantity.toString());
            await expect(page.locator('.cart-item-purchase-type')).toContainText(purchaseType);

            // Proceed to checkout
            await page.locator('#proceed-to-checkout').click();

            // Take screenshot of checkout page
            await bsHelper.takeScreenshot(page, 'checkout-page');
        });

        await test.step('Fill checkout form', async () => {
            // Fill checkout form
            console.log('Filling checkout form with user data:', testData.user);
            await page.locator('#customer-email').fill(testData.user.email);
            await page.locator('#billing-first-name').fill(testData.user.firstName);
            await page.locator('#billing-last-name').fill(testData.user.lastName);
            await page.locator('#billing-phone').fill(testData.user.phone);
            await page.locator('#billing-address').fill(testData.user.address);
            await page.locator('#billing-city').fill(testData.user.city);
            await page.locator('#billing-postcode').fill(testData.user.postcode);
            await page.locator('#billing-country').selectOption(testData.user.country);

            // Use same billing address for shipping
            await page.locator('#same-as-billing').check();

            // Take screenshot of filled form
            await bsHelper.takeScreenshot(page, 'checkout-form-filled');

            // Select shipping method
            await page.locator('input[name="shipping_method"]').first().check();

            // Wait for visual stability after form fill
            await bsHelper.waitForVisualStability(page);
        });

        await test.step('Complete payment', async () => {
            // Fill payment information in Stripe iframe
            console.log('Filling payment information');
            const stripeFrame = page.frameLocator('iframe[name*="stripe-card-number"]').first();
            await stripeFrame.locator('[data-elements-stable-field-name="cardNumber"]').fill(testData.paymentMethod.cardNumber);

            const expiryFrame = page.frameLocator('iframe[name*="stripe-card-expiry"]').first();
            await expiryFrame.locator('[data-elements-stable-field-name="cardExpiry"]').fill(testData.paymentMethod.expiry);

            const cvcFrame = page.frameLocator('iframe[name*="stripe-card-cvc"]').first();
            await cvcFrame.locator('[data-elements-stable-field-name="cardCvc"]').fill(testData.paymentMethod.cvc);

            // Take screenshot before completing order
            await bsHelper.takeScreenshot(page, 'before-order-completion');

            // Complete order
            await page.locator('#complete-order').click();
        });

        await test.step('Verify order confirmation', async () => {
            // Wait for and verify order confirmation
            await page.waitForSelector('#order-confirmation', { state: 'visible', timeout: 30000 });

            // Take screenshot of order confirmation
            await bsHelper.takeScreenshot(page, 'order-confirmation');

            // Verify order details
            console.log('Verifying order details');
            await expect(page.locator('#order-product-name')).toContainText(testData.product.name);
            await expect(page.locator('#order-purchase-type')).toContainText(testData.product.options.purchase_types.one_time);
            await expect(page.locator('#order-customer-email')).toContainText(testData.user.email);
            await expect(page.locator('#order-shipping-name')).toContainText(`${testData.user.firstName} ${testData.user.lastName}`);
            await expect(page.locator('#order-shipping-address')).toContainText(testData.user.address);
            await expect(page.locator('#order-shipping-city')).toContainText(testData.user.city);
            await expect(page.locator('#order-shipping-postcode')).toContainText(testData.user.postcode);

            // Verify total price
            const medPrice = testData.product.prices.one_time.medium;
            console.log('Verifying total price:', medPrice);
            await expect(page.locator('#order-total')).toContainText(`£${medPrice.toFixed(2)}`);

            // Wait for visual stability on confirmation page for accurate screenshots
            await bsHelper.waitForVisualStability(page);
        });
    });
});
