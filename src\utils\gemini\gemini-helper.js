/**
 * @fileoverview Enhanced Gemini AI integration combining existing components
 */

const { GeminiAnalysisHelper } = require('./analysis-helper');
const { ANALYSIS_PROMPTS } = require('./prompts');
const { ResultProcessor } = require('./result-processor');
const path = require('path');

class GeminiHelper {
    constructor() {
        this.analysisHelper = new GeminiAnalysisHelper();
        this.resultProcessor = new ResultProcessor();
    }

    async analyzeTestResults({ screenshots, browserLogs, testName, testResult }) {
        try {
            // Analyze each screenshot
            for (const screenshot of screenshots) {
                const analysis = await this.analysisHelper.analyzeScreenshot(
                    path.join('test-results', 'screenshots', screenshot),
                    ANALYSIS_PROMPTS.VISUAL_ANALYSIS
                );
                this.resultProcessor.addVisualAnalysis(screenshot, analysis);
            }

            // Analyze performance data
            if (browserLogs) {
                const performanceAnalysis = await this.analysisHelper.analyzeScreenshot(
                    null,
                    ANALYSIS_PROMPTS.PERFORMANCE_ANALYSIS,
                    browserLogs
                );
                this.resultProcessor.addPerformanceData(browserLogs, performanceAnalysis);
            }

            // If test failed, analyze errors
            if (testResult === 'failed') {
                const errorAnalysis = await this.analysisHelper.analyzeScreenshot(
                    path.join('test-results', 'screenshots', screenshots[screenshots.length - 1]),
                    ANALYSIS_PROMPTS.ERROR_ANALYSIS
                );
                this.resultProcessor.addError(testResult, errorAnalysis);
            }

            // Generate and return final report
            const report = this.resultProcessor.generateReport();
            
            // Save report to file
            const fs = require('fs');
            const reportPath = path.join('test-results', `${testName}-analysis.json`);
            fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

            return report;
        } catch (error) {
            console.error('Error in Gemini analysis:', error);
            return {
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
}

module.exports = { GeminiHelper }; 