class PayPalPage {
    constructor(page) {
        this.page = page;
        this.selectors = {
            emailField: '#email',
            passwordField: '#password',
            nextButton: '#btnNext',
            loginButton: '#btnLogin',
            continueButton: '#continueButton',
            paymentAmount: '.amount',
            paymentCurrency: '.currency'
        };
    }

    async waitForPayPalPage() {
        await this.page.waitForURL('**/paypal.com/**', { timeout: 60000 });
        await this.page.waitForSelector(this.selectors.emailField, { timeout: 30000 });
    }

    async login(email, password) {
        await this.waitForPayPalPage();
        await this.page.fill(this.selectors.emailField, email);
        await this.page.click(this.selectors.nextButton);
        await this.page.waitForSelector(this.selectors.passwordField, { timeout: 30000 });
        await this.page.fill(this.selectors.passwordField, password);
        await this.page.click(this.selectors.loginButton);
        await this.page.waitForSelector(this.selectors.continueButton, { timeout: 30000 });
    }

    async getPaymentAmount() {
        const amountElement = await this.page.$(this.selectors.paymentAmount);
        if (!amountElement) return null;

        const amountText = await amountElement.textContent();
        const currencyElement = await this.page.$(this.selectors.paymentCurrency);
        const currencyText = currencyElement ? await currencyElement.textContent() : '';

        return {
            amount: amountText.trim(),
            currency: currencyText.trim()
        };
    }

    async continueToReviewOrder() {
        await this.page.click(this.selectors.continueButton);
        // Wait for PayPal to process and redirect back
        await this.page.waitForNavigation({ timeout: 60000 });
    }

    async abandonCheckout() {
        // Simply close the page to simulate abandoning the checkout
        await this.page.close();
    }
}

module.exports = { PayPalPage };