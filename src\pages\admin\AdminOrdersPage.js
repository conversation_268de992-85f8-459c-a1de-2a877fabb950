class AdminOrdersPage {
    constructor(page) {
        this.page = page;
        this.selectors = {
            ordersTable: 'table.table',
            orderRow: 'table.table tbody tr',
            orderNumberCell: 'td:nth-child(1)',
            orderDateCell: 'td:nth-child(2)',
            orderCustomerCell: 'td:nth-child(3)',
            orderStateCell: 'td:nth-child(4)',
            orderPaymentStateCell: 'td:nth-child(5)',
            orderShippingStateCell: 'td:nth-child(6)',
            orderTotalCell: 'td:nth-child(7)',
            searchField: 'input[name="criteria[search][value]"]',
            searchButton: 'button[type="submit"]'
        };
    }

    async navigate() {
        await this.page.goto('/admin/orders');
        await this.page.waitForSelector(this.selectors.ordersTable);
    }

    async searchForOrder(searchTerm) {
        await this.page.fill(this.selectors.searchField, searchTerm);
        await this.page.click(this.selectors.searchButton);
        await this.page.waitForSelector(this.selectors.ordersTable);
    }

    async getOrderByNumber(orderNumber) {
        const rows = await this.page.$$(this.selectors.orderRow);
        for (const row of rows) {
            const numberText = await row.$eval(this.selectors.orderNumberCell, el => el.textContent.trim());
            if (numberText === orderNumber) {
                return row;
            }
        }
        return null;
    }

    async getOrderDetails(orderNumber) {
        const row = await this.getOrderByNumber(orderNumber);
        if (!row) return null;

        const customerText = await row.$eval(this.selectors.orderCustomerCell, el => el.textContent.trim());
        const stateText = await row.$eval(this.selectors.orderStateCell, el => el.textContent.trim());
        const paymentStateText = await row.$eval(this.selectors.orderPaymentStateCell, el => el.textContent.trim());
        const shippingStateText = await row.$eval(this.selectors.orderShippingStateCell, el => el.textContent.trim());
        const totalText = await row.$eval(this.selectors.orderTotalCell, el => el.textContent.trim());

        return {
            number: orderNumber,
            customer: customerText,
            state: stateText,
            paymentState: paymentStateText,
            shippingState: shippingStateText,
            total: totalText
        };
    }

    async openOrderDetails(orderNumber) {
        const row = await this.getOrderByNumber(orderNumber);
        if (!row) throw new Error(`Order #${orderNumber} not found`);

        await row.$eval(this.selectors.orderNumberCell + ' a', link => link.click());
        await this.page.waitForURL(`**/admin/orders/${orderNumber}`);
    }
}

module.exports = { AdminOrdersPage };