const { test, expect } = require('@playwright/test');
test.describe('Visual Comparison Tests', () => {
    test.beforeEach(async ({ page }) => {
        page.setDefaultTimeout(30000);
    });

    test('Compare Product Pages', async ({ page }) => {
        await page.goto('https://www.aeons.com/products/dark-spot-vanish');
        await page.waitForLoadState('networkidle');
        await page.evaluate(() => document.fonts.ready);
        await page.waitForTimeout(60000);
        await expect(page).toHaveURL(/dark-spot-vanish/);
    });

    test.afterAll(async ({ page }) => {
        try {
            const sessionDetails = await JSON.parse(await page.evaluate(
                () => {},
                `browserstack_executor: ${JSON.stringify({action: 'getSessionDetails'})}`
            ));
            console.log('BrowserStack session details:', sessionDetails);
        } catch (err) {
            console.error('Unable to retrieve session details:', err);
        }
    });
});
