/**
 * @fileoverview Performance analysis utilities for Gemini
 */

const { prompts } = require('../config/prompts');

class PerformanceAnalyzer {
    /**
     * Constructor for PerformanceAnalyzer
     * @param {Object} geminiService Gemini service instance
     */
    constructor(geminiService) {
        this.geminiService = geminiService;
    }

    /**
     * Analyze performance data
     * @param {Object} data Performance data to analyze
     * @param {Object} options Analysis options
     * @returns {Promise<Object>} Analysis results
     */
    async analyze(data, options = {}) {
        try {
            const prompt = prompts.performanceAnalysis;
            const analysis = await this.geminiService.analyzePerformance(data, prompt);

            return {
                data,
                analysis,
                metrics: this.extractMetrics(data),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error analyzing performance data:', error);
            return {
                data,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Extract key metrics from performance data
     * @private
     * @param {Object} data Performance data
     * @returns {Object} Extracted metrics
     */
    extractMetrics(data) {
        const metrics = {
            loadTime: 0,
            resourceCount: 0,
            networkRequests: 0,
            cpuUsage: 0,
            memoryUsage: 0
        };

        try {
            // Extract metrics from data object
            // This is a placeholder implementation
            if (data.timing) {
                metrics.loadTime = data.timing.loadEventEnd - data.timing.navigationStart;
            }
            if (data.resources) {
                metrics.resourceCount = data.resources.length;
                metrics.networkRequests = data.resources.filter(r => r.type === 'xhr').length;
            }
            if (data.performance) {
                metrics.cpuUsage = data.performance.cpu || 0;
                metrics.memoryUsage = data.performance.memory || 0;
            }
        } catch (error) {
            console.error('Error extracting metrics:', error);
        }

        return metrics;
    }
}

module.exports = { PerformanceAnalyzer }; 