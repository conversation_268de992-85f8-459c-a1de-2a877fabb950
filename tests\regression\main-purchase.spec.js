/**
 * @fileoverview Test implementation for TC-001: Purchase on Normal Site
 * @tags @smoke @regression @purchase @main
 */
const {test, expect} = require('../fixtures/workflows/purchase-flow-fixture');
const {validateTestDataEnhanced} = require('../utils/data-validator');
const {GeminiAnalysisHelper} = require('../../src/utils/gemini/analysis-helper');
const {VisualAnalysisHelper} = require('../../src/utils/visual-analisys-helper');

// Use the enhanced purchase flow fixture directly
const testWithPurchase = test;

testWithPurchase.describe('AEONS Test Suites', () => {
    // Store test data at the suite level
    let testData;

    testWithPurchase.beforeEach(async ({testDataManager}) => {
        // Initialize test data before each test
        testData = {
            product: testDataManager.getProduct('ancient_roots_olive_oil'),
            user: testDataManager.getUser('default'),
            paymentMethod: testDataManager.getPaymentMethod('stripe_valid'),
            shippingAddressOption: testDataManager.getUser('default').shippingAddressOption || 'same',
            expectedShippingMethodValue: testDataManager.getUser('default').expectedShippingMethodValue || null
        };

        // --- DEBUG LOGGING ---
        console.log('--- Test Data Initialization ---');
        console.log(`User Country (Billing): ${testData.user?.country}`);
        console.log(`Shipping Address Option: ${testData.shippingAddressOption}`);
        console.log(`Expected Shipping Method Value: ${testData.expectedShippingMethodValue}`);
        console.log('-----------------------------');
        // --- END DEBUG LOGGING ---

        // Validate test data
        validateTestDataEnhanced(testData);
    });

    testWithPurchase.describe('Product Purchase Flows', () => {
        testWithPurchase('TC-001: Successful one-time purchase with normal card @stage_one_time_smoke', async ({
                                                                                                           page,
                                                                                                           testDataManager,
                                                                                                           emailHelper,
                                                                                                           pageObjectFactory,
                                                                                                           purchaseFlow
                                                                                                       }) => {
            // Increase the timeout for this test to prevent timeouts
            testWithPurchase.setTimeout(120000);

            const pageObjects = pageObjectFactory.getAll();
            const {productPage, cartPage, checkoutPage, confirmationPage} = pageObjects;

            // Debug: Log test data
            console.log('Test Data:', {
                product: {
                    name: testData.product.name,
                    urlPath: testData.product.urlPath,
                    purchaseTypes: testData.product.options.purchaseTypes,
                    flavors: Object.keys(testData.product.flavors || {})
                }
            });

            // 1. Navigate to product page
            await testWithPurchase.step('Navigate to product page', async () => {
                await purchaseFlow.navigateToProduct(testData);
            });

            // 2. Analyze page structure before selection
            await testWithPurchase.step('Analyze purchase options', async () => {
                const {expectedTypes, availableTypes} = await purchaseFlow.analyzePurchaseOptions(testData);
                expect(availableTypes.sort()).toEqual(expectedTypes.sort());
            });

            // 3. Select flavor if available
            await testWithPurchase.step('Select flavor if available', async () => {
                await purchaseFlow.selectFlavor(testData);
            });

            // 4. Set quantity
            await testWithPurchase.step('Set quantity', async () => {
                await purchaseFlow.setQuantity(testData);
            });

            // 5. Select one-time purchase
            await testWithPurchase.step('Select one-time purchase', async () => {
                await purchaseFlow.selectPurchaseType(testData, 'oneTime');
            });

            // 6. Store expected values for verification
            await testWithPurchase.step('Store expected values', async () => {
                await purchaseFlow.storeExpectedValues(testData);
            });

            // 7. Add to cart and proceed to checkout
            await testWithPurchase.step('Add to cart and proceed to checkout', async () => {
                const cartDetails = await purchaseFlow.addToCartAndProceedToCheckout(testData);
                console.log('Cart Details:', cartDetails);

                // Verify cart details match expected values
                expect(cartDetails.name).toContain(testData.product.name);
                if (testData.expectedFlavorName) {
                    expect(cartDetails.variantInfo).toContain(testData.expectedFlavorName);
                }
                expect(cartDetails.quantity).toBe(testData.expectedQuantity);
                expect(cartDetails.price).toBe(testData.expectedPrice);
            });

            // 8. Fill shipping information
            await testWithPurchase.step('Fill shipping information', async () => {
                await purchaseFlow.fillShippingInformation(testData);
            });

            // 9. Verify shipping method and cost
            await testWithPurchase.step('Verify shipping method and cost', async () => {
                const shippingDetails = await purchaseFlow.verifyShippingMethodAndCost(testData);

                // Verify shipping details are present
                expect(shippingDetails.method).toBeTruthy();
                expect(shippingDetails.cost).toBeTruthy();
                expect(shippingDetails.orderSummary.subtotal).toBeTruthy();
                expect(shippingDetails.orderSummary.total).toBeTruthy();
            });

            // 10. Complete payment and order
            await testWithPurchase.step('Complete payment and order', async () => {
                await purchaseFlow.completePaymentAndOrder(testData);
            });

            // 11. Verify order confirmation
            await testWithPurchase.step('Verify order confirmation', async () => {
                const orderDetails = await purchaseFlow.verifyOrderConfirmation(testData);

                // Verify order details
                // Note: orderNumber is not available on the confirmation page, only in email
                expect(orderDetails.items).toBeTruthy();
                expect(orderDetails.totals).toBeTruthy();
                expect(orderDetails.shipping).toBeTruthy();
                expect(orderDetails.billing).toBeTruthy();
            });

            // 12. Verify email confirmation
            await testWithPurchase.step('Verify email confirmation', async () => {
                const emailResult = await purchaseFlow.verifyEmailConfirmation(testData, emailHelper, {});

                // Email verification is optional, so we don't fail the test if it doesn't work
                console.log(`Email verification result: ${emailResult}`);
                expect(emailResult).toBe(true);
            });
        });

        testWithPurchase(`Successful one-time purchase with normal card for dev environment @dev_one_time_smoke`, async ({
                                                                                                                             page,
                                                                                                                             pageObjectFactory,
                                                                                                                             purchaseFlow,
                                                                                                                             testDataManager,
                                                                                                                             emailHelper
                                                                                                                         }) => {
            const pageObjects = pageObjectFactory.getAll();
            const {productPage, cartPage, checkoutPage} = pageObjects;

            // Initialize test data for dev environment
            const testData = testDataManager.getTestData();
            testData.environment = 'dev'; // Set environment to dev

            // Add required properties for validation
            if (!testData.user) {
                testData.user = testDataManager.getUser('default');
            }
            if (!testData.paymentMethod) {
                testData.paymentMethod = testDataManager.getPaymentMethod('stripe_valid');
            }

            // Initialize the test data with the purchaseFlow helper
            const enhancedTestData = purchaseFlow.initTestData(testData);

            console.log('Running dev environment test - payment methods not available');

            // 1. Navigate to product page
            await testWithPurchase.step('Navigate to product page', async () => {
                await purchaseFlow.navigateToProduct(enhancedTestData);
            });

            // 2. Analyze page structure before selection
            await testWithPurchase.step('Analyze purchase options', async () => {
                const {expectedTypes, availableTypes} = await purchaseFlow.analyzePurchaseOptions(enhancedTestData);
                expect(availableTypes.sort()).toEqual(expectedTypes.sort());
            });

            // 3. Select flavor if available
            await testWithPurchase.step('Select flavor if available', async () => {
                await purchaseFlow.selectFlavor(enhancedTestData);
            });

            // 4. Set quantity
            await testWithPurchase.step('Set quantity', async () => {
                await purchaseFlow.setQuantity(enhancedTestData);
            });

            // 5. Select one-time purchase
            await testWithPurchase.step('Select one-time purchase', async () => {
                await purchaseFlow.selectPurchaseType(enhancedTestData, 'oneTime');
            });

            // 6. Store expected values for verification
            await testWithPurchase.step('Store expected values', async () => {
                await purchaseFlow.storeExpectedValues(enhancedTestData);
            });

            // 7. Add to cart and proceed to checkout
            await testWithPurchase.step('Add to cart and proceed to checkout', async () => {
                const cartDetails = await purchaseFlow.addToCartAndProceedToCheckout(enhancedTestData);

                // Verify cart details match expected values
                expect(cartDetails.name).toContain(enhancedTestData.product.name);
                if (enhancedTestData.expectedFlavorName) {
                    expect(cartDetails.variantInfo).toContain(enhancedTestData.expectedFlavorName);
                }
                expect(cartDetails.quantity).toBe(enhancedTestData.expectedQuantity);
                expect(cartDetails.price).toBe(enhancedTestData.expectedPrice);
            });

            // 8. Fill shipping information
            await testWithPurchase.step('Fill shipping information', async () => {
                await purchaseFlow.fillShippingInformation(enhancedTestData);
            });

            // 9. Verify shipping method and cost
            await testWithPurchase.step('Verify shipping method and cost', async () => {
                try {
                    // Check if shipping methods need to be loaded after address is filled
                    const shippingPromptVisible = await page.locator('li:has-text("Enter your shipping address to see shipping options")').isVisible();

                    if (shippingPromptVisible) {
                        console.log('Shipping methods not loaded yet - this is expected in dev environment');

                        // For dev environment, we'll check the order summary instead of shipping method
                        const checkoutDetails = await purchaseFlow.verifyCheckoutPage(enhancedTestData);

                        console.log('Order summary:', checkoutDetails.orderSummary);

                        // Verify order summary contains expected values
                        expect(checkoutDetails.orderSummary.subtotal).toBeTruthy();
                        expect(checkoutDetails.orderSummary.total).toBeTruthy();

                        // Return early with modified shipping details
                        return;
                    }

                    // Standard verification if shipping methods are available
                    const shippingDetails = await purchaseFlow.verifyShippingMethodAndCost(enhancedTestData);

                    // Verify shipping details are present
                    expect(shippingDetails.method).toBeTruthy();
                    expect(shippingDetails.cost).toBeGreaterThan(0);
                } catch (error) {
                    console.error('Error during shipping method verification:', error);
                    // Take a screenshot for debugging
                    await page.screenshot({path: `./screenshots/shipping-method-error-${Date.now()}.png`});

                    // In dev environment, don't fail the test for shipping method issues
                    if (enhancedTestData.environment === 'dev') {
                        console.warn('Continuing test despite shipping method issue in dev environment');
                    } else {
                        throw error;
                    }
                }
            });

            // 10. Verify we're on the checkout page
            await testWithPurchase.step('Verify checkout page', async () => {
                // Use the new verifyCheckoutPage method from purchase fixtures
                const checkoutDetails = await purchaseFlow.verifyCheckoutPage(enhancedTestData);

                // Verify we're on the checkout page
                expect(checkoutDetails.isOnCheckoutPage).toBe(true);

                // Log payment section status (but don't make it a hard requirement)
                console.log(`Payment section visible: ${checkoutDetails.paymentSectionVisible}`);

                // Verify order summary is available
                expect(checkoutDetails.orderSummary.subtotal).toBeTruthy();
                expect(checkoutDetails.orderSummary.total).toBeTruthy();

                // Test passes if we reach the checkout page successfully
                console.log('Dev environment test completed successfully - reached checkout page');
            });

            // No payment or confirmation steps for dev environment
        });
    });
});
