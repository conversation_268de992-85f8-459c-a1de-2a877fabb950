import { test } from 'magnitude-test';

test.group('AEONS Checkout with Different Payment Methods', () => {
    test('Successful one-time purchase with regular card')
        .step('Navigate to product "Ancient Roots Olive Oil"')
            .data({
                productName: 'Ancient Roots Olive Oil',
                quantity: '1', // Changed to string
                purchaseType: 'oneTime'
            })
        .step('Select flavor if applicable and set quantity')
            .data({ 
                // flavor: "Specify if a default or specific flavor is part of the test" 
            })
        .step('Add to cart and proceed to checkout')
        .step('Fill shipping information')
            .data({
                user: 'default', // Corresponds to testDataManager.getUser('default')
                shippingAddressOption: 'same' // Or specify other options
            })
        .step('Verify shipping method and cost')
            .check('Shipping method "tracked_48" is available and selected')
            .check('Order summary subtotal and total are displayed correctly')
        .step('Complete payment with valid Stripe card')
            .secureData({
                paymentMethod: 'stripe_valid' // Corresponds to testDataManager.getPaymentMethod('stripe_valid')
            })
            .check('Order confirmation page is displayed')
        .step('Verify order details on confirmation page')
            .check('Product name "Ancient Roots Olive Oil" is listed in order items')
            // Add checks for quantity, price, shipping, and billing details
        .step('Verify email confirmation is received')
            .data({
                // Potentially Mailtrap API key or specific email subject/content to look for
            })
            .check('Email confirmation for the order is found');

    test('Successful purchase with 3D Secure card')
        .step('Navigate to product "Ancient Roots Olive Oil"')
            .data({
                productName: 'Ancient Roots Olive Oil',
                quantity: '1', // Changed to string
                purchaseType: 'oneTime'
            })
        .step('Select flavor if applicable and set quantity')
        .step('Add to cart and proceed to checkout')
        .step('Fill shipping information')
            .data({
                user: 'default',
                shippingAddressOption: 'same'
            })
        .step('Verify shipping method and cost')
            .check('Shipping method "tracked_48" is available and selected')
        .step('Attempt payment with a 3D Secure card')
            .secureData({
                cardNumber: '****************', // Stripe 3DS test card
                expiry: '12/29',
                cvc: '123'
            })
            .check('3D Secure authentication challenge is presented')
        .step('Complete 3D Secure authentication')
            // Magnitude might need specific instructions here if it cannot auto-handle iframes/redirects for 3DS
            // Or this step could be combined with the previous one if Magnitude handles it.
            .check('Order confirmation page is displayed after 3DS')
        .step('Verify order details on confirmation page after 3DS')
            .check('Product name "Ancient Roots Olive Oil" is listed');

    test('Failed purchase with invalid card')
        .step('Navigate to product "Ancient Roots Olive Oil"')
            .data({
                productName: 'Ancient Roots Olive Oil',
                quantity: '1', // Changed to string
                purchaseType: 'oneTime'
            })
        .step('Select flavor if applicable and set quantity')
        .step('Add to cart and proceed to checkout')
        .step('Fill shipping information')
            .data({
                user: 'default',
                shippingAddressOption: 'same'
            })
        .step('Verify shipping method and cost')
        .step('Attempt payment with an invalid Stripe card')
            .secureData({
                cardNumber: '****************', // Stripe generic decline test card
                expiry: '12/29',
                cvc: '123'
            })
            .check('Payment error message "Your card was declined" is displayed') // Or the specific error message
            .check('User remains on the checkout page')
        .step('Verify no order confirmation is received')
            .check('Order confirmation page is NOT displayed');
}); 