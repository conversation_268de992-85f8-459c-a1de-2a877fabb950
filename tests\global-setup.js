/**
 * @fileoverview Global setup for Playwright tests with BrowserStack integration
 * Initializes the BrowserStack service and sets up test environment before test execution
 */

const fs = require('fs').promises;
const path = require('path');
const { SessionManager } = require('../src/utils/session-manager');
const { BrowserStackService } = require('../src/utils/browserstack/browserstack-service');
const { BrowserStackCleanup } = require('../src/utils/browserstack/browserstack-cleanup');

/**
 * Global setup that runs before all tests
 * @returns {Promise<void>}
 */
async function globalSetup() {
  console.log('🚀 Starting global setup...');

  // Initialize the BrowserStack service
  const browserStackService = BrowserStackService.getInstance();
  console.log(`BrowserStack enabled: ${browserStackService.isBrowserStackEnabled()}`);
  console.log(`Platform: ${browserStackService.platformName}`);

  // Ensure required directories exist
  const testResultsDir = path.join(process.cwd(), 'test-results');
  const screenshotsDir = path.join(testResultsDir, 'screenshots');

  console.log('Creating required directories for test artifacts...');
  try {
    await fs.mkdir(testResultsDir, { recursive: true });
    await fs.mkdir(screenshotsDir, { recursive: true });
    console.log('Directories created successfully');
  } catch (error) {
    console.warn(`Error creating directories: ${error.message}`);
  }

  // Clean up any stale sessions
  console.log('Cleaning up stale sessions...');
  const sessionManager = SessionManager.getInstance();

  // Always clean up BrowserStack sessions, even when using SDK
  if (browserStackService.isBrowserStackEnabled()) {
    try {
      const browserStackCleanup = new BrowserStackCleanup();

      // Get plan information
      const planInfo = await browserStackCleanup.getPlanInfo();
      console.log(`BrowserStack Plan: ${planInfo.automate_plan || 'Unknown'}`);
      console.log(`Parallel Sessions Allowed: ${planInfo.parallel_sessions_max_allowed || 1}`);
      console.log(`Parallel Sessions Running: ${planInfo.parallel_sessions_running || 0}`);

      // Clean up active sessions
      const closedCount = await browserStackCleanup.cleanupAllSessions();
      console.log(`Cleaned up ${closedCount} BrowserStack sessions`);
    } catch (error) {
      console.warn(`Error cleaning up BrowserStack sessions: ${error.message}`);
    }
  }

  // Clean up local sessions
  await sessionManager.cleanupAllSessions();

  // Create a global settings file for the tests to use
  const testSettings = {
    browserStack: {
      enabled: browserStackService.isBrowserStackEnabled(),
      platform: browserStackService.platformName,
      isMobile: browserStackService.isMobile,
      isAndroid: browserStackService.isAndroid,
      isIOS: browserStackService.isIOS
    },
    environment: {
      testEnv: process.env.TEST_ENV || 'test',
      brand: process.env.BRAND || 'aeons',
      testType: process.env.TEST_TYPE || 'regression',
      buildName: process.env.BUILD_NAME || `Test_Run_${new Date().toISOString().replace(/[:.]/g, '_')}`
    },
    timestamp: new Date().toISOString()
  };

  // Write settings to file for tests to access
  try {
    await fs.writeFile(
      path.join(testResultsDir, 'test-settings.json'),
      JSON.stringify(testSettings, null, 2)
    );
    console.log('Test settings saved successfully');
  } catch (error) {
    console.warn(`Error writing test settings: ${error.message}`);
  }

  console.log('✅ Global setup completed successfully');
}

module.exports = globalSetup;