# Example GitHub Actions workflow for Playwright tests
# Save this file as .github/workflows/playwright.yml in your repository

name: Playwright Tests

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to run tests against'
        required: true
        default: 'stage'
        type: choice
        options:
          - dev
          - stage
          - prod
      device_type:
        description: 'Device type to run tests on'
        required: true
        default: 'desktop'
        type: choice
        options:
          - desktop
          - mobile
          - all

jobs:
  test:
    name: 'Tests - ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'stage') || 'dev' }} - ${{ github.event.inputs.device_type || 'desktop' }}'
    runs-on: ubuntu-latest
    
    # Determine environment based on branch or manual input
    env:
      TEST_ENV: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'stage') || 'dev' }}
      DEVICE_TYPE: ${{ github.event.inputs.device_type || 'desktop' }}
      CI: true
      
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install Playwright browsers
        run: npx playwright install --with-deps
        
      - name: Run Playwright tests
        run: |
          # Choose tests based on environment and device type
          if [ "$TEST_ENV" = "dev" ]; then
            if [ "$DEVICE_TYPE" = "mobile" ]; then
              npm run ci:test:s23
            elif [ "$DEVICE_TYPE" = "all" ]; then
              npm run ci:test:all-devices
            else
              npm run ci:test
            fi
          elif [ "$TEST_ENV" = "stage" ]; then
            if [ "$DEVICE_TYPE" = "mobile" ]; then
              npm run ci:test:staging:s23
            elif [ "$DEVICE_TYPE" = "all" ]; then
              npm run ci:test:staging:all-devices
            else
              npm run ci:test:staging
            fi
          elif [ "$TEST_ENV" = "prod" ]; then
            npm run test:smoke:prod
          fi
          
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: playwright-report-${{ env.TEST_ENV }}-${{ env.DEVICE_TYPE }}
          path: |
            playwright-report/
            test-results/
          retention-days: 30

  test-all-browsers:
    name: 'All Browsers - ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'stage') || 'dev' }}'
    runs-on: ubuntu-latest
    # Only run on manual dispatch with "all" device type or tag
    if: (github.event_name == 'workflow_dispatch' && github.event.inputs.device_type == 'all') || startsWith(github.ref, 'refs/tags/')
    
    env:
      TEST_ENV: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'stage') || 'dev' }}
      CI: true
      
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install Playwright browsers
        run: npx playwright install --with-deps
        
      - name: Run all browser tests
        run: |
          if [ "$TEST_ENV" = "dev" ]; then
            npm run ci:test:all-devices
          elif [ "$TEST_ENV" = "stage" ]; then
            npm run ci:test:staging:all-devices
          fi
          
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: playwright-report-all-devices-${{ env.TEST_ENV }}
          path: |
            playwright-report/
            test-results/
          retention-days: 30 