const { test, expect } = require('@playwright/test');
const { AeonsCheckoutPage } = require('../../src/pages/shop/CheckoutPage');
const { AeonsConfirmationPage } = require('../../src/pages/shop/ConfirmationPage');
const { AeonsProductPage } = require('../../src/pages/shop/ProductPage');
const { AeonsCartPage } = require('../../src/pages/shop/CartPage');
const { AeonsHomePage } = require('../../src/pages/general/HomePage');

/**
 * Example test demonstrating order verification on the confirmation page
 * This test shows how to use the verifyOrderDetails method to validate order information
 */
test('Verify order details on confirmation page', async ({ page }) => {
    // Initialize page objects
    const homePage = new AeonsHomePage(page);
    const productPage = new AeonsProductPage(page);
    const cartPage = new AeonsCartPage(page);
    const checkoutPage = new AeonsCheckoutPage(page);
    const confirmationPage = new AeonsConfirmationPage(page);
    
    // Sample product to purchase (update with actual product details)
    const productUrl = 'https://stage.aeonslaboratory.com/collections/supplements/products/total-harmony';
    const productName = 'Total Harmony';
    
    // Sample customer information (update with test data)
    const customerInfo = {
        firstName: 'Test',
        lastName: 'Customer',
        email: '<EMAIL>',
        address: '123 Test St',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        phone: '5551234567',
        country: 'United States',
        // Card info would be needed for actual checkout
    };
    
    // Create expected order details to verify against
    const expectedOrder = {
        items: [
            {
                name: productName,
                quantity: '1',
                // Price will depend on the actual product
            }
        ],
        totals: {
            // Values will depend on the product and shipping
            subtotal: '$95.00',
            shipping: '$0.00',
            // tax and total will vary
        },
        shippingAddress: {
            name: `${customerInfo.firstName} ${customerInfo.lastName}`,
            addressLines: customerInfo.address,
            postalCode: customerInfo.zipCode
        },
        billingAddress: {
            name: `${customerInfo.firstName} ${customerInfo.lastName}`,
            addressLines: customerInfo.address
        },
        paymentMethod: 'Credit Card'
    };
    
    // Start test with navigation to the product
    await test.step('Navigate to product page', async () => {
        await page.goto(productUrl);
        await productPage.waitForPageLoad();
    });
    
    // Add to cart
    await test.step('Add product to cart', async () => {
        await productPage.addToCart();
    });
    
    // Go to checkout
    await test.step('Proceed to checkout', async () => {
        await cartPage.waitForPageLoad();
        await cartPage.proceedToCheckout();
    });
    
    // Note: In a real test, you would complete the checkout process with actual payment
    // For this example, we'll simulate navigation to confirmation page directly
    // with a comment indicating the actual checkout steps
    
    /* 
    // Fill in customer information
    await test.step('Fill in customer information', async () => {
        await checkoutPage.waitForPageLoad();
        await checkoutPage.fillCustomerInformation({
            email: customerInfo.email,
            firstName: customerInfo.firstName,
            lastName: customerInfo.lastName,
            address: customerInfo.address,
            city: customerInfo.city,
            country: customerInfo.country,
            state: customerInfo.state,
            zipCode: customerInfo.zipCode,
            phone: customerInfo.phone
        });
    });
    
    // Select shipping method
    await test.step('Select shipping method', async () => {
        await checkoutPage.selectShippingMethod('Standard Shipping');
    });
    
    // Enter payment information and complete purchase
    await test.step('Complete purchase', async () => {
        await checkoutPage.enterPaymentInformation({
            // Credit card details would go here
            // For test environments, use approved test cards
        });
        await checkoutPage.completePurchase();
    });
    */
    
    // For example purposes, simulate being on the confirmation page
    // In a real test, the above checkout steps would lead to the confirmation page
    await test.step('Verify order details on confirmation page', async () => {
        // Note: In a real test, you wouldn't need this navigation since checkout would lead here
        // This is just for the example
        await page.goto('https://stage.aeonslaboratory.com/pages/order-confirmation?example=true'); 
        
        await confirmationPage.waitForPageLoad();
        
        // Verify the order details against our expectations
        const verificationResults = await confirmationPage.verifyOrderDetails(expectedOrder);
        
        // Assert the verification was successful
        expect(verificationResults.verified, 
            `Order verification failed with discrepancies: ${JSON.stringify(verificationResults.discrepancies)}`
        ).toBeTruthy();
        
        // You can also check specific parts of the verification
        if (!verificationResults.verified) {
            // Log detailed discrepancies for debugging
            console.log('Verification discrepancies:', verificationResults.discrepancies);
            
            // You could assert on individual fields if needed
            // For example:
            // expect(verificationResults.actualOrder.orderNumber).toBe(expectedOrder.orderNumber);
        }
    });
}); 