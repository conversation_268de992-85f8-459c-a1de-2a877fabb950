/**
 * @fileoverview Fixture Factory - Creates appropriate test fixtures based on environment
 */

const base = require('@playwright/test');
const testDataManager = require('../data/test-data-manager');
const { MailtrapHelper } = require('../../src/utils/email/mailtrap-helper');
const { SessionManager } = require('../../src/utils/session-manager');
const path = require('path');

/**
 * Creates appropriate test fixtures based on environment
 */
class FixtureFactory {
    /**
     * Get the appropriate test fixtures based on environment variables
     * @returns {Object} Configured test object with appropriate fixtures
     */
    static getFixtures() {
        // Base test with data manager and Mailtrap helper
        const baseTest = base.test.extend({
            testDataManager: async ({}, use) => {
                const dataSet = process.env.TEST_DATA_SET || 'default';
                const brand = process.env.BRAND || 'aeons';
                const environment = process.env.TEST_ENV || 'stage';
                
                console.log(`Initializing TestDataManager for brand: ${brand}, environment: ${environment}, dataSet: ${dataSet}`);
                testDataManager.initialize(dataSet, brand, environment);
                
                await use(testDataManager);
            },
            
            // Add Mailtrap helper fixture
            mailtrapHelper: async ({}, use) => {
                const mailtrapHelper = new MailtrapHelper();
                await use(mailtrapHelper);
            }
        });
        
        // If BrowserStack is enabled, add BrowserStack fixtures
        if (process.env.BROWSERSTACK_USERNAME) {
            return this.getBrowserStackFixtures(baseTest);
        }
        
        // Return the base test for regular Playwright usage
        return baseTest;
    }
    
    /**
     * Get BrowserStack-specific fixtures
     * @param {Object} baseTest - Base test object with data manager
     * @returns {Object} Enhanced test object with BrowserStack fixtures
     */
    static getBrowserStackFixtures(baseTest) {
        // Create enhanced BrowserStack fixtures
        return baseTest.extend({
            // Add BrowserStack helper
            bsHelper: async ({}, use) => {
                const helper = new EnhancedBrowserStackHelper();
                await use(helper);
            },
            
            // Extend test timeout for Android platforms
            timeout: [process.env.PLATFORM && process.env.PLATFORM.includes('android') ? 
                300000 : 120000, { option: true }],
            
            // Register browser context with SessionManager
            context: async ({ context }, use) => {
                const sessionManager = SessionManager.getInstance();
                const testId = process.env.TEST_ID || `test_${Date.now()}`;
                const isAndroid = process.env.PLATFORM && process.env.PLATFORM.includes('android');
                
                // Disable context tracking for Android to prevent race conditions
                if (!isAndroid) {
                    await sessionManager.registerSession(testId, context, {
                        metadata: {
                            platform: process.env.PLATFORM || 'unknown',
                            browser: process.env.BROWSER || 'unknown'
                        }
                    });
                } else {
                    console.log('Android platform detected, skipping session registration to prevent race conditions');
                }
                
                // Use the context
                await use(context);
            },
            
            // Extend page with screenshot helpers that are aware of Android limitations
            page: async ({ page, bsHelper }, use) => {
                const isAndroidPlatform = process.env.PLATFORM && process.env.PLATFORM.includes('android');
                
                // For Android, use simplified implementation that's less likely to cause issues
                if (isAndroidPlatform) {
                    // For Android, use simplified screenshot method
                    page.fullPageScreenshot = async (name, options = {}) => {
                        try {
                            console.log('Taking Android-friendly screenshot');
                            return await page.screenshot({
                                path: path.join(
                                    process.cwd(), 
                                    'test-results', 
                                    'screenshots',
                                    options.testName || 'unknown',
                                    `${name}-${Date.now()}.png`
                                ),
                                fullPage: false // Full page can be problematic on Android
                            });
                        } catch (error) {
                            console.warn(`Android screenshot failed: ${error.message}`);
                            return null;
                        }
                    };
                    
                    // Add network idle wait method for Android
                    page.waitForNetworkIdle = async (options = {}) => {
                        const timeout = options.timeout || 30000;
                        console.log(`Waiting for network idle (simplified Android version, timeout: ${timeout}ms)`);
                        
                        try {
                            // Use simpler approach for Android
                            await page.waitForLoadState('networkidle', { 
                                timeout: timeout 
                            });
                        } catch (error) {
                            console.warn(`Network idle wait failed: ${error.message}`);
                        }
                    };
                } else {
                    // Use standard implementation for non-Android platforms
                    // Add full-page screenshot capability
                    page.fullPageScreenshot = async (name, options = {}) => {
                        // Use enhanced screenshot method that tracks URLs
                        return await bsHelper.takeScreenshot(page, name, {
                            fullPage: true,
                            ...options
                        });
                    };
                    
                    // Add network idle wait method
                    page.waitForNetworkIdle = async (options = {}) => {
                        try {
                            await page.waitForLoadState('networkidle', { 
                                timeout: options.timeout || 30000 
                            });
                        } catch (error) {
                            console.warn(`Network idle wait failed: ${error.message}`);
                        }
                    };
                }
                
                // Add test status reporting method
                page.reportTestStatus = async (status) => {
                    const sessionId = process.env.BROWSERSTACK_SESSION_ID;
                    if (sessionId && bsHelper) {
                        return await bsHelper.updateTestStatus(sessionId, status);
                    }
                };
                
                await use(page);
            }
        });
    }
}

// Export a simplified interface for tests
module.exports = {
    // Main export - gets the right fixtures based on environment
    test: FixtureFactory.getFixtures(),
    expect: base.expect
};