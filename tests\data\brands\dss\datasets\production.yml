# Production dataset overrides for DSS brand
products:
  # Override specific product data for production environment
  # Add product overrides as needed

test_data:
  # Override specific test data for production environment
  test_users:
    default:
      firstName: "DSS"
      lastName: "Production"
      email: "<EMAIL>"
      phone: "0778899001"
      address: "101 DSS Production Ave"
      city: "Birmingham"
      postcode: "B1 1AA"
      country: "GB"
  
  # Add any other test data overrides as needed
  payment_methods:
    # Payment method overrides for production if needed
  
  shipping_methods:
    # Shipping method overrides for production if needed
  
  funnel_configs:
    # Funnel configuration overrides for production if needed 