Feature: Mixed Cart Purchase

  Background:
    Given I load brand configuration
    And I load product data
    And I am on the product page

  @mixed_cart @subscription
  Scenario: Purchase with multiple subscriptions and one-time items
    # First subscription item
    When I select "Subscribe & Save"
    And I select flavor "classic"
    And I set the quantity to "medium"
    And I set the supply to "2 Months"
    And I add the product to the cart
    
    # Return to product page for second item
    And I am on the product page
    And I select "Subscribe & Save"
    And I select flavor "lemon"
    And I set the quantity to "minimum"
    And I set the supply to "1 Month"
    And I add the product to the cart
    
    # Return to product page for one-time purchase
    And I am on the product page
    When I select "One-Time Purchase"
    And I select flavor "truffle"
    And I set the quantity to "maximum"
    And I add the product to the cart

    Then the cart should contain "3" items
    And the cart should contain "2" subscription items
    And the cart should contain "1" one-time purchase items
    And the subscription items should have correct frequencies

    When I proceed to checkout
    Then I verify Google Tag Manager is present
    And I fill in the shipping information with "default" user data
    And I use the same address for billing
    Then The shipping method "Domestic tracked" should be selected
    And I verify the shipping cost

    When I enter "stripe_valid" payment details
    And I complete the purchase
    And I wait for the order confirmation page to load
    Then I verify Google Tag Manager is present
    And I verify the mixed cart order details are correct 