# Windows PowerShell script for triggering GitLab CI/CD pipelines and retrieving results
# This is the PowerShell equivalent of trigger-tests.sh

# Get current values
$TIMESTAMP = Get-Date -Format "yyyyMMddTHHmmss"
$COMMIT_SHA = try {
    git rev-parse HEAD
} catch {
    "manual-test-$(Get-Date -UFormat %s)"
}

# Set default values
$ENV = if ($args[0]) { $args[0] } else { "stage" }
$BROWSER = if ($args[1]) { $args[1] } else { "chromium" }
$DEVICE = if ($args[2]) { $args[2] } else { "desktop" }
$MAX_WAIT_TIME = if ($args[3]) { [int]$args[3] } else { 1800 } # Default 30 minutes

# GitLab variables
$PROJECT_ID = 66834057
$TOKEN = "glptt-f732f3c8ad6b9357e8b07770215006239ac29e5f"
$BRANCH = "cleanup"
$API_URL = "https://gitlab.com/api/v4/projects/$PROJECT_ID"

# Create results directory
$RESULTS_DIR = "pipeline-results\$TIMESTAMP"
New-Item -Path $RESULTS_DIR -ItemType Directory -Force | Out-Null

# Function to check pipeline status
function Check-PipelineStatus {
    param($pipelineId)
    
    $response = Invoke-RestMethod -Uri "$API_URL/pipelines/$pipelineId" -Headers @{
        "PRIVATE-TOKEN" = $TOKEN
    } -Method Get
    
    return $response.status
}

# Function to wait for pipeline completion
function Wait-ForPipeline {
    param($pipelineId)
    
    $wait_time = 0
    $interval = 30 # Check every 30 seconds
    
    Write-Host "Waiting for pipeline #$pipelineId to complete..."
    
    while ($wait_time -lt $MAX_WAIT_TIME) {
        $status = Check-PipelineStatus -pipelineId $pipelineId
        
        $currentTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        Write-Host "$currentTime - Pipeline status: $status"
        
        if ($status -eq "success" -or $status -eq "failed" -or $status -eq "canceled") {
            Write-Host "Pipeline #$pipelineId finished with status: $status"
            return $true
        } elseif ($status -eq "running" -or $status -eq "pending" -or $status -eq "created") {
            # Still in progress, continue waiting
            Start-Sleep -Seconds $interval
            $wait_time += $interval
        } else {
            Write-Host "Pipeline has unexpected status: $status"
            return $false
        }
    }
    
    Write-Host "Timeout waiting for pipeline #$pipelineId to complete. Last status: $status"
    return $false
}

# Function to download job artifacts
function Download-JobArtifacts {
    param($pipelineId)
    
    # Get all jobs for the pipeline
    Write-Host "Retrieving jobs for pipeline #$pipelineId..."
    $jobs = Invoke-RestMethod -Uri "$API_URL/pipelines/$pipelineId/jobs" -Headers @{
        "PRIVATE-TOKEN" = $TOKEN
    } -Method Get
    
    # Filter jobs with artifacts
    $jobsWithArtifacts = $jobs | Where-Object { $_.artifacts_file -ne $null }
    
    if ($jobsWithArtifacts.Count -eq 0) {
        Write-Host "No artifacts found for pipeline #$pipelineId"
        return $false
    }
    
    Write-Host "Found $($jobsWithArtifacts.Count) jobs with artifacts."
    
    # Download artifacts for each job
    foreach ($job in $jobsWithArtifacts) {
        $jobId = $job.id
        $jobName = $job.name
        $artifactFile = "$RESULTS_DIR\$jobName-artifacts.zip"
        
        Write-Host "Downloading artifacts for job '$jobName' (#$jobId)..."
        Invoke-WebRequest -Uri "$API_URL/jobs/$jobId/artifacts" -Headers @{
            "PRIVATE-TOKEN" = $TOKEN
        } -OutFile $artifactFile
        
        if (Test-Path $artifactFile) {
            Write-Host "Artifacts saved to $artifactFile"
            
            # Extract artifacts
            $extractDir = "$RESULTS_DIR\$jobName"
            New-Item -Path $extractDir -ItemType Directory -Force | Out-Null
            
            # Use Expand-Archive for extraction
            try {
                Expand-Archive -Path $artifactFile -DestinationPath $extractDir -Force
                Write-Host "Extracted artifacts to $extractDir"
            } catch {
                Write-Host "Failed to extract artifacts: $_"
            }
        } else {
            Write-Host "Failed to download artifacts for job #$jobId"
        }
    }
    
    return $true
}

# Function to get pipeline URL
function Get-PipelineUrl {
    param($pipelineId)
    return "https://gitlab.com/projects/$PROJECT_ID/pipelines/$pipelineId"
}

# Main execution
Write-Host "Triggering pipeline for $ENV environment with $BROWSER on $DEVICE"
Write-Host "Results will be saved to: $RESULTS_DIR"

# Trigger the pipeline using curl with proper multipart form data
try {
    # Use curl to trigger the pipeline since it handles form data better
    $curlCommand = "curl.exe -s -X POST " + 
                  "-F token=$TOKEN " +
                  "-F ref=$BRANCH " +
                  "-F ""variables[TEST_ENV]=$ENV"" " +
                  "-F ""variables[BROWSER]=$BROWSER"" " +
                  "-F ""variables[DEVICE]=$DEVICE"" " +
                  "-F ""variables[COMMIT_SHA]=$COMMIT_SHA"" " +
                  "-F ""variables[TIMESTAMP]=$TIMESTAMP"" " +
                  """$API_URL/trigger/pipeline"""
    
    Write-Host "Executing: $curlCommand"
    $responseJson = Invoke-Expression $curlCommand
    
    # Convert response to PowerShell object
    $response = $responseJson | ConvertFrom-Json
    
    # Extract pipeline ID
    $pipelineId = $response.id
    
    if (-not $pipelineId) {
        Write-Host "Failed to extract pipeline ID from response:"
        Write-Host $responseJson
        exit 1
    }
    
    Write-Host "Pipeline triggered successfully with ID: $pipelineId"
    Write-Host "Pipeline URL: $(Get-PipelineUrl -pipelineId $pipelineId)"
    
    # Wait for pipeline to complete
    if (Wait-ForPipeline -pipelineId $pipelineId) {
        # Download artifacts
        Download-JobArtifacts -pipelineId $pipelineId
        
        # Get final status
        $finalStatus = Check-PipelineStatus -pipelineId $pipelineId
        
        Write-Host "Test results saved to: $RESULTS_DIR"
        Write-Host "===== Pipeline Summary ====="
        Write-Host "Pipeline ID: $pipelineId"
        Write-Host "URL: $(Get-PipelineUrl -pipelineId $pipelineId)"
        Write-Host "Environment: $ENV"
        Write-Host "Browser/Device: $BROWSER / $DEVICE"
        Write-Host "Status: $finalStatus"
        Write-Host "Results location: $RESULTS_DIR"
        
        if ($finalStatus -eq "success") {
            exit 0
        } else {
            exit 1
        }
    } else {
        Write-Host "Failed to monitor pipeline. Check pipeline status manually at:"
        Write-Host "$(Get-PipelineUrl -pipelineId $pipelineId)"
        exit 1
    }
} catch {
    Write-Host "Failed to trigger pipeline. Error: $_"
    exit 1
} 