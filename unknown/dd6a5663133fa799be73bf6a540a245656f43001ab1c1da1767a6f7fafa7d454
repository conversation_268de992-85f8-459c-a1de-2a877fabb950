const { test, expect } = require('@playwright/test');
const { ProductPage } = require('../../src/pages/shop/ProductPage');
const { CartPage } = require('../../src/pages/shop/CartPage');
const { CheckoutPage } = require('../../src/pages/shop/CheckoutPage');
const { ConfirmationPage } = require('../../src/pages/shop/ConfirmationPage');
const { PayPalPage } = require('../../src/pages/shop/PayPalPage');
const { LoginPage } = require('../../src/pages/account/LoginPage');
const { DashboardPage } = require('../../src/pages/account/DashboardPage');
const { HomePage } = require('../../src/pages/general/HomePage');

test('Verify page object imports', async ({ page }) => {
  // Create instances of all page objects
  const productPage = new ProductPage(page);
  const cartPage = new CartPage(page);
  const checkoutPage = new CheckoutPage(page);
  const confirmationPage = new ConfirmationPage(page);
  const paypalPage = new PayPalPage(page);
  const loginPage = new LoginPage(page);
  const dashboardPage = new DashboardPage(page);
  const homePage = new HomePage(page);
  
  // Verify that all page objects have been instantiated correctly
  expect(productPage).toBeDefined();
  expect(cartPage).toBeDefined();
  expect(checkoutPage).toBeDefined();
  expect(confirmationPage).toBeDefined();
  expect(paypalPage).toBeDefined();
  expect(loginPage).toBeDefined();
  expect(dashboardPage).toBeDefined();
  expect(homePage).toBeDefined();
  
  // Verify that all page objects have the expected methods
  expect(typeof productPage.navigateToProduct).toBe('function');
  expect(typeof cartPage.waitForCartDrawer).toBe('function');
  expect(typeof checkoutPage.fillShippingInformation).toBe('function');
  expect(typeof confirmationPage.waitForOrderConfirmation).toBe('function');
  expect(typeof paypalPage.completePayment).toBe('function');
  expect(typeof loginPage.login).toBe('function');
  expect(typeof dashboardPage.isLoggedIn).toBe('function');
  expect(typeof homePage.goto).toBe('function');
  
  console.log('All page objects verified successfully');
});
