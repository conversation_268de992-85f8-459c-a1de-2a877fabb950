/**
 * @fileoverview Enhanced Fixture Validation Tests
 * 
 * Validation tests to ensure the enhanced fixture system works correctly
 * and maintains backward compatibility with existing patterns.
 * 
 * @tags @validation @enhanced_fixture @foundation
 */

const { test, expect } = require('../fixtures/enhanced-unified-fixture');

test.describe('Enhanced Fixture Validation', () => {
    test.describe('Foundation Layer Validation', () => {
        test('Enhanced TestDataManager should initialize correctly', async ({ testDataManager }) => {
            // Verify TestDataManager is properly initialized
            expect(testDataManager).toBeTruthy();
            expect(testDataManager.brand).toBeTruthy();
            expect(testDataManager.environment).toBeTruthy();
            
            // Verify enhanced debug capabilities
            expect(testDataManager.debug).toBeTruthy();
            expect(typeof testDataManager.debug.getCurrentBrand).toBe('function');
            expect(typeof testDataManager.debug.getCurrentEnvironment).toBe('function');
            expect(typeof testDataManager.debug.dumpCurrentConfig).toBe('function');
            
            // Test debug methods
            const config = testDataManager.debug.dumpCurrentConfig();
            expect(config.brand).toBeTruthy();
            expect(config.environment).toBeTruthy();
            expect(config.baseUrl).toBeTruthy();
            
            console.log('TestDataManager validation passed:', config);
        });

        test('Page Object Factory should create page objects correctly', async ({ pageObjectFactory }) => {
            // Verify factory is available
            expect(pageObjectFactory).toBeTruthy();
            
            // Test organized structure
            expect(pageObjectFactory.shop).toBeTruthy();
            expect(pageObjectFactory.account).toBeTruthy();
            expect(pageObjectFactory.admin).toBeTruthy();
            expect(pageObjectFactory.general).toBeTruthy();
            expect(pageObjectFactory.salesFunnel).toBeTruthy();
            
            // Test specific page objects
            expect(pageObjectFactory.shop.product).toBeTruthy();
            expect(pageObjectFactory.shop.cart).toBeTruthy();
            expect(pageObjectFactory.shop.checkout).toBeTruthy();
            
            // Test backward compatibility
            const allPageObjects = pageObjectFactory.getAll();
            expect(allPageObjects.productPage).toBeTruthy();
            expect(allPageObjects.cartPage).toBeTruthy();
            expect(allPageObjects.checkoutPage).toBeTruthy();
            expect(allPageObjects.loginPage).toBeTruthy();
            
            // Test caching
            const shop1 = pageObjectFactory.shop;
            const shop2 = pageObjectFactory.shop;
            expect(shop1).toBe(shop2); // Should be the same cached object
            
            console.log('Page Object Factory validation passed');
        });

        test('Test Data Helper should provide standardized patterns', async ({ testDataHelper, testDataManager }) => {
            // Verify helper is available
            expect(testDataHelper).toBeTruthy();
            
            // Test standard purchase data pattern
            const purchaseData = testDataHelper.getPurchaseTestData();
            expect(purchaseData.product).toBeTruthy();
            expect(purchaseData.user).toBeTruthy();
            expect(purchaseData.paymentMethod).toBeTruthy();
            expect(purchaseData.baseUrl).toBeTruthy();
            expect(purchaseData.brand).toBe(testDataManager.brand);
            
            // Test subscription data pattern
            const subscriptionData = testDataHelper.getSubscriptionTestData();
            expect(subscriptionData.subscriptionFrequency).toBe('monthly');
            expect(subscriptionData.purchaseType).toBe('subscription');
            
            // Test PayPal data pattern
            const paypalData = testDataHelper.getPayPalTestData();
            expect(paypalData.paymentFlow).toBe('paypal');
            
            // Test brand-specific data
            const brandData = testDataHelper.getBrandSpecificData();
            expect(brandData.brand).toBeTruthy();
            expect(brandData.baseUrl).toBeTruthy();
            expect(brandData.emailDomain).toBeTruthy();
            
            console.log('Test Data Helper validation passed');
        });

        test('Device Helper should handle platform detection correctly', async ({ deviceHelper }) => {
            // Verify helper is available
            expect(deviceHelper).toBeTruthy();
            
            // Test device type detection
            const deviceType = deviceHelper.getDeviceType();
            expect(['desktop', 'mobile', 'tablet']).toContain(deviceType);
            
            // Test viewport configuration
            const viewport = deviceHelper.getViewportConfig();
            expect(viewport.width).toBeGreaterThan(0);
            expect(viewport.height).toBeGreaterThan(0);
            
            // Test timeout calculations
            const timeoutMultiplier = deviceHelper.getTimeoutMultiplier();
            expect(timeoutMultiplier).toBeGreaterThan(0);
            
            const recommendedTimeout = deviceHelper.getRecommendedTimeout(30000);
            expect(recommendedTimeout).toBeGreaterThanOrEqual(30000);
            
            // Test platform info
            const platformInfo = deviceHelper.getPlatformInfo();
            expect(platformInfo.platform).toBeDefined();
            expect(platformInfo.deviceType).toBeDefined();
            expect(typeof platformInfo.isMobile).toBe('boolean');
            
            console.log('Device Helper validation passed:', platformInfo);
        });

        test('BrowserStack Enhanced Helper should initialize correctly', async ({ browserStackHelper }) => {
            // Verify helper is available
            expect(browserStackHelper).toBeTruthy();
            
            // Test session info
            const sessionInfo = browserStackHelper.getSessionInfo();
            expect(sessionInfo).toBeTruthy();
            expect(typeof sessionInfo.isEnabled).toBe('boolean');
            expect(typeof sessionInfo.isSdkMode).toBe('boolean');
            
            // Test configuration status
            const configStatus = browserStackHelper.getConfigurationStatus();
            expect(configStatus).toBeTruthy();
            expect(typeof configStatus.hasUsername).toBe('boolean');
            expect(typeof configStatus.hasAccessKey).toBe('boolean');
            
            console.log('BrowserStack Enhanced Helper validation passed:', configStatus);
        });

        test('Email Helper should initialize with brand context', async ({ emailHelper, testDataManager }) => {
            // Verify helper is available
            expect(emailHelper).toBeTruthy();
            
            // Test brand-specific email subjects
            const orderSubject = emailHelper.getBrandOrderConfirmationSubject();
            expect(orderSubject).toBeTruthy();
            expect(orderSubject).toContain(testDataManager.brand.toUpperCase());
            
            const abandonedSubject = emailHelper.getBrandAbandonedCartSubject();
            expect(abandonedSubject).toBeTruthy();
            
            const welcomeSubject = emailHelper.getBrandWelcomeSubject();
            expect(welcomeSubject).toBeTruthy();
            
            // Test email generation
            const testEmail = emailHelper.generateTestEmail('validation');
            expect(testEmail).toContain('validation');
            expect(testEmail).toContain(testDataManager.brand);
            expect(testEmail).toContain('@malaberg.com');
            
            console.log('Email Helper validation passed');
        });
    });

    test.describe('Backward Compatibility Validation', () => {
        test('Legacy pageObjects fixture should work', async ({ pageObjects }) => {
            // Test that legacy pageObjects structure is available
            expect(pageObjects).toBeTruthy();
            expect(pageObjects.productPage).toBeTruthy();
            expect(pageObjects.cartPage).toBeTruthy();
            expect(pageObjects.checkoutPage).toBeTruthy();
            expect(pageObjects.confirmationPage).toBeTruthy();
            expect(pageObjects.loginPage).toBeTruthy();
            expect(pageObjects.dashboardPage).toBeTruthy();
            expect(pageObjects.homePage).toBeTruthy();
            
            console.log('Legacy pageObjects compatibility validated');
        });

        test('Base URL fixture should work', async ({ baseUrl, testDataManager }) => {
            // Test that baseUrl fixture provides the same value as testDataManager
            expect(baseUrl).toBeTruthy();
            expect(baseUrl).toBe(testDataManager.getBaseUrl());
            
            console.log('Base URL fixture compatibility validated:', baseUrl);
        });

        test('Device info fixture should provide comprehensive information', async ({ deviceInfo }) => {
            // Test enhanced device info
            expect(deviceInfo).toBeTruthy();
            expect(deviceInfo.platform).toBeDefined();
            expect(typeof deviceInfo.isMobile).toBe('boolean');
            expect(deviceInfo.deviceName).toBeTruthy();
            expect(typeof deviceInfo.isRealDevice).toBe('boolean');
            expect(typeof deviceInfo.isBrowserStackSdk).toBe('boolean');
            
            console.log('Device info fixture validated:', deviceInfo);
        });
    });

    test.describe('Integration Validation', () => {
        test('Enhanced page methods should be available', async ({ page }) => {
            // Test enhanced page methods
            expect(typeof page.takeScreenshotWithContext).toBe('function');
            expect(typeof page.takeScreenshotForBrowserStack).toBe('function');
            expect(typeof page.waitForVisualStability).toBe('function');
            
            console.log('Enhanced page methods validated');
        });

        test('TestDataManager integration should work with all helpers', async ({ 
            testDataManager, 
            testDataHelper, 
            emailHelper 
        }) => {
            // Test that all helpers use the same TestDataManager instance
            expect(testDataHelper.testDataManager).toBe(testDataManager);
            expect(emailHelper.testDataManager).toBe(testDataManager);
            
            // Test that brand consistency is maintained
            expect(testDataHelper.testDataManager.brand).toBe(testDataManager.brand);
            expect(emailHelper.brand).toBe(testDataManager.brand);
            
            console.log('TestDataManager integration validated');
        });
    });

    test.describe('Error Handling Validation', () => {
        test('Helpers should handle missing data gracefully', async ({ testDataHelper }) => {
            // Test graceful handling of missing product
            try {
                const testData = testDataHelper.getPurchaseTestData('nonexistent_product');
                // If it doesn't throw, that's fine - it might use defaults
                console.log('Graceful handling test passed');
            } catch (error) {
                // If it throws, the error should be informative
                expect(error.message).toBeTruthy();
                console.log('Error handling test passed:', error.message);
            }
        });

        test('BrowserStack helper should handle missing configuration gracefully', async ({ browserStackHelper }) => {
            // Test that helper doesn't crash with missing configuration
            const configStatus = browserStackHelper.getConfigurationStatus();
            expect(configStatus).toBeTruthy();
            
            // Test that methods don't throw even if not properly configured
            try {
                await browserStackHelper.setTestStatus('passed', 'test');
                console.log('BrowserStack graceful handling test passed');
            } catch (error) {
                // Should not throw, but if it does, error should be handled gracefully
                console.log('BrowserStack error handled gracefully:', error.message);
            }
        });
    });
});

test.describe('Performance Validation', () => {
    test('Page object caching should improve performance', async ({ pageObjectFactory }) => {
        const startTime = Date.now();
        
        // First access - should create objects
        const shop1 = pageObjectFactory.shop;
        const firstAccessTime = Date.now() - startTime;
        
        const cacheStartTime = Date.now();
        
        // Second access - should use cache
        const shop2 = pageObjectFactory.shop;
        const secondAccessTime = Date.now() - cacheStartTime;
        
        // Verify caching works
        expect(shop1).toBe(shop2);
        
        // Second access should be faster (though this might be minimal)
        console.log(`First access: ${firstAccessTime}ms, Second access: ${secondAccessTime}ms`);
        
        // Test cache statistics
        const stats = pageObjectFactory.getCacheStats();
        expect(stats.size).toBeGreaterThan(0);
        expect(stats.groups).toContain('shop');
        
        console.log('Page object caching performance validated:', stats);
    });
});
