"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.platformConfigs = void 0;
var dotenv = require("dotenv");
// Load environment variables
dotenv.config();
// Platform configurations
exports.platformConfigs = {
    'chrome-windows': {
        name: 'chrome-windows',
        browserName: 'chromium',
        os: 'Windows',
        osVersion: '11',
        viewport: { width: 1920, height: 1080 }
    },
    'android-chrome': {
        name: 'android-chrome',
        browserName: 'chromium',
        os: 'android',
        osVersion: '13.0',
        deviceName: 'Samsung Galaxy S23',
        realMobile: true,
        viewport: { width: 393, height: 851 }
    },
    'ios-safari': {
        name: 'ios-safari',
        browserName: 'webkit',
        os: 'ios',
        osVersion: '16',
        deviceName: 'iPhone 14',
        realMobile: true,
        viewport: { width: 390, height: 844 }
    }
};
// Environment-specific configurations
var environments = {
    dev: {
        baseURL: 'https://aeons-dev.info'
    },
    stage: {
        baseURL: 'https://aeonstest.info'
    },
    prod: {
        baseURL: 'https://aeons.co.uk'
    }
};
// Brand-specific environment URLs
var brandEnvironmentURLs = {
    aeons: {
        dev: 'https://aeons-dev.info',
        stage: 'https://aeonstest.info',
        prod: 'https://aeons.co.uk'
    },
    dss: {
        dev: 'https://dss-dev.info',
        stage: 'https://dss.crm-test.info',
        prod: 'https://drsisterskincare.com'
    },
    ypn: {
        dev: 'https://ypn-dev.info',
        stage: 'https://ypntest.info',
        prod: 'https://yourpetnutrition.com'
    }
};
// Brand-specific configurations
var brandConfigs = {
    aeons: {
        testDir: './tests/regression/aeons'
    },
    dss: {
        testDir: './tests/regression/dss'
    },
    ypn: {
        testDir: './tests/shopify/ypn'
    }
};
// Create configuration
var ConfigurationManager = /** @class */ (function () {
    function ConfigurationManager() {
        var _a;
        var env = (process.env.TEST_ENV || 'stage');
        var brand = (process.env.BRAND || 'aeons');
        var testType = (process.env.TEST_TYPE || 'smoke');
        var dataSet = (process.env.TEST_DATA_SET || 'default');
        // Get brand-specific URL for the current environment if available
        var brandSpecificURL = (_a = brandEnvironmentURLs[brand]) === null || _a === void 0 ? void 0 : _a[env];
        // Merge configurations
        var baseConfig = __assign(__assign(__assign({}, environments[env]), brandConfigs[brand]), { brand: brand, environment: env, testType: testType, dataSet: dataSet, testDir: brandConfigs[brand].testDir || './tests', 
            // Use brand-specific URL if available, otherwise fall back to environment default
            baseURL: brandSpecificURL || environments[env].baseURL || 'https://aeonstest.info', testDataConfig: {
                dataSet: dataSet,
                brand: brand
            } });
        // BrowserStack configuration
        var browserstackConfig = {
            username: process.env.BROWSERSTACK_USERNAME || '',
            accessKey: process.env.BROWSERSTACK_ACCESS_KEY || '',
            debug: true,
            networkLogs: true,
            consoleLogs: 'verbose',
            buildName: process.env.BUILD_NAME || "".concat(brand, "_").concat(testType, "_").concat(env),
            projectName: brand.toUpperCase()
        };
        this.config = {
            testDir: baseConfig.testDir,
            timeout: 60000,
            workers: 1,
            use: {
                baseURL: baseConfig.baseURL,
                screenshot: 'only-on-failure',
                trace: 'on-first-retry',
                video: 'on-first-retry',
                testDataManager: {
                    dataSet: baseConfig.dataSet,
                    brand: baseConfig.brand
                }
            },
            projects: this.generateProjects(baseConfig, browserstackConfig)
        };
    }
    ConfigurationManager.prototype.generateProjects = function (baseConfig, bsConfig) {
        var _this = this;
        return Object.values(exports.platformConfigs).map(function (platform) { return ({
            name: platform.name,
            testMatch: _this.getTestPattern(baseConfig.testType),
            use: __assign(__assign({}, platform), { 'browserstack.username': bsConfig.username, 'browserstack.accessKey': bsConfig.accessKey, 'browserstack.debug': bsConfig.debug, 'browserstack.networkLogs': bsConfig.networkLogs, 'browserstack.consoleLogs': bsConfig.consoleLogs, 'browserstack.buildName': bsConfig.buildName, 'browserstack.projectName': bsConfig.projectName })
        }); });
    };
    ConfigurationManager.prototype.getTestPattern = function (testType) {
        var patterns = {
            smoke: '.*smoke.*\\.spec\\.js',
            visual: '.*visual.*\\.spec\\.js',
            regression: '.*regression.*\\.spec\\.js'
        };
        // Get environment-specific test pattern
        var env = process.env.TEST_ENV;
        if (testType === 'smoke' && env) {
            console.log("Configuring test pattern for ".concat(env, " environment"));
            // Return the base pattern but we'll use the grep in the command line
            // to further filter to the specific environment test tag
        }
        return new RegExp(patterns[testType]);
    };
    ConfigurationManager.getInstance = function () {
        if (!ConfigurationManager.instance) {
            ConfigurationManager.instance = new ConfigurationManager();
        }
        return ConfigurationManager.instance;
    };
    ConfigurationManager.prototype.getConfig = function () {
        return this.config;
    };
    return ConfigurationManager;
}());
exports.default = ConfigurationManager.getInstance().getConfig();
