/**
 * Mixed Card Tests Runner
 * 
 * This script provides an easy way to run the mixed card payment tests
 * with various configurations and platforms.
 * 
 * Usage:
 *   node run-mixed-card-tests.js [platform] [card-type] [--browserstack]
 * 
 * Examples:
 *   node run-mixed-card-tests.js                           # Run all tests locally on Chrome
 *   node run-mixed-card-tests.js windows-chrome regular    # Run regular card test on Chrome
 *   node run-mixed-card-tests.js samsung-galaxy-s23 3ds --browserstack  # Run 3DS test on mobile via BrowserStack
 */

const { execSync } = require('child_process');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);
const platform = args[0] || 'windows-chrome'; // Default to Chrome on Windows
const cardType = args[1] || 'all'; // Default to all card types
const useBrowserStack = args.includes('--browserstack');
const env = 'stage'; // We always use staging for payment tests

// Card type to tag mapping
const cardTags = {
    'regular': '@stage_regular_card',
    '3ds': '@stage_3ds_card',
    'invalid': '@stage_invalid_card',
    'all': '' // No tag filter means run all tests
};

// Check if the card type is valid
if (!Object.keys(cardTags).includes(cardType)) {
    console.error(`Invalid card type: ${cardType}`);
    console.log('Available card types: regular, 3ds, invalid, all');
    process.exit(1);
}

// Define the test file path
const testPath = 'tests/regression/aeons/smoke/mixed-card.spec.js';

// Build the command
let command;
if (useBrowserStack) {
    // Use run-browserstack-test.js for BrowserStack
    command = `node run-browserstack-test.js ${testPath} --platform=${platform}`;
    
    // Add tag filter if a specific card type is requested
    if (cardType !== 'all') {
        command += ` --tags="${cardTags[cardType]}"`;
    }
} else {
    // Use run-test.js for local testing
    command = `node run-test.js ${testPath} --platform=${platform} --env=${env}`;
    
    // Add tag filter if a specific card type is requested
    if (cardType !== 'all') {
        command += ` --tags="${cardTags[cardType]}"`;
    }
}

// Log execution details
console.log('==================================================');
console.log('Mixed Card Tests Runner');
console.log('==================================================');
console.log(`Test path:     ${testPath}`);
console.log(`Platform:      ${platform}`);
console.log(`Card type:     ${cardType}`);
console.log(`Tag filter:    ${cardTags[cardType] || 'none (running all tests)'}`);
console.log(`Environment:   ${env}`);
console.log(`Using:         ${useBrowserStack ? 'BrowserStack' : 'Local Playwright'}`);
console.log('--------------------------------------------------');
console.log(`Command: ${command}`);
console.log('==================================================');

// Run the command
try {
    execSync(command, { stdio: 'inherit' });
} catch (error) {
    console.error(`\nTest run failed with exit code: ${error.status}`);
    process.exit(error.status);
} 