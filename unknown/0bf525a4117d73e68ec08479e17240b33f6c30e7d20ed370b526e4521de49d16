// Script to check Mailtrap environment variables
require('dotenv').config();

console.log('============================================');
console.log('Checking Mailtrap environment variables:');
console.log('============================================');

const vars = [
  'MAILTRAP_API_TOKEN',
  'MAILTRAP_INBOX_ID',
  'MAILTRAP_ACCOUNT_ID'
];

vars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    // Mask the token for security - only show first 4 and last 4 characters
    const maskedValue = varName.includes('TOKEN') || varName.includes('KEY') 
      ? `${value.substring(0, 4)}...${value.substring(value.length - 4)}` 
      : value;
    console.log(`✅ ${varName}: DEFINED (${maskedValue})`);
  } else {
    console.log(`❌ ${varName}: NOT DEFINED`);
  }
});

// Check .env file
const fs = require('fs');
try {
  if (fs.existsSync('.env')) {
    console.log('\n✅ .env file exists');
    const envContent = fs.readFileSync('.env', 'utf8');
    const envLines = envContent.split('\n').filter(line => 
      line.trim() && !line.trim().startsWith('#') && line.includes('=')
    );
    console.log(`   File contains ${envLines.length} environment variables`);
    
    // Count MAILTRAP-related variables
    const mailtrapLines = envLines.filter(line => line.includes('MAILTRAP'));
    console.log(`   File contains ${mailtrapLines.length} MAILTRAP-related variables`);
    
    // Print the actual MAILTRAP lines (with masked values)
    mailtrapLines.forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        const maskedValue = key.includes('TOKEN') || key.includes('KEY')
          ? `${value.substring(0, 4)}...${value.substring(value.length - 4)}`
          : value;
        console.log(`   ${key}=${maskedValue}`);
      }
    });
  } else {
    console.log('\n❌ .env file does NOT exist');
  }
} catch (error) {
  console.log('\n❌ Error checking .env file:', error.message);
}

console.log('\nDone checking environment configuration.');
console.log('============================================');
