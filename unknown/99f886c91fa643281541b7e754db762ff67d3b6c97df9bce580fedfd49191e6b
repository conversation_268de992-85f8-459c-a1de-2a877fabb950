import { PlaywrightTestConfig, Project } from '@playwright/test';
import * as dotenv from 'dotenv';
import * as path from 'path';
import { TestDataManagerOptions } from './types/test-data';

// Load environment variables
dotenv.config();

// Environment type definition
type Environment = 'dev' | 'stage' | 'prod';
type DataSet = 'default' | 'staging' | 'production';
type Brand = 'aeons' | 'dss' | 'ypn';
type TestType = 'smoke' | 'visual' | 'regression';
type BrowserName = 'chromium' | 'firefox' | 'webkit';

// Platform configuration types
interface PlatformConfig {
  name: string;
  browserName: BrowserName;
  os?: string;
  osVersion?: string;
  deviceName?: string;
  realMobile?: boolean;
  viewport?: { width: number; height: number };
}

// Base configuration interface
interface BaseConfig {
  brand: Brand;
  environment: Environment;
  testType: TestType;
  baseURL: string;
  testDir: string;
  dataSet: DataSet;
  testDataConfig: TestDataManagerOptions;
}

// BrowserStack specific configuration
interface BrowserStackConfig {
  username: string;
  accessKey: string;
  debug: boolean;
  networkLogs: boolean;
  consoleLogs: 'verbose' | 'silent';
  buildName: string;
  projectName: string;
}

// Platform configurations
export const platformConfigs: Record<string, PlatformConfig> = {
  'chrome-windows': {
    name: 'chrome-windows',
    browserName: 'chromium',
    os: 'Windows',
    osVersion: '11',
    viewport: { width: 1920, height: 1080 }
  },
  'android-chrome': {
    name: 'android-chrome',
    browserName: 'chromium',
    os: 'android',
    osVersion: '13.0',
    deviceName: 'Samsung Galaxy S23',
    realMobile: true,
    viewport: { width: 393, height: 851 }
  },
  'ios-safari': {
    name: 'ios-safari',
    browserName: 'webkit',
    os: 'ios',
    osVersion: '16',
    deviceName: 'iPhone 14',
    realMobile: true,
    viewport: { width: 390, height: 844 }
  }
};

// Environment-specific configurations
const environments: Record<Environment, Partial<BaseConfig>> = {
  dev: {
    baseURL: 'https://aeons-dev.info'
  },
  stage: {
    baseURL: 'https://aeonstest.info'
  },
  prod: {
    baseURL: 'https://aeons.co.uk'
  }
};

// Brand-specific environment URLs
const brandEnvironmentURLs: Record<Brand, Record<Environment, string>> = {
  aeons: {
    dev: 'https://aeons-dev.info',
    stage: 'https://aeonstest.info',
    prod: 'https://aeons.co.uk'
  },
  dss: {
    dev: 'https://dss-dev.info',
    stage: 'https://dss.crm-test.info',
    prod: 'https://drsisterskincare.com'
  },
  ypn: {
    dev: 'https://ypn-dev.info',
    stage: 'https://ypntest.info',
    prod: 'https://yourpetnutrition.com'
  }
};

// Brand-specific configurations
const brandConfigs: Record<Brand, Partial<BaseConfig>> = {
  aeons: {
    testDir: './tests/regression/aeons'
  },
  dss: {
    testDir: './tests/regression/dss'
  },
  ypn: {
    testDir: './tests/shopify/ypn'
  }
};

// Create configuration
class ConfigurationManager {
  private static instance: ConfigurationManager;
  private config: PlaywrightTestConfig;

  private constructor() {
    const env = (process.env.TEST_ENV || 'stage') as Environment;
    const brand = (process.env.BRAND || 'aeons') as Brand;
    const testType = (process.env.TEST_TYPE || 'smoke') as TestType;
    const dataSet = (process.env.TEST_DATA_SET || 'default') as DataSet;

    // Get brand-specific URL for the current environment if available
    const brandSpecificURL = brandEnvironmentURLs[brand]?.[env];

    // Merge configurations
    const baseConfig: BaseConfig = {
      ...environments[env],
      ...brandConfigs[brand],
      brand,
      environment: env,
      testType,
      dataSet,
      testDir: brandConfigs[brand].testDir || './tests',
      // Use brand-specific URL if available, otherwise fall back to environment default
      baseURL: brandSpecificURL || environments[env].baseURL || 'https://aeonstest.info',
      testDataConfig: {
        dataSet: dataSet,
        brand: brand
      } as TestDataManagerOptions
    };

    // BrowserStack configuration
    const browserstackConfig: BrowserStackConfig = {
      username: process.env.BROWSERSTACK_USERNAME || '',
      accessKey: process.env.BROWSERSTACK_ACCESS_KEY || '',
      debug: true,
      networkLogs: true,
      consoleLogs: 'verbose',
      buildName: process.env.BUILD_NAME || `${brand}_${testType}_${env}`,
      projectName: brand.toUpperCase()
    };

    this.config = {
      testDir: baseConfig.testDir,
      timeout: 60000,
      workers: 1,
      use: {
        baseURL: baseConfig.baseURL,
        screenshot: 'only-on-failure',
        trace: 'on-first-retry',
        video: 'on-first-retry',
        testDataManager: {
          dataSet: baseConfig.dataSet,
          brand: baseConfig.brand
        } as TestDataManagerOptions
      },
      projects: this.generateProjects(baseConfig, browserstackConfig)
    };
  }

  private generateProjects(baseConfig: BaseConfig, bsConfig: BrowserStackConfig): Project[] {
    return Object.values(platformConfigs).map(platform => ({
      name: platform.name,
      testMatch: this.getTestPattern(baseConfig.testType),
      use: {
        ...platform,
        'browserstack.username': bsConfig.username,
        'browserstack.accessKey': bsConfig.accessKey,
        'browserstack.debug': bsConfig.debug,
        'browserstack.networkLogs': bsConfig.networkLogs,
        'browserstack.consoleLogs': bsConfig.consoleLogs,
        'browserstack.buildName': bsConfig.buildName,
        'browserstack.projectName': bsConfig.projectName
      }
    }));
  }

  private getTestPattern(testType: TestType): RegExp {
    const patterns = {
      smoke: '.*smoke.*\\.spec\\.js',
      visual: '.*visual.*\\.spec\\.js',
      regression: '.*regression.*\\.spec\\.js'
    };

    // Get environment-specific test pattern
    const env = process.env.TEST_ENV as Environment;
    if (testType === 'smoke' && env) {
      console.log(`Configuring test pattern for ${env} environment`);
      // Return the base pattern but we'll use the grep in the command line
      // to further filter to the specific environment test tag
    }

    return new RegExp(patterns[testType]);
  }

  public static getInstance(): ConfigurationManager {
    if (!ConfigurationManager.instance) {
      ConfigurationManager.instance = new ConfigurationManager();
    }
    return ConfigurationManager.instance;
  }

  public getConfig(): PlaywrightTestConfig {
    return this.config;
  }
}

export default ConfigurationManager.getInstance().getConfig();
