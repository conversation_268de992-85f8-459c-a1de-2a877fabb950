# BrowserStack Playwright Testing Framework Guidelines

This document provides essential information for developers working on the BrowserStack Playwright testing framework.

## Build/Configuration Instructions

### Setting Up the Project

1. **Clone the repository and install dependencies**:
   ```bash
   git clone <repository-url>
   cd browserstack-playwright
   npm install
   ```

2. **Environment Configuration**:
   - Create a `.env` file in the project root with the following variables:
     ```
     BROWSERSTACK_USERNAME=your_username
     BROWSERSTACK_ACCESS_KEY=your_access_key
     MAILTRAP_API_TOKEN=your_mailtrap_token
     MAILTRAP_INBOX_ID=your_inbox_id
     ```
   - For local testing without BrowserStack, these variables are optional.

3. **BrowserStack Configuration**:
   - The `browserstack.yml` file contains platform configurations.
   - Modify this file to add or update test platforms.

## Testing Information

### Running Tests

#### Local Test Execution

1. **Run a specific test file**:
   ```bash
   node run-test.js tests\path\to\test.spec.js
   ```

2. **Run tests with specific tags**:
   ```bash
   node run-test.js tests\path\to\test.spec.js --tags="@smoke"
   ```

3. **Run tests on specific platforms**:
   ```bash
   node run-test.js tests\path\to\test.spec.js --platform=windows-chrome
   node run-test.js tests\path\to\test.spec.js --platform=iphone-14
   ```

4. **Run tests in specific environments**:
   ```bash
   node run-test.js tests\path\to\test.spec.js --env=stage
   node run-test.js tests\path\to\test.spec.js --env=prod
   ```

#### BrowserStack Test Execution

1. **Run tests on BrowserStack**:
   ```bash
   npm run test:bs -- tests\path\to\test.spec.js
   ```

2. **Run tests on specific BrowserStack platforms**:
   ```bash
   npm run test:chrome:bs -- tests\path\to\test.spec.js
   npm run test:safari:bs -- tests\path\to\test.spec.js
   npm run test:android:bs -- tests\path\to\test.spec.js
   npm run test:ios:bs -- tests\path\to\test.spec.js
   ```

### Adding New Tests

1. **Create a new test file**:
   - Place test files in the appropriate directory under `tests/`
   - Use the following template:
     ```javascript
     /**
      * @fileoverview Description of the test
      * @tags @tag1 @tag2
      */
     const { test, expect } = require('@playwright/test');
     // For purchase flows, use:
     // const { test, expect } = require('../../common/fixtures/purchase-fixtures');

     test('Test name', async ({ page }) => {
       // Test code
     });
     ```

2. **Using Page Objects**:
   - Page objects are located in `src/pages/`
   - Import and use them in your tests:
     ```javascript
     const { ProductPage } = require('../../src/pages/shop/ProductPage');

     test('Product test', async ({ page }) => {
       const productPage = new ProductPage(page);
       await productPage.navigateToProduct('product-url');
     });
     ```

3. **Using Test Data**:
   - Test data is managed through the `testDataManager` fixture
   - Access it in your tests:
     ```javascript
     test('Data-driven test', async ({ testDataManager }) => {
       const user = testDataManager.getUser('default');
       const product = testDataManager.getProduct('product_id');
     });
     ```

### Known Issues and Workarounds

1. **Tag Handling in run-test.js**:
   - When running tests with only a file path, tags may not be properly extracted
   - Workaround: Always specify tags explicitly:
     ```bash
     node run-test.js tests\path\to\test.spec.js --tags="@smoke"
     ```

## Test Data Management

### Data Architecture Overview

1. **TestDataManager**:
   - The project uses a centralized TestDataManager to handle all test data
   - Located in `tests/data/test-data-manager.js`
   - Provides methods to access products, users, payment methods, etc.
   - Automatically handles environment and brand-specific data

2. **Data Flow**:
   ```
   ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
   │  Base Test Data │     │ Dataset Overrides│     │  Test Execution  │
   │  (YAML files)   │────▶│  (Environment-  │────▶│  (Test Scripts)  │
   │                 │     │  specific YAMLs) │     │                 │
   └─────────────────┘     └─────────────────┘     └─────────────────┘
   ```

3. **Initialization Process**:
   - TestDataManager is initialized with brand, environment, and dataset parameters
   - Base data is loaded from brand-specific YAML files
   - Environment-specific overrides are applied if available
   - The resulting merged data is used in tests

### Test Data Organization

1. **Directory Structure**:
   ```
   tests/data/
   ├── brands/
   │   ├── aeons/
   │   │   ├── config/
   │   │   │   ├── dev.yml
   │   │   │   ├── stage.yml
   │   │   │   └── prod.yml
   │   │   ├── datasets/
   │   │   │   ├── staging.yml
   │   │   │   └── production.yml
   │   │   ├── products.yml
   │   │   ├── test_data.yml
   │   │   └── content-mapping.yml
   │   ├── dss/
   │   │   └── ...
   │   └── ypn/
   │       └── ...
   └── test-data-manager.js
   ```

2. **Key Files**:
   - `products.yml`: Contains product definitions, options, prices, and metadata
   - `test_data.yml`: Contains test users, payment methods, shipping methods, etc.
   - `datasets/[environment].yml`: Environment-specific overrides
   - `config/[environment].yml`: Environment configuration (URLs, features, etc.)
   - `content-mapping.yml`: Content verification mappings for comparison tests

### Using Test Data in Tests

1. **Accessing the TestDataManager**:
   ```javascript
   test('Example test', async ({ testDataManager }) => {
     // TestDataManager is available as a fixture
   });
   ```

2. **Getting Product Data**:
   ```javascript
   const product = testDataManager.getProduct('product_slug');
   console.log(product.name);        // Product name
   console.log(product.fullUrl);     // Complete product URL
   ```

3. **Getting User Data**:
   ```javascript
   const user = testDataManager.getUser('default');
   // Or for a specific user type:
   const internationalUser = testDataManager.getUser('international');
   ```

4. **Getting Payment Methods**:
   ```javascript
   const paymentMethod = testDataManager.getPaymentMethod('stripe_valid');
   ```

5. **Getting Shipping Methods**:
   ```javascript
   const shippingMethod = testDataManager.getShippingMethod('UK');
   ```

6. **Getting Prices**:
   ```javascript
   const price = testDataManager.getPrice('product_slug', 'flavor', 'oneTime', 'minimum');
   ```

7. **Storing Order Data for Later Use**:
   ```javascript
   testDataManager.setOrderData('ORD123456', 99.95, '<EMAIL>', {
     items: ['Product A', 'Product B'],
     shippingMethod: 'Express'
   });

   // Later in another test:
   const orderData = testDataManager.getLatestOrderData();
   // or
   const specificOrder = testDataManager.getOrderDataByNumber('ORD123456');
   ```

### Updating Existing Test Data

1. **Modifying Product Data**:
   - Open the appropriate brand's `products.yml` file
   - Find the product entry you want to modify
   - Update the values as needed
   - For environment-specific changes, use dataset overrides instead

2. **Modifying User Data**:
   - Open the appropriate brand's `test_data.yml` file
   - Find the user entry under `test_users` section
   - Update the values as needed

3. **Environment-Specific Overrides**:
   - To modify data only for a specific environment, edit the appropriate file in `datasets/` directory
   - For example, to change a product price only in production, edit `datasets/production.yml`

### Adding New Test Data

1. **Adding a New Product**:
   ```yaml
   # In products.yml
   new_product_key:
     name: "New Product Name"
     slug: "new-product-slug"
     urlPath: "products/new-product-slug"
     options:
       purchaseTypes:
         oneTime:
           label: "One-Time Purchase"
           price: 49.99
         subscription:
           label: "Subscribe & Save"
           price: 39.99
       quantities:
         minimum:
           numberOfItems: 1
         medium:
           numberOfItems: 3
         maximum:
           numberOfItems: 6
   ```

2. **Adding a New User Type**:
   ```yaml
   # In test_data.yml under test_users section
   new_user_type:
     firstName: "John"
     lastName: "Doe"
     email: "<EMAIL>"
     phone: "1234567890"
     address: "123 Test St"
     city: "London"
     postcode: "W1A 1AA"
     country: "GB"
   ```

3. **Adding a New Payment Method**:
   ```yaml
   # In test_data.yml under payment_methods section
   new_payment_method:
     type: card
     number: "****************"
     expiry: "12/25"
     cvc: "123"
   ```

### Environment-Specific Data Overrides

1. **Creating Environment Overrides**:
   - Create or edit files in the `datasets/` directory
   - Use the same structure as the base files, but only include what needs to be overridden

2. **Example Override**:
   ```yaml
   # In datasets/staging.yml
   products:
     product_key:
       options:
         purchaseTypes:
           oneTime:
             price: 39.99  # Override just the price for staging

   test_data:
     test_users:
       default:
         email: "<EMAIL>"  # Override just the email
   ```

3. **Applying Overrides**:
   - Overrides are automatically applied when running tests with the specified dataset
   - Use the `--dataset` parameter when running tests:
     ```bash
     node run-test.js tests\path\to\test.spec.js --dataset=staging
     ```

### Best Practices for Test Data Management

1. **Keep Base Data Generic**:
   - Base data in `products.yml` and `test_data.yml` should be suitable for most tests
   - Use environment-specific overrides for special cases

2. **Use Descriptive Keys**:
   - Use clear, descriptive keys for products and user types
   - Follow consistent naming conventions (snake_case recommended)

3. **Validate Test Data**:
   - Use the `validateTestDataEnhanced()` utility in tests to ensure data is complete
   - Add validation early in test setup to catch issues quickly

4. **Document Special Requirements**:
   - Add comments in YAML files for any special requirements or dependencies
   - Document the purpose of custom user types or payment methods

5. **Avoid Hardcoding Values in Tests**:
   - Always retrieve values from TestDataManager instead of hardcoding
   - This ensures tests remain valid when data changes

6. **Use Dataset Overrides Appropriately**:
   - Only override what's necessary for a specific environment
   - Keep overrides minimal and focused

## Additional Development Information

### Code Style and Best Practices

1. **Test Structure**:
   - Use descriptive test names
   - Group related tests using `test.describe()`
   - Use `test.step()` for clear test steps
   - Add proper assertions with descriptive messages

2. **Page Object Model**:
   - Keep selectors in the page object classes
   - Implement high-level methods that represent user actions
   - Avoid direct page interactions in test files

3. **Error Handling**:
   - Add proper error handling in critical sections
   - Use try/catch blocks for operations that might fail
   - Add screenshots on failure for better debugging

### Debugging Tests

1. **Debug Mode**:
   ```bash
   node run-test.js tests\path\to\test.spec.js --debug=true
   ```

2. **Screenshots and Videos**:
   - Screenshots are automatically captured on test failure
   - Videos are recorded for failed tests when using `--video=on`
   - Find them in the `test-results/` directory

3. **BrowserStack Debugging**:
   - Access test sessions in the BrowserStack dashboard
   - View screenshots, videos, and logs
   - Use the BrowserStack Local tunnel for testing local environments

### CI/CD Integration

1. **GitLab CI**:
   - CI configuration is in `.gitlab-ci.yml`
   - Tests are automatically run on merge requests
   - Results are reported in the GitLab CI/CD pipeline

2. **GitHub Actions**:
   - Workflow configurations are in `.github/workflows/`
   - Tests can be run manually or on push/PR events
