#!/usr/bin/env node
/**
 * BrowserStack Test Runner
 * Simplified script for running BrowserStack tests with direct platform mapping
 */

const { spawn } = require('child_process');
const path = require('path');
const yaml = require('yaml');
const fs = require('fs');

// Supported platforms
const supportedPlatforms = [
  'windows-chrome', 'mac-safari', 'firefox', 'samsung-galaxy-s23', 'iphone-14'
];

// Platform display names
const platformNames = {
  'windows-chrome': 'Chrome on Windows',
  'mac-safari': 'Safari on Mac',
  'firefox': 'Firefox',
  'samsung-galaxy-s23': 'Samsung Galaxy S23',
  'iphone-14': 'iPhone 14'
};

// Load environment variables from .env file if present
try {
  require('dotenv').config();
  console.log('Loaded environment variables from .env file');
} catch (e) {
  console.log('dotenv not found, skipping .env file loading');
}

// Parse command line arguments
const args = process.argv.slice(2);
let platform = process.env.PLATFORM || 'windows-chrome';
let testPath = '';
let testTag = process.env.TEST_TAG || '';
let testType = process.env.TEST_TYPE || 'regression';
let brand = process.env.BRAND || 'aeons';
let testEnv = process.env.TEST_ENV || 'stage';
let headless = false;
let debug = false;
let workers = process.env.WORKERS || 1;
let retries = process.env.RETRY_COUNT || 1;

// Process command line arguments
for (let i = 0; i < args.length; i++) {
  const arg = args[i];
  if (arg === '--platform' && i + 1 < args.length) {
    platform = args[i + 1];
    i++;
  } else if (arg === '--test-path' && i + 1 < args.length) {
    testPath = args[i + 1];
    i++;
  } else if (arg === '--test-type' && i + 1 < args.length) {
    testType = args[i + 1];
    i++;
  } else if (arg === '--tag' && i + 1 < args.length) {
    testTag = args[i + 1];
    i++;
  } else if (arg === '--brand' && i + 1 < args.length) {
    brand = args[i + 1];
    i++;
  } else if (arg === '--env' && i + 1 < args.length) {
    testEnv = args[i + 1];
    i++;
  } else if (arg === '--headless') {
    headless = true;
  } else if (arg === '--debug') {
    debug = true;
  } else if (arg === '--workers' && i + 1 < args.length) {
    workers = args[i + 1];
    i++;
  } else if (arg === '--retries' && i + 1 < args.length) {
    retries = args[i + 1];
    i++;
  }
}

// Validate platform
if (!supportedPlatforms.includes(platform)) {
  console.warn(`Warning: Unsupported platform: ${platform}`);
  console.warn(`Available platforms: ${supportedPlatforms.join(', ')}`);
  process.exit(1);
}

// Determine if it's a mobile platform
const isMobile = platform.includes('galaxy') || 
                platform.includes('iphone') || 
                platform.includes('android') || 
                platform.includes('ios');

// Set environment variables for the test run
process.env.PLATFORM = platform;
process.env.TEST_TYPE = testType;
process.env.BRAND = brand;
process.env.TEST_ENV = testEnv;
process.env.BROWSERSTACK_SDK_ENABLED = 'true';
process.env.BROWSERSTACK_FORCED = 'true';
process.env.IS_MOBILE = isMobile ? 'true' : 'false';

// Set BROWSERSTACK_REAL_DEVICE for mobile platforms
if (isMobile) {
  process.env.BROWSERSTACK_REAL_DEVICE = 'true';
  console.log('Setting BROWSERSTACK_REAL_DEVICE=true for mobile testing');
}

// Generate a unique build name if not set
if (!process.env.BUILD_NAME) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '_');
  process.env.BUILD_NAME = `${brand}_${testType}_${platform}_${timestamp}`;
}

console.log('🚀 Starting BrowserStack test run with the following configuration:');
console.log(`  Platform: ${platform} (${platformNames[platform] || platform})`);
console.log(`  Test Type: ${testType}`);
console.log(`  Brand: ${brand}`);
console.log(`  Environment: ${testEnv}`);
console.log(`  Build Name: ${process.env.BUILD_NAME}`);
console.log(`  Headless: ${headless}`);
console.log(`  Debug: ${debug}`);
console.log(`  Workers: ${workers}`);
console.log(`  Retries: ${retries}`);
console.log(`  Mobile Device: ${isMobile}`);

// Determine the test path
if (!testPath) {
  testPath = `./tests/${testType}/${brand}/`;
}
console.log(`  Test Path: ${testPath}`);

// Construct the playwright command
const playwrightArgs = [
  'npx', 
  'browserstack-node-sdk', 
  'playwright', 
  'test',
  testPath,
  `--project="${platform}"`,
  `--workers=${workers}`,
  `--retries=${retries}`
];

// Add tag filter if specified
if (testTag) {
  playwrightArgs.push(`--grep="${testTag}"`);
  console.log(`  Test Tag: ${testTag}`);
}

// Add additional arguments as needed
if (headless) {
  // Don't add anything for headless mode, as it's the default
} else {
  playwrightArgs.push('--headed');
}

if (debug) {
  playwrightArgs.push('--debug');
}

// Run the command
console.log(`\nExecuting: ${playwrightArgs.join(' ')}`);
const playwrightProcess = spawn(playwrightArgs[0], playwrightArgs.slice(1), {
  stdio: 'inherit',
  shell: true,
  env: {
    ...process.env,
    // Additional environment variables for BrowserStack
    BROWSERSTACK_SDK_ENABLED: 'true',
    BROWSERSTACK_FORCED: 'true',
    PROJECT_NAME: platformNames[platform] || platform
  }
});

playwrightProcess.on('close', (code) => {
  console.log(`\nBrowserStack test run completed with exit code: ${code}`);
  
  if (code !== 0) {
    console.log('\nTroubleshooting tips:');
    console.log('1. Check if the browser/device is supported by BrowserStack');
    console.log('2. Verify that your BrowserStack credentials are correct');
    console.log('3. Check if the test file exists and contains the specified tag');
    console.log('4. Examine the test logs for specific error messages');
    
    // Suggest available devices
    console.log('\nAvailable platforms:');
    console.log('- Desktop: ' + supportedPlatforms.filter(p => !p.includes('galaxy') && !p.includes('iphone')).join(', '));
    console.log('- Mobile: ' + supportedPlatforms.filter(p => p.includes('galaxy') || p.includes('iphone')).join(', '));
  }
  
  process.exit(code);
});
