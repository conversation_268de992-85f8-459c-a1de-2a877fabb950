# CI/CD Integration for Browserstack Playwright Tests

This directory contains scripts and configurations for integrating the Browserstack Playwright tests with CI/CD pipelines, specifically with the Aeons repository GitLab CI/CD.

## Overview

- Tests are triggered via GitLab CI/CD API from the Aeons repository
- Different tests are run based on the deployment environment (dev vs staging)
- Test results are reported back to GitLab

## How It Works

1. The Aeons repository's CI/CD pipeline includes manual jobs to trigger tests:
   - `test_smoke_dev` - Runs dev-specific smoke tests
   - `test_smoke_staging` - Runs staging-specific smoke tests

2. These jobs use curl to trigger a pipeline in this repository using the GitLab API

3. This repository's CI/CD pipeline runs the appropriate tests based on environment variables

## Required Environment Variables

In the GitLab CI/CD settings for the Aeons repository, the following variables must be set:

- `BROWSERSTACK_TEST_TRIGGER_TOKEN` - The CI/CD trigger token for this repository
- `BROWSERSTACK_TEST_PROJECT_ID` - The GitLab project ID for this repository

## Manual Testing

You can also run the environment-specific tests locally using:

```bash
# For dev environment
npm run test:bs:dev

# For staging environment
npm run test:bs:staging

# Using the CI script directly
node ci/run-env-tests.js dev chrome-windows
node ci/run-env-tests.js stage chrome-windows
```

## Test Tags

The tests use specific tags to determine which tests to run:

- `@smoke_one_time` - Tests for staging environment
- `@smoke_one_time_dev` - Tests for dev environment
