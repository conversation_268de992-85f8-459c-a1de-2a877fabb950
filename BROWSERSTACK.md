# Simplified BrowserStack Testing with <PERSON><PERSON>

This document explains the simplified approach to BrowserStack testing in this project.

## Overview

This project uses [<PERSON><PERSON>](https://playwright.dev/) with [BrowserStack](https://www.browserstack.com/) for cross-browser and cross-platform testing on real devices. We've standardized on the BrowserStack SDK approach for running tests, which provides better compatibility with mobile devices and a more streamlined configuration.

## Key Components

1. **Universal Test Runner**: `run-test.js` - A unified command-line tool for running tests with both local Playwright and BrowserStack

2. **Unified BrowserStack Helper**: A single helper class that handles all BrowserStack operations

3. **Standardized Configuration**: All BrowserStack settings now live in `browserstack.yml` 

4. **Simplified Scripts**: Package.json now contains simplified scripts using the universal runner

## Running Tests

### Basic Usage

```bash
# Run a specific test locally
npm run test tests/path/to/test.spec.js

# Run a test on BrowserStack
npm run test:bs tests/path/to/test.spec.js

# Run with specific tags
npm run test tests/path/to/test.spec.js --tags=@visual

# Run on specific platform
npm run test tests/path/to/test.spec.js --platform=android-s23
```

### Predefined Scripts

The package.json includes several predefined scripts:

```bash
# Run visual defect verification tests
npm run test:visual

# Run visual defect verification tests on BrowserStack
npm run test:visual:bs

# Run smoke tests
npm run test:smoke

# Run tests on Android
npm run test:android:bs

# Run tests on iOS
npm run test:ios:bs
```

### Advanced Usage

The universal test runner supports these options:

```bash
node run-test.js <test-path> [options]

Options:
  --platform=<platform>     Platform to run tests on (e.g., windows-chrome, android-s23)
  --brand=<brand>           Brand to test (default: aeons)
  --env=<env>               Environment to test (stage, dev, prod) (default: stage)
  --tags=<tags>             Test tags to filter (e.g., @smoke, @visual)
  --workers=<count>         Number of worker processes (default: 1)
  --dataset=<dataset>       Test data set to use (default: default)
  --browserstack=<bool>     Use BrowserStack (true/false)
  --debug=<bool>            Enable debug mode (true/false)
```

## Platform Support

The following platforms are pre-configured in `browserstack.yml`:

### Mobile
- `android-s23`: Samsung Galaxy S23 with Chrome
- `android-s22`: Samsung Galaxy S22 with Chrome
- `ios-iphone14`: iPhone 14 with Safari
- `ios-iphone13`: iPhone 13 with Safari

### Desktop
- `windows-chrome`: Windows 11 with Chrome
- `windows-edge`: Windows 11 with Edge
- `mac-safari`: Mac OS Ventura with Safari
- `mac-chrome`: Mac OS Ventura with Chrome

## Key Files

- `run-test.js`: Universal test runner that works with both local and BrowserStack tests
- `browserstack.yml`: Single source of truth for all BrowserStack configurations
- `src/utils/browserstack/unified-browserstack-helper.js`: Unified helper for BrowserStack operations
- `tests/fixtures/unified-fixture.js`: Standardized test fixture that works with both approaches
- `tests/global-setup.js`: Simplified global setup for all tests

## Notes for Visual Testing

- Visual testing integrates with Cloudinary for storing screenshots
- Gemini AI is used for intelligent visual analysis 
- BrowserStack is used only for device rendering, not for visual comparison (not using Percy)

## Environment Variables

Essential environment variables:
- `BROWSERSTACK_USERNAME`: Your BrowserStack username
- `BROWSERSTACK_ACCESS_KEY`: Your BrowserStack access key
- `CLOUDINARY_CLOUD_NAME`: Cloudinary cloud name
- `CLOUDINARY_API_KEY`: Cloudinary API key  
- `CLOUDINARY_API_SECRET`: Cloudinary API secret
- `GEMINI_API_KEY`: Your Google Gemini API key

Optional variables with defaults:
- `PLATFORM`: Default platform (windows-chrome)
- `BRAND`: Default brand (aeons)
- `TEST_ENV`: Default environment (stage)
- `BUILD_NAME`: Name for the test build (auto-generated with timestamp if not specified)
- `BROWSERSTACK_RETRIES`: Number of retry attempts (default 0)
