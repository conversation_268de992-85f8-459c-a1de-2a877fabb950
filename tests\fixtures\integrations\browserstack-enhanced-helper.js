/**
 * @fileoverview BrowserStack Enhanced Helper
 *
 * Enhanced BrowserStack integration that eliminates repetitive BrowserStack
 * setup code while maintaining compatibility with existing patterns.
 * 
 * Features:
 * - Context-aware screenshot capture
 * - Unified session management
 * - Enhanced error handling and logging
 * - Support for single concurrent session limitation
 * - Real device vs emulation handling
 */

const { BrowserStackService } = require('../../../src/utils/browserstack/browserstack-service');
const { VisualAnalysisHelper } = require('../../../src/utils/visual-analisys-helper');

/**
 * Enhanced BrowserStack Helper with context awareness
 */
class BrowserStackEnhancedHelper {
    constructor() {
        this.baseHelper = BrowserStackService.getInstance();
        this.isEnabled = process.env.BROWSERSTACK_SESSION_ID !== undefined;
        this.isSdkMode = process.env.BROWSERSTACK_SDK_ENABLED === 'true';
        this.sessionId = process.env.BROWSERSTACK_SESSION_ID;
        this.platform = process.env.PLATFORM || 'unknown';
        
        console.log(`[BrowserStackEnhancedHelper] Initialized - Enabled: ${this.isEnabled}, SDK Mode: ${this.isSdkMode}, Platform: ${this.platform}`);
    }

    /**
     * Take screenshot with enhanced context information
     * @param {Object} page - Playwright page object
     * @param {string} name - Screenshot name
     * @param {Object} testContext - Test context information
     * @returns {Promise<string>} Screenshot path or URL
     */
    async takeScreenshotWithContext(page, name, testContext = {}) {
        console.log(`[BrowserStackEnhancedHelper] Taking screenshot: ${name}`);
        
        // If BrowserStack is not enabled, take local screenshot
        if (!this.isEnabled) {
            const localPath = `test-results/${name}-${Date.now()}.png`;
            await page.screenshot({ path: localPath });
            console.log(`[BrowserStackEnhancedHelper] Local screenshot saved: ${localPath}`);
            return localPath;
        }

        try {
            // Prepare enhanced options with context
            const enhancedOptions = {
                cdpMeta: {
                    _requestedPlatform: this.platform,
                    _actualPlatform: this.platform,
                    _testName: testContext.testName || 'unknown',
                    _brand: testContext.brand || process.env.BRAND || 'unknown',
                    _environment: testContext.environment || process.env.TEST_ENV || 'unknown',
                    _step: testContext.step || 'unknown',
                    _timestamp: new Date().toISOString(),
                    browserStackSession: this.sessionId,
                    _requestedDeviceType: null
                }
            };

            // Use existing VisualAnalysisHelper for consistency
            const result = await VisualAnalysisHelper.captureAndUploadScreenshot(
                page,
                testContext.testName || 'unknown',
                name,
                enhancedOptions
            );

            console.log(`[BrowserStackEnhancedHelper] Screenshot uploaded successfully: ${name}`);
            return result;
        } catch (error) {
            console.error(`[BrowserStackEnhancedHelper] Screenshot failed: ${error.message}`);
            
            // Fallback to local screenshot
            try {
                const fallbackPath = `test-results/fallback-${name}-${Date.now()}.png`;
                await page.screenshot({ path: fallbackPath });
                console.log(`[BrowserStackEnhancedHelper] Fallback screenshot saved: ${fallbackPath}`);
                return fallbackPath;
            } catch (fallbackError) {
                console.error(`[BrowserStackEnhancedHelper] Fallback screenshot also failed: ${fallbackError.message}`);
                throw error;
            }
        }
    }

    /**
     * Take screenshot using legacy method for backward compatibility
     * @param {Object} page - Playwright page object
     * @param {string} name - Screenshot name
     * @param {Object} options - Legacy options
     * @returns {Promise<string>} Screenshot path or URL
     */
    async takeScreenshot(page, name, options = {}) {
        // Convert legacy options to new context format
        const testContext = {
            testName: options.testName || 'legacy-test',
            brand: options.brand || process.env.BRAND,
            environment: options.environment || process.env.TEST_ENV,
            step: options.step || 'unknown'
        };

        return await this.takeScreenshotWithContext(page, name, testContext);
    }

    /**
     * Wait for visual stability with platform-specific handling
     * @param {Object} page - Playwright page object
     * @param {Object} options - Stability options
     * @returns {Promise<void>}
     */
    async waitForVisualStability(page, options = {}) {
        console.log('[BrowserStackEnhancedHelper] Waiting for visual stability');
        
        try {
            // Use base helper if available
            if (this.baseHelper && typeof this.baseHelper.waitForVisualStability === 'function') {
                await this.baseHelper.waitForVisualStability(page, options);
            } else {
                // Fallback implementation
                await this.fallbackVisualStability(page, options);
            }
        } catch (error) {
            console.warn(`[BrowserStackEnhancedHelper] Visual stability wait failed: ${error.message}`);
            // Continue execution - visual stability is not critical
        }
    }

    /**
     * Fallback visual stability implementation
     * @param {Object} page - Playwright page object
     * @param {Object} options - Stability options
     * @private
     */
    async fallbackVisualStability(page, options = {}) {
        const isMobile = this.platform.includes('galaxy') || 
                        this.platform.includes('iphone') || 
                        this.platform.includes('android');
        
        const waitTime = options.waitTime || (isMobile ? 3000 : 2000);
        
        // Wait for network idle
        await page.waitForLoadState('networkidle', { timeout: 30000 });
        
        // Additional wait for mobile platforms
        if (isMobile) {
            await page.waitForTimeout(waitTime);
        }
        
        console.log(`[BrowserStackEnhancedHelper] Fallback visual stability completed (${waitTime}ms)`);
    }

    /**
     * Register session with enhanced error handling
     * @param {string} sessionId - BrowserStack session ID
     * @returns {Promise<void>}
     */
    async registerSession(sessionId = null) {
        const actualSessionId = sessionId || this.sessionId;
        
        if (!actualSessionId) {
            console.warn('[BrowserStackEnhancedHelper] No session ID available for registration');
            return;
        }

        try {
            if (this.baseHelper && typeof this.baseHelper.registerSession === 'function') {
                await this.baseHelper.registerSession(actualSessionId);
                console.log(`[BrowserStackEnhancedHelper] Session registered: ${actualSessionId}`);
            } else {
                console.warn('[BrowserStackEnhancedHelper] Base helper not available for session registration');
            }
        } catch (error) {
            console.error(`[BrowserStackEnhancedHelper] Session registration failed: ${error.message}`);
            // Don't throw - session registration failure shouldn't stop tests
        }
    }

    /**
     * Set test status with enhanced error handling
     * @param {string} status - Test status (passed, failed)
     * @param {string} reason - Reason for status
     * @returns {Promise<void>}
     */
    async setTestStatus(status, reason = '') {
        if (!this.isEnabled || this.isSdkMode) {
            console.log(`[BrowserStackEnhancedHelper] Skipping test status update - Enabled: ${this.isEnabled}, SDK: ${this.isSdkMode}`);
            return;
        }

        try {
            if (this.baseHelper && typeof this.baseHelper.setTestStatus === 'function') {
                await this.baseHelper.setTestStatus(status, reason);
                console.log(`[BrowserStackEnhancedHelper] Test status set to: ${status}`);
            } else {
                console.warn('[BrowserStackEnhancedHelper] Base helper not available for status update');
            }
        } catch (error) {
            console.error(`[BrowserStackEnhancedHelper] Test status update failed: ${error.message}`);
            // Don't throw - status update failure shouldn't stop tests
        }
    }

    /**
     * Download artifacts with enhanced error handling
     * @param {string} sessionId - BrowserStack session ID
     * @returns {Promise<void>}
     */
    async downloadArtifacts(sessionId = null) {
        const actualSessionId = sessionId || this.sessionId;
        
        if (!actualSessionId || !this.isEnabled) {
            console.log('[BrowserStackEnhancedHelper] Skipping artifact download - no session or not enabled');
            return;
        }

        try {
            // Check if base helper has downloadArtifacts method
            if (this.baseHelper && typeof this.baseHelper.downloadArtifacts === 'function') {
                await this.baseHelper.downloadArtifacts(actualSessionId);
                console.log(`[BrowserStackEnhancedHelper] Artifacts downloaded for session: ${actualSessionId}`);
            } else {
                console.log('[BrowserStackEnhancedHelper] Artifact download not available in base helper');
            }
        } catch (error) {
            console.error(`[BrowserStackEnhancedHelper] Artifact download failed: ${error.message}`);
            // Don't throw - artifact download failure shouldn't stop tests
        }
    }

    /**
     * Get session information
     * @returns {Object} Session information
     */
    getSessionInfo() {
        return {
            sessionId: this.sessionId,
            platform: this.platform,
            isEnabled: this.isEnabled,
            isSdkMode: this.isSdkMode,
            isRealDevice: process.env.BROWSERSTACK_REAL_DEVICE === 'true'
        };
    }

    /**
     * Check if BrowserStack is properly configured
     * @returns {boolean} True if properly configured
     */
    isProperlyConfigured() {
        const hasCredentials = process.env.BROWSERSTACK_USERNAME && process.env.BROWSERSTACK_ACCESS_KEY;
        const hasSession = this.sessionId || this.isSdkMode;
        
        return hasCredentials && hasSession;
    }

    /**
     * Get configuration status for debugging
     * @returns {Object} Configuration status
     */
    getConfigurationStatus() {
        return {
            hasUsername: !!process.env.BROWSERSTACK_USERNAME,
            hasAccessKey: !!process.env.BROWSERSTACK_ACCESS_KEY,
            hasSessionId: !!this.sessionId,
            isSdkMode: this.isSdkMode,
            isEnabled: this.isEnabled,
            platform: this.platform,
            isProperlyConfigured: this.isProperlyConfigured()
        };
    }

    /**
     * Enhanced setup for test execution
     * @param {Object} page - Playwright page object
     * @param {Object} testContext - Test context
     * @returns {Promise<void>}
     */
    async setupForTest(page, testContext = {}) {
        console.log('[BrowserStackEnhancedHelper] Setting up for test execution');
        
        // Register session if needed
        if (this.sessionId && !this.isSdkMode) {
            await this.registerSession();
        }

        // Wait for initial stability
        await this.waitForVisualStability(page, { waitTime: 1000 });

        // Take initial screenshot if context provided
        if (testContext.testName) {
            await this.takeScreenshotWithContext(page, 'test-start', {
                ...testContext,
                step: 'test-initialization'
            });
        }

        console.log('[BrowserStackEnhancedHelper] Test setup completed');
    }

    /**
     * Enhanced cleanup after test execution
     * @param {Object} page - Playwright page object
     * @param {Object} testContext - Test context
     * @param {string} testStatus - Test status (passed/failed)
     * @returns {Promise<void>}
     */
    async cleanupAfterTest(page, testContext = {}, testStatus = 'unknown') {
        console.log(`[BrowserStackEnhancedHelper] Cleaning up after test - Status: ${testStatus}`);
        
        try {
            // Take final screenshot
            if (testContext.testName) {
                await this.takeScreenshotWithContext(page, 'test-end', {
                    ...testContext,
                    step: 'test-completion'
                });
            }

            // Set test status
            await this.setTestStatus(testStatus, testContext.reason || '');

            // Download artifacts
            await this.downloadArtifacts();

        } catch (error) {
            console.error(`[BrowserStackEnhancedHelper] Cleanup failed: ${error.message}`);
            // Don't throw - cleanup failure shouldn't affect test results
        }

        console.log('[BrowserStackEnhancedHelper] Test cleanup completed');
    }
}

module.exports = { BrowserStackEnhancedHelper };
