const { chromium } = require('playwright');
const { ProductPage } = require('../../src/pages/shop/ProductPage');

/**
 * Test script to verify price extraction from both DSS and Aeons websites
 */
async function testPriceSelectors() {
  console.log('Starting test script...');
  
  const browser = await chromium.launch({ 
    headless: false,
    args: ['--disable-web-security', '--disable-features=IsolateOrigins,site-per-process']
  });
  
  console.log('Browser launched');
  
  // Set longer default timeout for all operations
  const context = await browser.newContext({
    viewportSize: { width: 1280, height: 800 },
    timezoneId: 'Europe/London',
  });
  
  // Enable more detailed logging
  context.on('console', message => {
    console.log(`Browser console [${message.type()}]: ${message.text()}`);
  });
  
  const page = await context.newPage();
  
  try {
    // First try DSS website
    console.log('\n------------- Testing DSS website -------------');
    process.env.BASE_URL = 'https://dss.crm-test.info';
    
    const productPage = new ProductPage(page);
    console.log('ProductPage object created');
    
    try {
      await productPage.navigateToProduct('/products/younger-you-skin-cream');
      
      // Try to get price with extra error handling
      try {
        const dssPrice = await productPage.getCurrentPrice();
        console.log(`DSS Price: £${dssPrice}`);
      } catch (priceError) {
        console.error(`Failed to get DSS price: ${priceError.message}`);
        // Take a screenshot for debugging
        await page.screenshot({ path: 'dss-price-error.png' });
      }
    } catch (dssError) {
      console.error(`DSS test failed: ${dssError.message}`);
      await page.screenshot({ path: 'dss-page-error.png' });
    }
    
    // Now try Aeons website
    console.log('\n------------- Testing Aeons website -------------');
    process.env.BASE_URL = 'https://aeonstest.info';
    
    try {
      await productPage.navigateToProduct('/products/aeons-golden-harvest');
      
      // Try to get price with extra error handling
      try {
        const aeonsPrice = await productPage.getCurrentPrice();
        console.log(`Aeons Price: £${aeonsPrice}`);
      } catch (priceError) {
        console.error(`Failed to get Aeons price: ${priceError.message}`);
        // Take a screenshot for debugging
        await page.screenshot({ path: 'aeons-price-error.png' });
      }
    } catch (aeonsError) {
      console.error(`Aeons test failed: ${aeonsError.message}`);
      await page.screenshot({ path: 'aeons-page-error.png' });
    }
    
    console.log('\nTest completed - check results above');
    
  } catch (error) {
    console.error(`Test failed with critical error: ${error.message}`);
    await page.screenshot({ path: 'critical-error.png' });
  } finally {
    await context.close();
    await browser.close();
    console.log('Browser closed');
  }
}

// Run the test
testPriceSelectors().catch(error => {
  console.error(`Fatal error in test: ${error.message}`);
}); 