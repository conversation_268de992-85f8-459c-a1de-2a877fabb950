class AdminSalesFunnelPage {
    constructor(page) {
        this.page = page;
        this.selectors = {
            funnelItemsRows: 'table tbody tr',
            funnelCodeCell: 'td:nth-child(1)',
            isInitialProductCell: 'td:nth-child(3)',  // Column for "Is initial product?"
            actionsCell: 'td:nth-child(5)',           // Column for "Actions"
            editLink: 'a:has-text("Edit")',           // Edit link in actions cell
            usernameField: '#_username',
            passwordField: '#_password',
            loginButton: 'button[type="submit"]'
        };
    }

    async navigate() {
        await this.page.goto('/admin/sales-funnel-items');
        
        if (this.page.url().includes('/admin/login')) {
            console.log('Redirected to login page, attempting to login');
            await this.handleLogin();
        }
        
        await this.page.waitForSelector(this.selectors.funnelItemsRows, { timeout: 15000 });
        
        // Debug info - log what we found
        console.log('Found funnel items table. Listing rows:');
        const rows = await this.page.$$(this.selectors.funnelItemsRows);
        console.log(`Total rows found: ${rows.length}`);
    }
    
    async handleLogin() {
        await this.page.waitForSelector(this.selectors.usernameField);
        
        const username = process.env.ADMIN_USER || 'admin';
        const password = process.env.ADMIN_PASSWORD || 'password';
        
        console.log(`Logging in as ${username}`);
        
        await this.page.fill(this.selectors.usernameField, username);
        await this.page.fill(this.selectors.passwordField, password);
        
        await Promise.all([
            this.page.waitForNavigation({ waitUntil: 'load' }),
            this.page.click(this.selectors.loginButton)
        ]);
    }

    async getFunnelItemByCode(code) {
        console.log(`Looking for funnel item with code: ${code}`);
        const rows = await this.page.$$(this.selectors.funnelItemsRows);
        console.log(`Total rows found: ${rows.length}`);
        
        for (const row of rows) {
            try {
                const codeText = await row.$eval(this.selectors.funnelCodeCell, el => el.textContent.trim());
                console.log(`Found row with code: ${codeText}`);
                
                if (codeText === code) {
                    return row;
                }
            } catch (error) {
                console.warn(`Error getting code text from row: ${error.message}`);
            }
        }
        console.warn(`No funnel item found with code: ${code}`);
        return null;
    }

    async getFunnelLink(code) {
        const row = await this.getFunnelItemByCode(code);
        if (!row) return null;

        try {
            // Instead of looking for a link in the row, we'll construct the URL
            // This is because the actual funnel URLs might not be directly visible in the table
            console.log(`Constructing funnel link for code: ${code}`);
            
            // The DSS funnel URLs typically follow this pattern
            const baseUrl = this.page.url().split('/admin')[0];
            return `${baseUrl}/specials/start/${code}`;
        } catch (error) {
            console.error(`Error getting funnel link for code ${code}:`, error);
            return null;
        }
    }

    async getCloseOrderLink(code) {
        // In DSS admin, close order link is shown at the bottom
        try {
            const closeOrderLinkText = await this.page.textContent('text="Link to close the order:"');
            if (closeOrderLinkText) {
                const closeOrderLinkElement = await this.page.$('text="Link to close the order:" + a');
                if (closeOrderLinkElement) {
                    return await closeOrderLinkElement.getAttribute('href');
                }
            }
            
            // Fallback to a standard close order URL format
            const baseUrl = this.page.url().split('/admin')[0];
            return `${baseUrl}/checkout/complete`;
        } catch (error) {
            console.error(`Error getting close order link:`, error);
            return null;
        }
    }
}

module.exports = { AdminSalesFunnelPage };