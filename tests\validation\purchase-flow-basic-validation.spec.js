/**
 * @fileoverview Basic Purchase Flow Validation
 * 
 * Simple validation test for the purchase flow fixture to ensure
 * the enhanced fixture system works correctly.
 * 
 * @tags @validation @purchase_flow @basic
 */

const { test, expect } = require('../fixtures/workflows/purchase-flow-fixture');

test.describe('Basic Purchase Flow Validation', () => {
    test('Purchase flow fixture should initialize correctly', async ({ 
        purchaseFlow, 
        testDataHelper, 
        purchaseTestData,
        paypalTestData,
        subscriptionTestData,
        mobileTestData
    }) => {
        console.log('[Basic Validation] Testing purchase flow fixture initialization');

        // Verify purchase flow helper is available
        expect(purchaseFlow).toBeTruthy();
        expect(typeof purchaseFlow.executeStandardPurchase).toBe('function');
        expect(typeof purchaseFlow.navigateToProduct).toBe('function');
        expect(typeof purchaseFlow.verifyOrderConfirmationEmail).toBe('function');

        // Verify test data helper is available
        expect(testDataHelper).toBeTruthy();
        expect(typeof testDataHelper.getPurchaseTestData).toBe('function');
        expect(typeof testDataHelper.getPayPalTestData).toBe('function');
        expect(typeof testDataHelper.getSubscriptionTestData).toBe('function');

        // Verify pre-configured test data fixtures
        expect(purchaseTestData).toBeTruthy();
        expect(purchaseTestData.product).toBeTruthy();
        expect(purchaseTestData.user).toBeTruthy();
        expect(purchaseTestData.paymentMethod).toBeTruthy();

        expect(paypalTestData).toBeTruthy();
        expect(paypalTestData.paymentFlow).toBe('paypal');

        expect(subscriptionTestData).toBeTruthy();
        expect(subscriptionTestData.subscriptionFrequency).toBe('monthly');

        expect(mobileTestData).toBeTruthy();
        expect(mobileTestData.isMobile).toBe(true);

        console.log('Purchase flow fixture initialization validation passed');
    });

    test('Test data patterns should work correctly', async ({ testDataHelper }) => {
        console.log('[Basic Validation] Testing test data patterns');

        // Test standard purchase data
        const purchaseData = testDataHelper.getPurchaseTestData('ancient_roots_olive_oil');
        expect(purchaseData.product.name).toBe('Ancient Roots Olive Oil');
        expect(purchaseData.baseUrl).toBeTruthy();
        expect(purchaseData.brand).toBeTruthy();

        // Test PayPal data
        const paypalData = testDataHelper.getPayPalTestData('ancient_roots_olive_oil');
        expect(paypalData.paymentFlow).toBe('paypal');
        expect(paypalData.product.name).toBe('Ancient Roots Olive Oil');

        // Test subscription data
        const subscriptionData = testDataHelper.getSubscriptionTestData('ancient_roots_olive_oil');
        expect(subscriptionData.subscriptionFrequency).toBe('monthly');
        expect(subscriptionData.purchaseType).toBe('subscription');

        // Test mobile data
        const mobileData = testDataHelper.getMobileTestData('ancient_roots_olive_oil');
        expect(mobileData.isMobile).toBe(true);
        expect(mobileData.extendedTimeouts).toBe(true);

        // Test brand-specific data
        const brandData = testDataHelper.getBrandSpecificData();
        expect(brandData.brand).toBeTruthy();
        expect(brandData.baseUrl).toBeTruthy();
        expect(brandData.emailDomain).toBeTruthy();

        console.log('Test data patterns validation passed');
    });

    test('Purchase flow helper methods should be available', async ({ 
        purchaseFlow, 
        testDataHelper,
        pageObjectFactory 
    }) => {
        console.log('[Basic Validation] Testing purchase flow helper methods');

        // Verify helper has all required dependencies
        expect(purchaseFlow.pages).toBeTruthy();
        expect(purchaseFlow.dataHelper).toBeTruthy();
        expect(purchaseFlow.emailHelper).toBeTruthy();
        expect(purchaseFlow.browserStackHelper).toBeTruthy();
        expect(purchaseFlow.deviceHelper).toBeTruthy();

        // Verify page objects are accessible through the helper
        expect(purchaseFlow.pages.shop).toBeTruthy();
        expect(purchaseFlow.pages.shop.product).toBeTruthy();
        expect(purchaseFlow.pages.shop.cart).toBeTruthy();
        expect(purchaseFlow.pages.shop.checkout).toBeTruthy();
        expect(purchaseFlow.pages.shop.confirmation).toBeTruthy();

        // Test that page objects are the same as those from factory
        const directShop = pageObjectFactory.shop;
        expect(purchaseFlow.pages.shop.product).toBe(directShop.product);
        expect(purchaseFlow.pages.shop.cart).toBe(directShop.cart);

        console.log('Purchase flow helper methods validation passed');
    });

    test('Error handling should work gracefully', async ({ 
        purchaseFlow, 
        testDataHelper 
    }) => {
        console.log('[Basic Validation] Testing error handling');

        // Test invalid product data handling
        try {
            const invalidData = testDataHelper.getPurchaseTestData('nonexistent_product');
            console.log('Unexpected: Invalid product was accepted');
        } catch (error) {
            expect(error.message).toContain('not found');
            console.log('Invalid product error handled correctly');
        }

        // Test email verification graceful degradation
        const validData = testDataHelper.getPurchaseTestData('ancient_roots_olive_oil');
        const emailResult = await purchaseFlow.verifyOrderConfirmationEmail(validData, {
            timeout: 1000 // Very short timeout to trigger failure
        });

        expect(emailResult.emailVerified).toBe(false);
        expect(emailResult.error || emailResult.customerEmail).toBeTruthy();

        console.log('Error handling validation passed');
    });

    test('Backward compatibility should be maintained', async ({ 
        pageObjects, 
        testDataManager,
        baseUrl 
    }) => {
        console.log('[Basic Validation] Testing backward compatibility');

        // Test legacy pageObjects fixture
        expect(pageObjects).toBeTruthy();
        expect(pageObjects.productPage).toBeTruthy();
        expect(pageObjects.cartPage).toBeTruthy();
        expect(pageObjects.checkoutPage).toBeTruthy();

        // Test legacy testDataManager access
        expect(testDataManager).toBeTruthy();
        expect(testDataManager.getProduct).toBeTruthy();
        expect(testDataManager.getUser).toBeTruthy();
        expect(testDataManager.getPaymentMethod).toBeTruthy();

        // Test baseUrl fixture
        expect(baseUrl).toBeTruthy();
        expect(baseUrl).toBe(testDataManager.getBaseUrl());

        console.log('Backward compatibility validation passed');
    });

    test('Performance optimizations should work', async ({ 
        pageObjectFactory,
        testDataHelper 
    }) => {
        console.log('[Basic Validation] Testing performance optimizations');

        // Test page object caching
        const startTime = Date.now();
        
        // Multiple accesses should use cache
        for (let i = 0; i < 5; i++) {
            const shop = pageObjectFactory.shop;
            expect(shop.product).toBeTruthy();
        }
        
        const cacheTime = Date.now() - startTime;
        expect(cacheTime).toBeLessThan(50); // Should be very fast due to caching

        // Test cache statistics
        const stats = pageObjectFactory.getCacheStats();
        expect(stats.size).toBeGreaterThan(0);
        expect(stats.groups).toContain('shop');

        // Test test data helper efficiency
        const dataStartTime = Date.now();
        
        for (let i = 0; i < 5; i++) {
            testDataHelper.getPurchaseTestData('ancient_roots_olive_oil');
        }
        
        const dataTime = Date.now() - dataStartTime;
        expect(dataTime).toBeLessThan(100); // Should be fast

        console.log(`Performance validation passed - Cache: ${cacheTime}ms, Data: ${dataTime}ms`);
    });

    test('DRY implementation should reduce code duplication', async ({ 
        testDataHelper,
        purchaseFlow 
    }) => {
        console.log('[Basic Validation] Testing DRY implementation benefits');

        // Test that multiple test data calls return consistent results
        const data1 = testDataHelper.getPurchaseTestData('ancient_roots_olive_oil');
        const data2 = testDataHelper.getPurchaseTestData('ancient_roots_olive_oil');
        
        expect(data1.product.name).toBe(data2.product.name);
        expect(data1.baseUrl).toBe(data2.baseUrl);
        expect(data1.brand).toBe(data2.brand);

        // Test that different data types share common base data
        const purchaseData = testDataHelper.getPurchaseTestData('ancient_roots_olive_oil');
        const paypalData = testDataHelper.getPayPalTestData('ancient_roots_olive_oil');
        const subscriptionData = testDataHelper.getSubscriptionTestData('ancient_roots_olive_oil');

        // All should have the same product and base information
        expect(purchaseData.product.name).toBe(paypalData.product.name);
        expect(paypalData.product.name).toBe(subscriptionData.product.name);
        expect(purchaseData.baseUrl).toBe(paypalData.baseUrl);
        expect(paypalData.baseUrl).toBe(subscriptionData.baseUrl);

        // But should have different specific configurations
        expect(paypalData.paymentFlow).toBe('paypal');
        expect(subscriptionData.subscriptionFrequency).toBe('monthly');

        console.log('DRY implementation validation passed');
    });
});

test.describe('Integration Validation', () => {
    test('All helpers should work together', async ({ 
        purchaseFlow,
        testDataHelper,
        browserStackHelper,
        deviceHelper,
        emailHelper 
    }) => {
        console.log('[Integration Validation] Testing helper integration');

        // Test that all helpers are properly integrated
        expect(purchaseFlow.dataHelper).toBe(testDataHelper);
        expect(purchaseFlow.browserStackHelper).toBe(browserStackHelper);
        expect(purchaseFlow.deviceHelper).toBe(deviceHelper);
        expect(purchaseFlow.emailHelper).toBe(emailHelper);

        // Test helper cross-functionality
        const testData = testDataHelper.getPurchaseTestData();
        const brandData = testDataHelper.getBrandSpecificData();
        
        expect(emailHelper.brand).toBe(brandData.brand);
        expect(emailHelper.testDataManager).toBe(testDataHelper.testDataManager);

        // Test device helper platform detection
        const platformInfo = deviceHelper.getPlatformInfo();
        expect(platformInfo.platform).toBeDefined();
        expect(typeof platformInfo.isMobile).toBe('boolean');

        // Test BrowserStack helper configuration
        const bsConfig = browserStackHelper.getConfigurationStatus();
        expect(bsConfig).toBeTruthy();
        expect(typeof bsConfig.isEnabled).toBe('boolean');

        console.log('Helper integration validation passed');
    });
});
