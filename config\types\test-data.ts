/**
 * @fileoverview Type definitions for test data management
 */

// Product related types
export interface ProductOptions {
    purchaseTypes: {
        oneTime: string;
        subscription?: string;
    };
    quantities: {
        [key: string]: {
            numberOfItems: number;
        };
    };
    flavors?: string[];
}

export interface ProductPrices {
    oneTime: {
        [key: string]: number;
    };
    subscription?: {
        [key: string]: number;
    };
}

export interface Product {
    name: string;
    urlPath: string;
    options: ProductOptions;
    prices: ProductPrices;
}

// User related types
export interface User {
    email: string;
    firstName: string;
    lastName: string;
    phone: string;
    address: string;
    city: string;
    postcode: string;
    country: string;
}

// Payment method types
export interface PaymentMethod {
    cardNumber: string;
    expiry: string;
    cvc: string;
}

// Shipping method types
export interface ShippingMethod {
    method: string;
    cost: string;
}

// Test data set types
export interface TestDataSet {
    users: {
        [key: string]: User;
    };
    products: {
        [key: string]: Product;
    };
    paymentMethods: {
        [key: string]: PaymentMethod;
    };
    shippingMethods: {
        [key: string]: ShippingMethod;
    };
}

// TestDataManager configuration options
export interface TestDataManagerOptions {
    dataSet: 'default' | 'staging' | 'production';
    brand: 'aeons' | 'dss' | 'ypn';
}

// Extend Playwright's test options
declare module '@playwright/test' {
    interface PlaywrightTestOptions {
        testDataManager: TestDataManagerOptions;
    }
} 