/**
 * @fileoverview Service for interacting with Google's Gemini AI
 */

const { GoogleGenerativeAI } = require('@google/generative-ai');
const { prompts } = require('../config/prompts');
const path = require('path');
const fs = require('fs').promises;
const axios = require('axios');

class GeminiService {
    constructor() {
        const apiKey = process.env.GEMINI_API_KEY;
        if (!apiKey) {
            throw new Error('GEMINI_API_KEY environment variable is required');
        }

        this.genAI = new GoogleGenerativeAI(apiKey);
        this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.0-flash' });
        this.rateLimiter = {
            acquire: async () => {
                // Simple rate limiting implementation
                await new Promise(resolve => setTimeout(resolve, 100));
            },
            release: () => {}
        };
    }

    /**
     * Analyze an image using Gemini
     * @param {string} imageBase64 Base64 encoded image data
     * @param {string} prompt Analysis prompt
     * @returns {Promise<string>} Analysis results
     */
    async analyzeImage(imageBase64, prompt) {
        return await this.withRateLimit(async () => {
            try {
                const result = await this.model.generateContent([
                    prompt,
                    {
                        inlineData: {
                            data: imageBase64,
                            mimeType: 'image/jpeg'
                        }
                    }
                ]);

                return result.response.text();
            } catch (error) {
                console.error('Error analyzing image with Gemini:', error);
                throw error;
            }
        });
    }

    /**
     * Analyze an image from URL using Gemini
     * @param {string} imageUrl URL of the image to analyze (Cloudinary URL)
     * @param {string} prompt Analysis prompt
     * @returns {Promise<string>} Analysis results
     */
    async analyzeImageFromUrl(imageUrl, prompt) {
        return await this.withRateLimit(async () => {
            try {
                // Download image from URL
                const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
                const imageBuffer = Buffer.from(response.data);
                const imageBase64 = imageBuffer.toString('base64');

                const result = await this.model.generateContent([
                    prompt,
                    {
                        inlineData: {
                            data: imageBase64,
                            mimeType: response.headers['content-type'] || 'image/jpeg'
                        }
                    }
                ]);

                return result.response.text();
            } catch (error) {
                console.error('Error analyzing image from URL with Gemini:', error);
                throw error;
            }
        });
    }

    /**
     * Compare two images using Gemini
     * @param {string} image1Base64 First base64 encoded image
     * @param {string} image2Base64 Second base64 encoded image
     * @returns {Promise<string>} Comparison results
     */
    async compareImages(image1Base64, image2Base64) {
        return await this.withRateLimit(async () => {
            try {
                const result = await this.model.generateContent([
                    'Compare these two images and describe their differences:',
                    {
                        inlineData: {
                            data: image1Base64,
                            mimeType: 'image/jpeg'
                        }
                    },
                    {
                        inlineData: {
                            data: image2Base64,
                            mimeType: 'image/jpeg'
                        }
                    }
                ]);

                return result.response.text();
            } catch (error) {
                console.error('Error comparing images with Gemini:', error);
                throw error;
            }
        });
    }

    /**
     * Compare two images from URLs using Gemini
     * @param {string} image1Url URL of the first image (Cloudinary URL)
     * @param {string} image2Url URL of the second image (Cloudinary URL)
     * @param {string} prompt Optional custom comparison prompt
     * @returns {Promise<string>} Comparison results
     */
    async compareImagesFromUrls(image1Url, image2Url, prompt = 'Compare these two images and describe their differences:') {
        return await this.withRateLimit(async () => {
            try {
                // Download both images
                const [response1, response2] = await Promise.all([
                    axios.get(image1Url, { responseType: 'arraybuffer' }),
                    axios.get(image2Url, { responseType: 'arraybuffer' })
                ]);

                const image1Base64 = Buffer.from(response1.data).toString('base64');
                const image2Base64 = Buffer.from(response2.data).toString('base64');

                const result = await this.model.generateContent([
                    prompt,
                    {
                        inlineData: {
                            data: image1Base64,
                            mimeType: response1.headers['content-type'] || 'image/jpeg'
                        }
                    },
                    {
                        inlineData: {
                            data: image2Base64,
                            mimeType: response2.headers['content-type'] || 'image/jpeg'
                        }
                    }
                ]);

                return result.response.text();
            } catch (error) {
                console.error('Error comparing images from URLs with Gemini:', error);
                throw error;
            }
        });
    }

    /**
     * Analyze content using Gemini
     * @param {Object} content Content to analyze
     * @param {string} prompt Analysis prompt
     * @returns {Promise<string>} Analysis results
     */
    async analyzeContent(content, prompt) {
        return await this.withRateLimit(async () => {
            try {
                const model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
                const result = await model.generateContent([
                    prompt,
                    JSON.stringify(content, null, 2)
                ]);

                return result.response.text();
            } catch (error) {
                console.error('Error analyzing content with Gemini:', error);
                throw error;
            }
        });
    }

    /**
     * Analyze performance data using Gemini
     * @param {Object} data Performance data to analyze
     * @param {string} prompt Analysis prompt
     * @returns {Promise<string>} Analysis results
     */
    async analyzePerformance(data, prompt) {
        return await this.withRateLimit(async () => {
            try {
                const model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
                const result = await model.generateContent([
                    prompt,
                    JSON.stringify(data, null, 2)
                ]);

                return result.response.text();
            } catch (error) {
                console.error('Error analyzing performance with Gemini:', error);
                throw error;
            }
        });
    }

    /**
     * Analyze error data using Gemini
     * @param {Object} data Error data to analyze
     * @param {string} prompt Analysis prompt
     * @returns {Promise<string>} Analysis results
     */
    async analyzeError(data, prompt) {
        return await this.withRateLimit(async () => {
            try {
                const model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
                const result = await model.generateContent([
                    prompt,
                    JSON.stringify(data, null, 2)
                ]);

                return result.response.text();
            } catch (error) {
                console.error('Error analyzing error with Gemini:', error);
                throw error;
            }
        });
    }

    async withRateLimit(fn) {
        await this.rateLimiter.acquire();
        try {
            return await fn();
        } finally {
            this.rateLimiter.release();
        }
    }

    async loadImageFromPath(imagePath) {
        try {
            const imageBuffer = await fs.readFile(imagePath);
            return {
                inlineData: {
                    data: imageBuffer.toString('base64'),
                    mimeType: 'image/png'
                }
            };
        } catch (error) {
            console.error('Error loading image:', error);
            throw error;
        }
    }

    /**
     * Save analysis result to file
     * @param {Object} result Analysis result
     * @param {Object} testInfo Test information
     * @returns {Promise<string>} Path to saved file
     */
    async saveAnalysisResult(result, testInfo) {
        const resultPath = path.join(
            'test-results',
            'gemini',
            `${testInfo.title}-analysis.json`
        );
        await fs.mkdir(path.dirname(resultPath), { recursive: true });
        await fs.writeFile(resultPath, JSON.stringify(result, null, 2));
        return resultPath;
    }
}

module.exports = { GeminiService }; 