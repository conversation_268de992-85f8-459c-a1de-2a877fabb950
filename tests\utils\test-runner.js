/**
 * @fileoverview Test runner with integrated Gemini AI visual analysis
 */

const { GeminiAnalysisHelper } = require('./gemini-analysis-helper');
const path = require('path');
const fs = require('fs').promises;

class TestRunner {
    constructor(config = {}) {
        this.gemini = new GeminiAnalysisHelper(process.env.GEMINI_API_KEY);
        this.config = {
            screenshotsDir: path.join(process.cwd(), 'test-results', 'ai-analysis'),
            analysisDir: path.join(process.cwd(), 'test-results', 'gemini-analysis'),
            ...config
        };
    }

    /**
     * Runs visual analysis on test results
     * @param {string} testName - Name of the test to analyze
     */
    async analyzeTestResults(testName) {
        console.log(`Starting Gemini analysis for test: ${testName}`);

        const testDir = path.join(this.config.screenshotsDir, testName);
        const sessions = await fs.readdir(testDir);

        for (const session of sessions) {
            const sessionDir = path.join(testDir, session);
            const files = await fs.readdir(sessionDir);
            
            // Group screenshots by interaction
            const screenshots = files
                .filter(f => f.endsWith('.png'))
                .map(f => ({
                    path: path.join(sessionDir, f),
                    metadata: require(path.join(sessionDir, f.replace('.png', '.json')))
                }));

            // Analyze individual screenshots
            for (const screenshot of screenshots) {
                const analysis = await this.gemini.analyzeScreenshot(
                    screenshot.path,
                    screenshot.metadata
                );
                await this.gemini.saveAnalysis(analysis, testName, 'single');
            }

            // Analyze before/after pairs
            const pairs = this.groupBeforeAfterPairs(screenshots);
            for (const [before, after] of pairs) {
                const comparison = await this.gemini.compareScreenshots(
                    before.path,
                    after.path,
                    before.metadata
                );
                await this.gemini.saveAnalysis(comparison, testName, 'comparison');
            }

            // Analyze responsive states
            const responsiveScreenshots = screenshots
                .filter(s => s.metadata.screenshotName.startsWith('responsive_'));
            if (responsiveScreenshots.length > 0) {
                const responsiveAnalysis = await this.gemini.analyzeResponsiveness(
                    responsiveScreenshots.map(s => s.path),
                    {
                        ...responsiveScreenshots[0].metadata,
                        viewports: responsiveScreenshots.map(s => ({
                            width: s.metadata.deviceInfo.width,
                            height: s.metadata.deviceInfo.height
                        }))
                    }
                );
                await this.gemini.saveAnalysis(responsiveAnalysis, testName, 'responsive');
            }

            // Analyze interaction sequences
            const sequences = this.groupInteractionSequences(screenshots);
            for (const sequence of sequences) {
                const sequenceAnalysis = await this.gemini.analyzeInteractionSequence(
                    sequence,
                    sequence[0].metadata
                );
                await this.gemini.saveAnalysis(sequenceAnalysis, testName, 'sequence');
            }
        }

        console.log(`Completed Gemini analysis for test: ${testName}`);
    }

    /**
     * Groups screenshots into before/after pairs
     * @private
     */
    groupBeforeAfterPairs(screenshots) {
        const pairs = [];
        const sorted = [...screenshots].sort((a, b) => 
            a.metadata.timestamp.localeCompare(b.metadata.timestamp)
        );

        for (let i = 0; i < sorted.length - 1; i++) {
            const current = sorted[i];
            const next = sorted[i + 1];

            if (current.metadata.screenshotName.includes('before') &&
                next.metadata.screenshotName.includes('after')) {
                pairs.push([current, next]);
            }
        }

        return pairs;
    }

    /**
     * Groups screenshots into interaction sequences
     * @private
     */
    groupInteractionSequences(screenshots) {
        const sequences = [];
        const interactionGroups = new Map();

        for (const screenshot of screenshots) {
            const interactionName = screenshot.metadata.screenshotName.split('_')[0];
            if (!interactionGroups.has(interactionName)) {
                interactionGroups.set(interactionName, []);
            }
            interactionGroups.get(interactionName).push(screenshot);
        }

        for (const [_, group] of interactionGroups) {
            if (group.length > 1) {
                sequences.push(
                    group.sort((a, b) => 
                        a.metadata.timestamp.localeCompare(b.metadata.timestamp)
                    )
                );
            }
        }

        return sequences;
    }

    /**
     * Generates a summary report of all analyses
     */
    async generateSummaryReport(testName) {
        const analysisDir = path.join(this.config.analysisDir, testName);
        const files = await fs.readdir(analysisDir);
        
        const summary = {
            testName,
            timestamp: new Date().toISOString(),
            analyses: {
                single: [],
                comparison: [],
                responsive: [],
                sequence: []
            }
        };

        for (const file of files) {
            const analysis = require(path.join(analysisDir, file));
            const type = file.split('_')[0];
            summary.analyses[type].push(analysis);
        }

        await fs.writeFile(
            path.join(analysisDir, 'summary.json'),
            JSON.stringify(summary, null, 2)
        );
    }
}

module.exports = { TestRunner }; 