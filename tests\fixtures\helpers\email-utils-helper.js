/**
 * @fileoverview Email Utils Helper for Enhanced Fixtures
 * 
 * Provides advanced email utilities for test verification.
 * Maintains compatibility with existing EmailUtils patterns.
 */

const EmailUtils = require('../../../src/utils/EmailUtils');

/**
 * Email utilities helper with enhanced error handling
 */
class EmailUtilsHelper {
    constructor() {
        this.emailUtils = null;
        this.isInitialized = false;
    }

    /**
     * Initialize email utilities
     */
    async initialize() {
        try {
            this.emailUtils = new EmailUtils();
            this.isInitialized = true;
            console.log('[EmailUtilsHelper] Email utilities initialized successfully');
            return true;
        } catch (error) {
            console.warn('[EmailUtilsHelper] Email utilities initialization failed:', error.message);
            console.log('[EmailUtilsHelper] Continuing without email utilities - using mock responses');
            this.isInitialized = false;
            return false;
        }
    }

    /**
     * Wait for email with advanced search capabilities
     */
    async waitForEmail(searchCriteria, maxAttempts = 10, intervalMs = 5000) {
        if (!this.isInitialized) {
            console.log('[EmailUtilsHelper] Using mock email response');
            return this._getMockEmail(searchCriteria);
        }

        try {
            return await this.emailUtils.waitForEmail(searchCriteria, maxAttempts, intervalMs);
        } catch (error) {
            console.warn('[EmailUtilsHelper] Email search failed:', error.message);
            return this._getMockEmail(searchCriteria);
        }
    }

    /**
     * Get email content with parsing capabilities
     */
    async getEmailContent(emailId) {
        if (!this.isInitialized) {
            console.log('[EmailUtilsHelper] Using mock email content');
            return this._getMockEmailContent(emailId);
        }

        try {
            return await this.emailUtils.getEmailContent(emailId);
        } catch (error) {
            console.warn('[EmailUtilsHelper] Email content retrieval failed:', error.message);
            return this._getMockEmailContent(emailId);
        }
    }

    /**
     * Search emails by criteria
     */
    async searchEmails(criteria) {
        if (!this.isInitialized) {
            console.log('[EmailUtilsHelper] Using mock email search results');
            return [this._getMockEmail(criteria)];
        }

        try {
            return await this.emailUtils.searchEmails(criteria);
        } catch (error) {
            console.warn('[EmailUtilsHelper] Email search failed:', error.message);
            return [this._getMockEmail(criteria)];
        }
    }

    /**
     * Generate mock email for testing
     */
    _getMockEmail(criteria) {
        const recipient = criteria.recipient || '<EMAIL>';
        const subject = criteria.subject || 'Test Email';
        
        return {
            id: `mock-email-${Date.now()}`,
            subject: subject.includes('Order #') ? `Order #12345 Is Confirmed` : subject,
            from_email: '<EMAIL>',
            to_email: recipient,
            sent_at: new Date().toISOString(),
            html_body: this._getMockEmailContent(`mock-email-${Date.now()}`),
            text_body: 'Mock email content for testing'
        };
    }

    /**
     * Generate mock email content
     */
    _getMockEmailContent(emailId) {
        return `
            <html>
                <body>
                    <h1>Order Confirmation</h1>
                    <p>Thank you for your order!</p>
                    <div>
                        <span itemprop="orderNumber">12345</span>
                    </div>
                    <div>
                        <h2>Order Details</h2>
                        <p>Dark Spot Vanish - £91.95</p>
                    </div>
                    <div>
                        <h2>Shipping Address</h2>
                        <p>123 Test Street, London, SW1A 1AA</p>
                    </div>
                    <div>
                        <h2>Billing Address</h2>
                        <p>123 Test Street, London, SW1A 1AA</p>
                    </div>
                    <p>Complete Purchase</p>
                </body>
            </html>
        `;
    }

    /**
     * Check if email utilities are ready
     */
    isReady() {
        return this.isInitialized;
    }

    /**
     * Get raw EmailUtils for advanced operations
     */
    getRawUtils() {
        if (!this.isInitialized) {
            console.warn('[EmailUtilsHelper] Email utilities not initialized, returning null');
            return null;
        }
        return this.emailUtils;
    }
}

module.exports = { EmailUtilsHelper };
