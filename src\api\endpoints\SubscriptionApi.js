const ApiClient = require('../ApiClient');

class SubscriptionApi {
    constructor() {
        this.client = new ApiClient({ useAuth: true });
    }

    // Based on actual app command:
    // app:reorder DSS --last-ordered-before=-1second -e prod -v
    async triggerR<PERSON>wal(brand, lastOrderedBefore = '-1second') {
        return this.client.post('/api/v2/admin/subscription/reorder', {
            brand,
            lastOrderedBefore
        });
    }

    async getSubscriptionByOrderNumber(orderNumber) {
        return this.client.get(`/api/v2/admin/subscriptions`, {
            orderNumber
        });
    }

    async updateSubscriptionFrequency(orderNumber, frequencyInDays) {
        return this.client.put(`/api/v2/admin/subscriptions/${orderNumber}/frequency`, {
            frequencyInDays
        });
    }
}

module.exports = SubscriptionApi;