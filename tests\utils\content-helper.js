/**
 * @fileoverview Helper functions for content comparison between sites
 */
class ContentHelper {
    /**
     * Normalize text for comparison
     * @param {string} text - Text to normalize
     * @param {Object} options - Normalization options
     * @returns {string} - Normalized text
     */
    static normalizeText(text, options = {}) {
        let normalized = text || '';

        if (options.removeNewlines) {
            normalized = normalized.replace(/\r?\n/g, ' ');
        }
        if (options.removeExtraSpaces) {
            normalized = normalized.replace(/\s+/g, ' ').trim();
        }
        if (!options.caseSensitive) {
            normalized = normalized.toLowerCase();
        }
        if (options.ignorePunctuation) {
            normalized = normalized.replace(/[.,/#!$%^&*;:{}=\-_`~()]/g, '');
        }

        return normalized;
    }

    /**
     * Compare two texts with normalization
     * @param {string} text1 - First text
     * @param {string} text2 - Second text
     * @param {Object} options - Comparison options
     * @returns {Object} - Comparison result
     */
    static compareTexts(text1, text2, options = {}) {
        const normalized1 = this.normalizeText(text1, options);
        const normalized2 = this.normalizeText(text2, options);

        const identical = normalized1 === normalized2;
        const differences = [];

        if (!identical) {
            const words1 = normalized1.split(' ');
            const words2 = normalized2.split(' ');

            const missingWords = words1.filter(word => !words2.includes(word));
            const extraWords = words2.filter(word => !words1.includes(word));

            if (missingWords.length > 0) {
                differences.push({
                    type: 'missing',
                    words: missingWords
                });
            }
            if (extraWords.length > 0) {
                differences.push({
                    type: 'extra',
                    words: extraWords
                });
            }
        }

        return {
            identical,
            differences,
            normalizedText1: normalized1,
            normalizedText2: normalized2
        };
    }

    /**
     * Test mobile gallery swipe functionality
     * @param {Page} baselinePage - Baseline page
     * @param {Page} shopifyPage - Shopify page
     * @returns {Promise<Object>} - Swipe test results
     */
    static async testMobileGallerySwipe(baselinePage, shopifyPage) {
        const performSwipe = async (page, selector) => {
            const gallery = await page.$(selector);
            if (!gallery) {
                return { success: false, error: 'Gallery not found' };
            }

            const box = await gallery.boundingBox();
            if (!box) {
                return { success: false, error: 'Could not get gallery bounds' };
            }

            // Record initial state
            const initialState = await page.evaluate(sel => {
                const element = document.querySelector(sel);
                return {
                    scrollLeft: element.scrollLeft,
                    scrollWidth: element.scrollWidth
                };
            }, selector);

            // Perform swipe gesture
            try {
                await page.mouse.move(box.x + box.width - 20, box.y + box.height / 2);
                await page.mouse.down();
                await page.mouse.move(box.x + 20, box.y + box.height / 2, { steps: 10 });
                await page.mouse.up();
                await page.waitForTimeout(500); // Wait for animation

                // Check final state
                const finalState = await page.evaluate(sel => {
                    const element = document.querySelector(sel);
                    return {
                        scrollLeft: element.scrollLeft,
                        scrollWidth: element.scrollWidth
                    };
                }, selector);

                return {
                    success: true,
                    scrolled: initialState.scrollLeft !== finalState.scrollLeft,
                    initialState,
                    finalState
                };
            } catch (error) {
                return { success: false, error: error.message };
            }
        };

        const baselineSwipe = await performSwipe(baselinePage, '.mobile-product-gallery');
        const shopifySwipe = await performSwipe(shopifyPage, '.product-gallery--mobile');

        return {
            baselineSwipe,
            shopifySwipe,
            behaviorMatches: baselineSwipe.scrolled === shopifySwipe.scrolled
        };
    }

    /**
     * Test sticky header behavior
     * @param {Page} baselinePage - Baseline page
     * @param {Page} shopifyPage - Shopify page
     * @returns {Promise<Object>} - Sticky header test results
     */
    static async testStickyHeaderBehavior(baselinePage, shopifyPage) {
        const testStickyness = async (page, headerSelector) => {
            // Get initial position
            const initialPosition = await page.$eval(headerSelector, el => {
                const rect = el.getBoundingClientRect();
                return {
                    top: rect.top,
                    position: window.getComputedStyle(el).position
                };
            });

            // Scroll down
            await page.evaluate(() => window.scrollBy(0, 200));
            await page.waitForTimeout(100);

            // Get scrolled position
            const scrolledPosition = await page.$eval(headerSelector, el => {
                const rect = el.getBoundingClientRect();
                return {
                    top: rect.top,
                    position: window.getComputedStyle(el).position
                };
            });

            // Scroll back up
            await page.evaluate(() => window.scrollTo(0, 0));
            await page.waitForTimeout(100);

            // Get final position
            const finalPosition = await page.$eval(headerSelector, el => {
                const rect = el.getBoundingClientRect();
                return {
                    top: rect.top,
                    position: window.getComputedStyle(el).position
                };
            });

            return {
                initialPosition,
                scrolledPosition,
                finalPosition,
                isSticky: scrolledPosition.position === 'fixed' || scrolledPosition.position === 'sticky'
            };
        };

        const baselineHeader = await testStickyness(baselinePage, '.header');
        const shopifyHeader = await testStickyness(shopifyPage, '.site-header');

        return {
            baselineHeader,
            shopifyHeader,
            behaviorMatches: baselineHeader.isSticky === shopifyHeader.isSticky
        };
    }

    /**
     * Compare mobile layouts between sites
     * @param {Page} baselinePage - Baseline page
     * @param {Page} shopifyPage - Shopify page
     * @returns {Promise<Object>} - Layout comparison results
     */
    static async compareMobileLayouts(baselinePage, shopifyPage) {
        const getLayoutMetrics = async (page) => {
            return await page.evaluate(() => {
                const getElementMetrics = (selector) => {
                    const element = document.querySelector(selector);
                    if (!element) return null;

                    const rect = element.getBoundingClientRect();
                    const styles = window.getComputedStyle(element);

                    return {
                        top: rect.top,
                        left: rect.left,
                        width: rect.width,
                        height: rect.height,
                        fontSize: styles.fontSize,
                        lineHeight: styles.lineHeight,
                        padding: {
                            top: styles.paddingTop,
                            right: styles.paddingRight,
                            bottom: styles.paddingBottom,
                            left: styles.paddingLeft
                        }
                    };
                };

                return {
                    header: getElementMetrics('header'),
                    productTitle: getElementMetrics('h1'),
                    productGallery: getElementMetrics('.product-gallery'),
                    addToCart: getElementMetrics('.add-to-cart-button'),
                    optionSelectors: getElementMetrics('.product-options'),
                    priceDisplay: getElementMetrics('.product-price'),
                    viewport: {
                        width: window.innerWidth,
                        height: window.innerHeight
                    }
                };
            });
        };

        const baselineMetrics = await getLayoutMetrics(baselinePage);
        const shopifyMetrics = await getLayoutMetrics(shopifyPage);

        // Compare key dimensions with tolerance
        const tolerance = 5; // 5px tolerance for minor differences
        const dimensionsMatch = (dim1, dim2) => Math.abs(dim1 - dim2) <= tolerance;

        const comparisons = {
            header: {
                heightMatches: dimensionsMatch(
                    baselineMetrics.header.height,
                    shopifyMetrics.header.height
                ),
                positionMatches: dimensionsMatch(
                    baselineMetrics.header.top,
                    shopifyMetrics.header.top
                )
            },
            productGallery: {
                heightMatches: dimensionsMatch(
                    baselineMetrics.productGallery.height,
                    shopifyMetrics.productGallery.height
                ),
                widthMatches: dimensionsMatch(
                    baselineMetrics.productGallery.width,
                    shopifyMetrics.productGallery.width
                )
            },
            addToCart: {
                heightMatches: dimensionsMatch(
                    baselineMetrics.addToCart.height,
                    shopifyMetrics.addToCart.height
                ),
                positionMatches: dimensionsMatch(
                    baselineMetrics.addToCart.top,
                    shopifyMetrics.addToCart.top
                )
            },
            typography: {
                titleMatches: baselineMetrics.productTitle.fontSize === shopifyMetrics.productTitle.fontSize,
                priceMatches: baselineMetrics.priceDisplay.fontSize === shopifyMetrics.priceDisplay.fontSize
            }
        };

        // Test touch interactions
        const touchInteractions = await this.testTouchInteractions(baselinePage, shopifyPage);

        return {
            metrics: {
                baseline: baselineMetrics,
                shopify: shopifyMetrics
            },
            comparisons,
            touchInteractions,
            overallMatch: Object.values(comparisons).every(
                category => Object.values(category).every(match => match)
            )
        };
    }

    /**
     * Test touch interactions
     * @param {Page} baselinePage - Baseline page
     * @param {Page} shopifyPage - Shopify page
     * @returns {Promise<Object>} - Touch interaction test results
     */
    static async testTouchInteractions(baselinePage, shopifyPage) {
        const testTouch = async (page, selector) => {
            const element = await page.$(selector);
            if (!element) return { success: false, error: 'Element not found' };

            try {
                // Test tap
                await element.tap();
                await page.waitForTimeout(100);

                // Test double tap
                await element.tap({ delay: 100 });
                await page.waitForTimeout(100);

                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        };

        const touchTargets = [
            { name: 'gallery', selector: '.product-gallery' },
            { name: 'addToCart', selector: '.add-to-cart-button' },
            { name: 'options', selector: '.product-options' }
        ];

        const results = {};
        for (const target of touchTargets) {
            results[target.name] = {
                baseline: await testTouch(baselinePage, target.selector),
                shopify: await testTouch(shopifyPage, target.selector)
            };
        }

        return results;
    }

    /**
     * Compare structured data
     * @param {Array} baselineData - Baseline structured data
     * @param {Array} shopifyData - Shopify structured data
     * @returns {Object} - Structured data comparison results
     */
    static compareStructuredData(baselineData, shopifyData) {
        const extractEssentialProps = (data) => {
            return data.map(item => ({
                '@type': item['@type'],
                name: this.normalizeText(item.name, { removeExtraSpaces: true }),
                description: this.normalizeText(item.description, { removeExtraSpaces: true }),
                price: item.offers?.price,
                sku: item.sku
            }));
        };

        const baselineProps = extractEssentialProps(baselineData);
        const shopifyProps = extractEssentialProps(shopifyData);

        const compareProps = (prop1, prop2) => {
            return Object.keys(prop1).every(key => {
                if (key === 'price') {
                    return Math.abs(prop1[key] - prop2[key]) < 0.01;
                }
                return prop1[key] === prop2[key];
            });
        };

        const matches = baselineProps.length === shopifyProps.length &&
            baselineProps.every((prop, index) => compareProps(prop, shopifyProps[index]));

        return {
            matches,
            baselineProps,
            shopifyProps
        };
    }
}

module.exports = { ContentHelper };