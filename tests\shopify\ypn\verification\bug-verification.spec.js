/**
 * @fileoverview Bug verification tests for YourPetNutrition Shopify site
 */

const { test, expect } = require('@playwright/test');
const { BrowserStackHelper } = require('../../../../src/utils/browserstack/browserstack-helper');

test.describe('YourPetNutrition Bug Verification Tests @shopify @ypn', () => {
    let page;
    let bsHelper;
    const shopUrl = 'https://yourpetnutrition.myshopify.com/';
    const shopPassword = '12345';

    test.beforeEach(async ({ browser }, testInfo) => {
        // Create a new context and page for each test
        const context = await browser.newContext();
        page = await context.newPage();
        
        // Initialize BrowserStack helper
        bsHelper = new BrowserStackHelper();
        
        // Navigate to the password-protected store
        await page.goto(shopUrl);
        await page.waitForLoadState('networkidle');
        
        // Enter store password
        await page.fill('input[type="password"]', shopPassword);
        await page.click('button:has-text("Enter")');
        
        // Wait for the store to load
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Take a screenshot of the homepage
        await bsHelper.takeScreenshot(page, 'ypn-homepage', {
            testName: testInfo.title,
            fullPage: true,
            type: 'initial'
        });
    });

    test.afterEach(async ({ }, testInfo) => {
        if (process.env.BROWSERSTACK_SESSION_ID) {
            await bsHelper.downloadArtifacts(process.env.BROWSERSTACK_SESSION_ID);
        }
    });

    test('Bug #1: Canine Prime Add to Cart Functionality', async ({ }, testInfo) => {
        // Navigate to Shop Dogs page
        await page.click('text=Shop Dogs');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Take a screenshot of the Shop Dogs page
        await bsHelper.takeScreenshot(page, 'ypn-shop-dogs', {
            testName: testInfo.title,
            fullPage: true,
            type: 'navigation'
        });
        
        // Click on Canine Prime product
        await page.click('text=Canine Prime');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Take a screenshot of the Canine Prime product page
        await bsHelper.takeScreenshot(page, 'ypn-canine-prime-product', {
            testName: testInfo.title,
            fullPage: true,
            type: 'product'
        });
        
        // Verify product title
        const productTitle = await page.textContent('h1');
        expect(productTitle).toBe('Canine Prime');
        
        // Verify purchase options are displayed
        const purchaseOptions = await page.locator('text=Purchase Options').isVisible();
        expect(purchaseOptions).toBeTruthy();
        
        // Click Add to Cart button
        await page.click('button:has-text("Add to cart")');
        
        // Wait for potential cart update
        await page.waitForTimeout(2000);
        
        // Take a screenshot after clicking Add to Cart
        await bsHelper.takeScreenshot(page, 'ypn-canine-prime-add-to-cart', {
            testName: testInfo.title,
            fullPage: false,
            type: 'action'
        });
        
        // Click on cart icon to check if product was added
        await page.click('.header__icon--cart');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Take a screenshot of the cart
        await bsHelper.takeScreenshot(page, 'ypn-cart-after-canine-prime', {
            testName: testInfo.title,
            fullPage: true,
            type: 'cart'
        });
        
        // Check if cart contains Canine Prime
        const cartContainsProduct = await page.locator('text=Canine Prime').isVisible();
        
        // Log the result for reporting
        if (cartContainsProduct) {
            console.log('Bug #1 FIXED: Canine Prime was successfully added to cart');
        } else {
            console.log('Bug #1 STILL PRESENT: Canine Prime was not added to cart');
        }
        
        // This test is documenting the bug, so we don't assert the expected behavior
        // Instead, we're logging the current state for verification
    });

    test('Bug #2: Denta Soft Quantity Selection Price Update', async ({ }, testInfo) => {
        // Navigate to Shop Dogs page
        await page.click('text=Shop Dogs');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Click on Denta Soft product
        await page.click('text=Denta Soft');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Take a screenshot of the Denta Soft product page
        await bsHelper.takeScreenshot(page, 'ypn-denta-soft-product', {
            testName: testInfo.title,
            fullPage: true,
            type: 'product'
        });
        
        // Verify product title
        const productTitle = await page.textContent('h1');
        expect(productTitle).toBe('Denta Soft');
        
        // Get initial price
        const initialPrice = await page.textContent('.price');
        console.log(`Initial price: ${initialPrice}`);
        
        // Check if quantity selector exists
        const quantitySelector = await page.locator('text=Quantity').isVisible();
        expect(quantitySelector).toBeTruthy();
        
        // Select 3 jars option
        await page.click('text=3 jars');
        await page.waitForTimeout(1000);
        
        // Take a screenshot after selecting 3 jars
        await bsHelper.takeScreenshot(page, 'ypn-denta-soft-3-jars', {
            testName: testInfo.title,
            fullPage: false,
            type: 'quantity'
        });
        
        // Get price after selecting 3 jars
        const priceAfter3Jars = await page.textContent('.price');
        console.log(`Price after selecting 3 jars: ${priceAfter3Jars}`);
        
        // Select 6 jars option
        await page.click('text=6 jars');
        await page.waitForTimeout(1000);
        
        // Take a screenshot after selecting 6 jars
        await bsHelper.takeScreenshot(page, 'ypn-denta-soft-6-jars', {
            testName: testInfo.title,
            fullPage: false,
            type: 'quantity'
        });
        
        // Get price after selecting 6 jars
        const priceAfter6Jars = await page.textContent('.price');
        console.log(`Price after selecting 6 jars: ${priceAfter6Jars}`);
        
        // Check if prices are different
        const pricesUpdated = (initialPrice !== priceAfter3Jars) || (initialPrice !== priceAfter6Jars);
        
        // Log the result for reporting
        if (pricesUpdated) {
            console.log('Bug #2 FIXED: Price updates when selecting different quantities');
        } else {
            console.log('Bug #2 STILL PRESENT: Price does not update when selecting different quantities');
        }
        
        // This test is documenting the bug, so we don't assert the expected behavior
        // Instead, we're logging the current state for verification
    });

    test('Verify Add to Cart Functionality for Denta Soft', async ({ }, testInfo) => {
        // Navigate to Shop Dogs page
        await page.click('text=Shop Dogs');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Click on Denta Soft product
        await page.click('text=Denta Soft');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Select 1 jar option (default)
        await page.click('text=1 jar');
        
        // Click Add to Cart button
        await page.click('button:has-text("Add to cart")');
        
        // Wait for potential cart update
        await page.waitForTimeout(2000);
        
        // Take a screenshot after clicking Add to Cart
        await bsHelper.takeScreenshot(page, 'ypn-denta-soft-add-to-cart', {
            testName: testInfo.title,
            fullPage: false,
            type: 'action'
        });
        
        // Click on cart icon to check if product was added
        await page.click('.header__icon--cart');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Take a screenshot of the cart
        await bsHelper.takeScreenshot(page, 'ypn-cart-after-denta-soft', {
            testName: testInfo.title,
            fullPage: true,
            type: 'cart'
        });
        
        // Check if cart contains Denta Soft
        const cartContainsProduct = await page.locator('text=Denta Soft').isVisible();
        expect(cartContainsProduct).toBeTruthy();
        
        // Verify quantity can be updated in cart
        await page.fill('input[type="number"]', '2');
        await page.waitForTimeout(1000);
        
        // Take a screenshot after updating quantity
        await bsHelper.takeScreenshot(page, 'ypn-cart-quantity-update', {
            testName: testInfo.title,
            fullPage: true,
            type: 'cart-update'
        });
        
        // Verify quantity was updated
        const updatedQuantity = await page.inputValue('input[type="number"]');
        expect(updatedQuantity).toBe('2');
    });

    test('Verify Subscribe and Save Option', async ({ }, testInfo) => {
        // Navigate to Shop Dogs page
        await page.click('text=Shop Dogs');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Click on Denta Soft product
        await page.click('text=Denta Soft');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Get one-time purchase price
        const oneTimePriceElement = await page.locator('text=ONE TIME PURCHASE').locator('xpath=..').locator('.price');
        const oneTimePrice = await oneTimePriceElement.textContent();
        console.log(`One-time purchase price: ${oneTimePrice}`);
        
        // Select Subscribe and Save option
        await page.click('text=SUBSCRIBE AND SAVE');
        await page.waitForTimeout(1000);
        
        // Take a screenshot after selecting Subscribe and Save
        await bsHelper.takeScreenshot(page, 'ypn-subscribe-and-save', {
            testName: testInfo.title,
            fullPage: false,
            type: 'purchase-option'
        });
        
        // Get subscribe and save price
        const subscribePriceElement = await page.locator('text=SUBSCRIBE AND SAVE').locator('xpath=..').locator('.price');
        const subscribePrice = await subscribePriceElement.textContent();
        console.log(`Subscribe and save price: ${subscribePrice}`);
        
        // Verify subscribe price is less than one-time price
        const oneTimePriceValue = parseFloat(oneTimePrice.replace(/[^0-9.]/g, ''));
        const subscribePriceValue = parseFloat(subscribePrice.replace(/[^0-9.]/g, ''));
        expect(subscribePriceValue).toBeLessThan(oneTimePriceValue);
        
        // Verify 10% discount is applied
        const expectedDiscountedPrice = oneTimePriceValue * 0.9;
        expect(subscribePriceValue).toBeCloseTo(expectedDiscountedPrice, 1);
    });
});
