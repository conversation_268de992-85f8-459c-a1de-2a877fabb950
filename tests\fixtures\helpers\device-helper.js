/**
 * @fileoverview Device Helper
 *
 * Centralized helper for device and platform management that eliminates
 * repetitive mobile viewport setup and device detection code.
 * 
 * Features:
 * - Standardized mobile viewport configuration
 * - Device type detection and handling
 * - BrowserStack real device vs emulation handling
 * - Platform-specific optimizations
 */

/**
 * Device Helper for managing device-specific configurations
 */
class DeviceHelper {
    constructor() {
        this.platform = process.env.PLATFORM || '';
        this.isBrowserStackSdk = process.env.BROWSERSTACK_SDK_ENABLED === 'true';
        this.isRealDevice = process.env.BROWSERSTACK_REAL_DEVICE === 'true';
        
        console.log(`[DeviceHelper] Initialized - Platform: ${this.platform}, SDK: ${this.isBrowserStackSdk}, Real Device: ${this.isRealDevice}`);
    }

    /**
     * Detect if current platform is mobile
     * @returns {boolean} True if mobile platform
     */
    isMobile() {
        return this.platform.includes('galaxy') ||
               this.platform.includes('iphone') ||
               this.platform.includes('android') ||
               this.platform.includes('ios') ||
               process.env.IS_MOBILE === 'true';
    }

    /**
     * Get device type from platform
     * @returns {string} Device type (desktop, mobile, tablet)
     */
    getDeviceType() {
        if (this.platform.includes('ipad') || this.platform.includes('tablet')) {
            return 'tablet';
        } else if (this.isMobile()) {
            return 'mobile';
        } else {
            return 'desktop';
        }
    }

    /**
     * Get standardized viewport configuration for device type
     * @param {string} deviceType - Device type or platform identifier
     * @returns {Object} Viewport configuration
     */
    getViewportConfig(deviceType = null) {
        const type = deviceType || this.getDeviceType();
        
        const viewports = {
            // Mobile devices
            'mobile': { width: 375, height: 812 },
            'iphone': { width: 375, height: 812 },
            'iphone14': { width: 390, height: 844 },
            'iphone-14': { width: 390, height: 844 },
            'galaxy': { width: 360, height: 740 },
            'samsung-galaxy-s23': { width: 360, height: 780 },
            'android': { width: 360, height: 740 },
            
            // Tablets
            'tablet': { width: 768, height: 1024 },
            'ipad': { width: 768, height: 1024 },
            
            // Desktop (fallback)
            'desktop': { width: 1280, height: 720 },
            'default': { width: 375, height: 812 }
        };
        
        // Try exact platform match first
        if (viewports[this.platform]) {
            return viewports[this.platform];
        }
        
        // Try device type match
        if (viewports[type]) {
            return viewports[type];
        }
        
        // Fallback to default mobile viewport
        return viewports.default;
    }

    /**
     * Setup mobile viewport if needed
     * Respects BrowserStack SDK mode and real device settings
     * @param {Object} page - Playwright page object
     * @param {string} deviceType - Optional device type override
     */
    async setupMobileViewportIfNeeded(page, deviceType = null) {
        // Skip viewport setup for real devices or SDK mode
        if (this.isRealDevice || this.isBrowserStackSdk) {
            console.log('[DeviceHelper] Skipping viewport setup - real device or SDK mode');
            return;
        }

        // Only setup viewport for mobile devices
        if (!this.isMobile() && !deviceType) {
            console.log('[DeviceHelper] Desktop platform detected, skipping mobile viewport setup');
            return;
        }

        const viewport = this.getViewportConfig(deviceType);
        
        try {
            await page.setViewportSize(viewport);
            console.log(`[DeviceHelper] Set viewport to ${viewport.width}x${viewport.height} for ${deviceType || this.platform}`);
        } catch (error) {
            console.warn(`[DeviceHelper] Failed to set viewport: ${error.message}`);
        }
    }

    /**
     * Setup mobile viewport with specific device type
     * @param {Object} page - Playwright page object
     * @param {string} deviceType - Device type (iphone, galaxy, tablet, etc.)
     */
    async setupMobileViewport(page, deviceType = 'default') {
        await this.setupMobileViewportIfNeeded(page, deviceType);
    }

    /**
     * Get platform-specific timeout multiplier
     * @returns {number} Timeout multiplier
     */
    getTimeoutMultiplier() {
        if (this.isMobile()) {
            return 2.5; // 2.5x longer for mobile
        } else if (this.platform.includes('safari')) {
            return 1.5; // 1.5x longer for Safari
        } else {
            return 1.0; // Normal timeout for desktop
        }
    }

    /**
     * Get recommended timeout for current platform
     * @param {number} baseTimeout - Base timeout in milliseconds
     * @returns {number} Recommended timeout
     */
    getRecommendedTimeout(baseTimeout = 30000) {
        return Math.round(baseTimeout * this.getTimeoutMultiplier());
    }

    /**
     * Check if platform supports specific features
     * @param {string} feature - Feature to check
     * @returns {boolean} True if supported
     */
    supportsFeature(feature) {
        const featureSupport = {
            'touch': this.isMobile(),
            'hover': !this.isMobile(),
            'keyboard': !this.isMobile() || this.platform.includes('tablet'),
            'rightClick': !this.isMobile(),
            'dragAndDrop': !this.isMobile(),
            'fileUpload': !this.platform.includes('ios'), // iOS has restrictions
            'clipboard': !this.isMobile() || this.isBrowserStackSdk
        };
        
        return featureSupport[feature] || false;
    }

    /**
     * Get device-specific interaction options
     * @returns {Object} Interaction options
     */
    getInteractionOptions() {
        const options = {
            clickDelay: this.isMobile() ? 100 : 0,
            typeDelay: this.isMobile() ? 50 : 0,
            scrollBehavior: this.isMobile() ? 'smooth' : 'auto',
            waitForStability: this.isMobile() ? 2000 : 1000
        };
        
        return options;
    }

    /**
     * Get platform information for debugging
     * @returns {Object} Platform information
     */
    getPlatformInfo() {
        return {
            platform: this.platform,
            deviceType: this.getDeviceType(),
            isMobile: this.isMobile(),
            isBrowserStackSdk: this.isBrowserStackSdk,
            isRealDevice: this.isRealDevice,
            viewport: this.getViewportConfig(),
            timeoutMultiplier: this.getTimeoutMultiplier(),
            supportedFeatures: {
                touch: this.supportsFeature('touch'),
                hover: this.supportsFeature('hover'),
                keyboard: this.supportsFeature('keyboard'),
                rightClick: this.supportsFeature('rightClick'),
                dragAndDrop: this.supportsFeature('dragAndDrop'),
                fileUpload: this.supportsFeature('fileUpload'),
                clipboard: this.supportsFeature('clipboard')
            }
        };
    }

    /**
     * Wait for device-specific stability
     * @param {Object} page - Playwright page object
     * @param {Object} options - Wait options
     */
    async waitForDeviceStability(page, options = {}) {
        const defaultWait = this.isMobile() ? 2000 : 1000;
        const waitTime = options.waitTime || defaultWait;
        
        console.log(`[DeviceHelper] Waiting ${waitTime}ms for device stability`);
        
        try {
            // Wait for network idle
            await page.waitForLoadState('networkidle', { 
                timeout: this.getRecommendedTimeout(30000) 
            });
            
            // Additional wait for mobile devices
            if (this.isMobile()) {
                await page.waitForTimeout(waitTime);
            }
        } catch (error) {
            console.warn(`[DeviceHelper] Device stability wait failed: ${error.message}`);
        }
    }

    /**
     * Perform device-optimized click
     * @param {Object} page - Playwright page object
     * @param {string} selector - Element selector
     * @param {Object} options - Click options
     */
    async deviceOptimizedClick(page, selector, options = {}) {
        const interactionOptions = this.getInteractionOptions();
        
        const clickOptions = {
            delay: interactionOptions.clickDelay,
            timeout: this.getRecommendedTimeout(30000),
            ...options
        };
        
        if (this.isMobile()) {
            // For mobile, ensure element is in viewport first
            await page.locator(selector).scrollIntoViewIfNeeded();
            await page.waitForTimeout(500); // Brief pause for scroll completion
        }
        
        await page.locator(selector).click(clickOptions);
    }

    /**
     * Perform device-optimized typing
     * @param {Object} page - Playwright page object
     * @param {string} selector - Element selector
     * @param {string} text - Text to type
     * @param {Object} options - Type options
     */
    async deviceOptimizedType(page, selector, text, options = {}) {
        const interactionOptions = this.getInteractionOptions();
        
        const typeOptions = {
            delay: interactionOptions.typeDelay,
            timeout: this.getRecommendedTimeout(30000),
            ...options
        };
        
        if (this.isMobile()) {
            // For mobile, focus first and wait for keyboard
            await page.locator(selector).focus();
            await page.waitForTimeout(300);
        }
        
        await page.locator(selector).fill(text, typeOptions);
    }
}

module.exports = { DeviceHelper };
