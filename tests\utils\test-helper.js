const fs = require('fs');
const path = require('path');

class TestHelper {
    static async saveScreenshot(page, name) {
        const screenshotPath = path.join(__dirname, '../../screenshots');
        if (!fs.existsSync(screenshotPath)) {
            fs.mkdirSync(screenshotPath, { recursive: true });
        }
        await page.screenshot({ 
            path: path.join(screenshotPath, `${name}-${Date.now()}.png`),
            fullPage: true 
        });
    }

    static async logTestInfo(testInfo, message) {
        const logsPath = path.join(__dirname, '../../logs');
        if (!fs.existsSync(logsPath)) {
            fs.mkdirSync(logsPath, { recursive: true });
        }
        
        const logFile = path.join(logsPath, `test-run-${new Date().toISOString().split('T')[0]}.log`);
        const logMessage = `${new Date().toISOString()} - ${testInfo.title}: ${message}\n`;
        
        fs.appendFileSync(logFile, logMessage);
    }

    static formatErrorMessage(error, context) {
        return `Error in ${context}: ${error.message}\nStack: ${error.stack}`;
    }
}

module.exports = { TestHelper };