/**
 * @fileoverview Page object for product pages
 */
class ProductPage {
    /**
     * Initialize the page object
     * @param {import('@playwright/test').Page} page 
     */
    constructor(page) {
        this.page = page;
        this.selectors = {
            // Form and container selectors
            productForm: '#sylius-product-adding-to-cart',
            productTitle: '.main p.title',
            
            // Quantity selectors
            quantity: {
                container: '.choose-item.set-quantity',
                input: '#sylius_add_to_cart_cartItem_quantity',
                options: '[data-value]',
                active: '.choose-item.set-quantity.active',
                savings: (items) => `[data-value="${items}"] .savings`,
                price: (items) => `[data-value="${items}"] .price`
            },
            
            // Price selectors
            price: {
                container: '.product-price',
                current: '#product-price',
                amount: '.amount',
                savings: '.savings'
            },
            
            // Purchase type selectors
            purchaseType: {
                container: '#sylius-product-adding-to-cart',
                option: '.purchase-option',
                oneTimeText: 'One-Time Purchase',
                subscriptionText: 'Subscribe & Save',
                active: '.purchase-option.active'
            },
            
            // Variant selectors
            variant: {
                flavorContainer: '.flavor-options',
                flavorOption: (code) => `.flavor-option[data-flavor="${code}"]`,
                flavorName: '.flavor-name',
                flavorDescription: '.flavor-description',
                selected: '.flavor-option.active'
            },
            
            // Button selectors
            addToCart: 'button[type="submit"]',
            
            // Subscription selectors
            subscription: {
                container: '.product-subscription-frequency-wrapper',
                select: 'select.product-subscription-frequency',
                option: (frequency) => `option[value="${frequency}"]`,
                frequencyOptions: 'select.product-subscription-frequency option'
            },

            // Product information selectors
            restrictions: {
                container: '.product-restrictions',
                dietary: '.dietary-restrictions li',
                ageLimit: '.age-limit',
                medical: '.medical-restrictions li',
                warnings: '.warnings li'
            },

            // Trust badges
            trustBadges: {
                container: '.trust-badges',
                items: '.trust-badge',
                text: '.badge-text',
                icon: '.badge-icon'
            }
        };
    }

    /**
     * Navigate to product page
     * @param {string} productPath Product URL path
     */
    async navigateToProduct(productPath) {
        try {
            // Ensure path starts with /
            const path = productPath.startsWith('/') ? productPath : `/${productPath}`;
            
            // Get base URL from environment or use default
            const baseUrl = process.env.BASE_URL || 'https://aeonstest.info';
            const fullUrl = `${baseUrl}${path}`;
            
            console.log(`Navigating to product URL: ${fullUrl}`);
            
            // Navigate with extended timeout
            await this.page.goto(fullUrl, {
                timeout: 60000, // Extend timeout to 60 seconds
                waitUntil: 'domcontentloaded' // Less strict wait condition
            });
            
            console.log('Page navigation completed, waiting for content...');
            
            // Try to wait for the product form but don't fail if not found
            try {
                await this.page.waitForSelector(this.selectors.productForm, {
                    state: 'visible',
                    timeout: 10000
                });
                console.log('Product form found');
            } catch (formError) {
                console.log(`Product form not immediately visible: ${formError.message}`);
                // Continue anyway - the site might have different structure
            }
            
            // Wait a moment for any dynamic content
            await this.page.waitForTimeout(3000);
            
            // Just verify we're on a product page by URL pattern or content
            const currentUrl = this.page.url();
            const isProductUrl = currentUrl.includes('/products/');
            
            if (!isProductUrl) {
                // Try checking for product title as fallback
                const hasTitle = await this.page.locator('.main p.title, .product-title, h1').count() > 0;
                if (!hasTitle) {
                    throw new Error('Unable to verify we are on a product page');
                }
            }
            
            console.log('Successfully navigated to product page');
        } catch (error) {
            throw new Error(`Failed to navigate to product: ${error.message}`);
        }
    }

    /**
     * Select purchase type (one-time or subscription)
     * @param {string} purchaseType - The type of purchase ('oneTime' or 'subscription')
     */
    async selectPurchaseType(purchaseType) {
        console.log(`Attempting to select purchase type: ${purchaseType}`);
        
        try {
            // Determine the text to select based on purchase type
            const textToSelect = purchaseType === 'oneTime' 
                ? this.selectors.purchaseType.oneTimeText 
                : this.selectors.purchaseType.subscriptionText;
            
            // Find all purchase options and click the one that matches
            console.log(`Looking for purchase option with text: ${textToSelect}`);
            
            const options = await this.page.$$(this.selectors.purchaseType.option);
            console.log(`Found ${options.length} purchase options`);
            
            // Get currently selected flavor (if any)
            let selectedFlavor = null;
            try {
                // Try to find the selected flavor from the flavor options
                selectedFlavor = await this.getSelectedFlavor();
                console.log(`Current selected flavor: ${selectedFlavor}`);
            } catch (err) {
                console.log('No flavor selection found, proceeding with default');
            }
            
            // Build a more specific selector based on purchase type and flavor
            let selector = `${this.selectors.purchaseType.option}:has-text("${textToSelect}")`;
            
            // If we have a flavor, make selector more specific
            if (selectedFlavor) {
                // First try exact flavor match
                selector = `${this.selectors.purchaseType.option}[data-variant-option-flavor="${selectedFlavor}"]:has-text("${textToSelect}"):not([style*="display: none"])`;
            } else {
                // Just find any visible option with the right text if no flavor
                selector = `${this.selectors.purchaseType.option}:has-text("${textToSelect}"):not([style*="display: none"])`;
            }
            
            console.log(`Using selector: ${selector}`);
            
            // Click the selected purchase option
            await this.page.locator(selector).click({
                timeout: 10000
            }).catch(async (err) => {
                console.error(`Failed with specific selector: ${err.message}`);
                
                // Try a more generic selector as fallback
                console.log("Trying fallback selector");
                const fallbackSelector = `${this.selectors.purchaseType.option}:has-text("${textToSelect}"):not([style*="display: none"])`;
                await this.page.locator(fallbackSelector).click({
                    timeout: 10000
                }).catch(err => {
                    console.error(`Failed with fallback selector: ${err.message}`);
                    throw new Error(`Purchase option "${textToSelect}" not found`);
                });
            });
            
            // Wait a moment for the UI to update
            await this.page.waitForTimeout(1000);
            
            // Verify selection by checking active class
            const isSelected = await this.page.locator(this.selectors.purchaseType.active).textContent()
                .then(text => text.includes(textToSelect));

            if (!isSelected) {
                throw new Error(`Failed to verify ${purchaseType} selection`);
            }

            console.log(`Successfully selected purchase type: ${purchaseType}`);

            // If subscription selected, wait for frequency selector
            if (purchaseType === 'subscription') {
                console.log('Waiting for subscription frequency selector...');
                try {
                    await this.page.waitForSelector(this.selectors.subscription.container, {
                        state: 'visible',
                        timeout: 20000  // Increased timeout
                    });
                    console.log('Subscription frequency selector is visible');
                    
                    // Verify the select element is present
                    const selectExists = await this.page.locator(this.selectors.subscription.select).count() > 0;
                    console.log(`Subscription frequency select exists: ${selectExists}`);
                    
                    if (!selectExists) {
                        // Try to look for any select inside the container as a fallback
                        const anySelect = await this.page.locator(`${this.selectors.subscription.container} select`).count() > 0;
                        console.log(`Any select element in container exists: ${anySelect}`);
                        
                        if (anySelect) {
                            // Adjust the selector based on what we find
                            const actualSelectName = await this.page.locator(`${this.selectors.subscription.container} select`).getAttribute('name');
                            console.log(`Found select with name: ${actualSelectName}`);
                            
                            // Update the selectors dynamically
                            this.selectors.subscription.select = `${this.selectors.subscription.container} select`;
                            this.selectors.subscription.frequencyOptions = `${this.selectors.subscription.container} select option`;
                        }
                    }
                } catch (error) {
                    console.error('Subscription frequency selector not found:', error.message);
                    
                    // Take a screenshot for debugging
                    await this.page.screenshot({ path: 'subscription-selector-error.png' });
                    
                    // Check if the subscription element exists but is not visible
                    const exists = await this.page.$(this.selectors.subscription.container) !== null;
                    console.log(`Subscription container exists but not visible: ${exists}`);
                    
                    // Add more diagnostics
                    const htmlContent = await this.page.content();
                    console.log(`Page contains subscription-frequency-wrapper: ${htmlContent.includes('product-subscription-frequency-wrapper')}`);
                    
                    throw error;
                }
            }
        } catch (error) {
            console.error(`Failed to select purchase type ${purchaseType}:`, error);
            throw error;
        }
    }

    /**
     * Gets the currently selected purchase option
     * @returns {Promise<string>} The selected purchase option text
     */
    async getSelectedPurchaseOption() {
        try {
            // Wait for the active purchase option
            const activeOption = this.page.locator(this.selectors.purchaseType.active);
            await activeOption.waitFor({ state: 'visible', timeout: 5000 });
            
            // Get the text content
            const text = await activeOption.textContent();
            return text ? text.trim() : '';
        } catch (error) {
            throw new Error(`Failed to get selected purchase option: ${error.message}`);
        }
    }

    /**
     * Get available quantity options
     * @returns {Promise<Array<{value: string, text: string, savings: string|null}>>} Array of available quantity options
     */
    async getAvailableQuantityOptions() {
        try {
            // First check if we have a quantity selector
            const quantityInput = this.page.locator(this.selectors.quantity.input);
            const exists = await quantityInput.count() > 0;
            
            if (!exists) {
                throw new Error('No quantity input found');
            }

            // Check for quantity options
            const options = await this.page.locator(this.selectors.quantity.options).all();
            
            if (options.length === 0) {
                // If no options, get the fixed quantity from input
                const value = await quantityInput.getAttribute('value') || '1';
                return [{
                    value,
                    text: `${value} item(s)`,
                    savings: null
                }];
            }

            // Get all available options with savings
            return await Promise.all(options.map(async (option) => {
                const value = await option.getAttribute('data-value') || '1';
                const text = await option.textContent();
                const savingsElement = await option.locator('.savings');
                const savings = await savingsElement.count() > 0 ? await savingsElement.textContent() : null;
                return { value, text, savings };
            }));
        } catch (error) {
            console.warn('Error getting quantity options:', error);
            // Default to single quantity if we can't determine options
            return [{
                value: '1',
                text: '1 item(s)',
                savings: null
            }];
        }
    }

    /**
     * Get quantity savings for a specific quantity
     * @param {number} quantity The quantity to check savings for
     * @returns {Promise<string|null>} The savings percentage or null if no savings
     */
    async getQuantitySavings(quantity) {
        try {
            const savingsElement = this.page.locator(this.selectors.quantity.savings(quantity));
            return await savingsElement.count() > 0 ? (await savingsElement.textContent()).trim() : null;
        } catch (error) {
            console.warn(`Error getting savings for quantity ${quantity}:`, error);
            return null;
        }
    }

    /**
     * Set quantity
     * @param {number} quantity Desired quantity
     */
    async setQuantity(quantity) {
        try {
            // Get available quantity options
            const availableOptions = await this.getAvailableQuantityOptions();
            
            // If only one option is available, verify it matches or warn
            if (availableOptions.length === 1) {
                const singleOption = parseInt(availableOptions[0].value);
                if (singleOption !== quantity) {
                    console.warn(`Requested quantity ${quantity} not available. Using fixed quantity ${singleOption}`);
                }
                return; // No need to change quantity if only one option
            }

            // Find the matching quantity option
            const targetOption = availableOptions.find(opt => parseInt(opt.value) === quantity);
            if (!targetOption) {
                throw new Error(`Quantity ${quantity} not available. Available options: ${availableOptions.map(opt => opt.value).join(', ')}`);
            }

            // Try to select the quantity
            const quantitySelector = this.page.locator(`${this.selectors.quantity.options}[data-value="${quantity}"]`);
            if (await quantitySelector.count() > 0) {
                await quantitySelector.click();
                await this.page.waitForTimeout(1000); // Wait for any updates
            } else {
                // If no options, set the input value directly
                const input = this.page.locator(this.selectors.quantity.input);
                await input.fill(quantity.toString());
                await input.dispatchEvent('change');
                await input.dispatchEvent('input');
            }

            // Verify quantity was set correctly
            const currentQuantity = await this.page.locator(this.selectors.quantity.input).inputValue();
            const actualQuantity = parseInt(currentQuantity);
            
            if (actualQuantity !== quantity) {
                throw new Error(`Quantity verification failed. Expected ${quantity}, got ${actualQuantity}`);
            }
        } catch (error) {
            throw new Error(`Failed to set quantity ${quantity}: ${error.message}`);
        }
    }

    /**
     * Get current price
     * @returns {Promise<number>} Current price
     */
    async getCurrentPrice() {
        try {
            // Try price selectors in order of preference for both DSS and Aeons sites
            const priceSelectors = [
                // DSS site selectors
                'span.price_item',
                this.selectors.price.current,
                `${this.selectors.price.container} ${this.selectors.price.amount}`,
                this.selectors.price.container,
                
                // Aeons site selectors
                '.val.sylius-product-variant-price',
                '.sylius-product-variant-price',
                '.qty-price-per-item',
                '#product-price',
                '.product-per-item-amount'
            ];

            let priceElement = null;
            for (const selector of priceSelectors) {
                const element = this.page.locator(selector);
                if (await element.count() > 0 && await element.isVisible()) {
                    // Make sure the element is visible and has content
                    const content = await element.textContent();
                    if (content && content.trim() && content.includes('£')) {
                        priceElement = element;
                        break;
                    }
                }
            }

            if (!priceElement) {
                // Last resort - try to find any visible element containing price format
                const priceFormatSelector = '//*[contains(text(), "£") and string-length(text()) < 20]';
                const priceByContent = this.page.locator(priceFormatSelector);
                
                if (await priceByContent.count() > 0) {
                    priceElement = priceByContent.first();
                } else {
                    throw new Error('Price element not found on page');
                }
            }

            // Wait for price to be visible and get text
            await priceElement.waitFor({ state: 'visible', timeout: 10000 });
            const priceText = await priceElement.textContent();
            
            if (!priceText) {
                throw new Error('Price text is empty');
            }

            // Extract numeric value from price text
            const numericValue = priceText.replace(/[^0-9.]/g, '');
            const price = parseFloat(numericValue);
            
            if (isNaN(price)) {
                throw new Error(`Invalid price format: ${priceText}`);
            }
            
            return price;
        } catch (error) {
            throw new Error(`Failed to get current price: ${error.message}`);
        }
    }

    /**
     * Get expected price based on current selection state
     * @param {Object} productData Product data from test data manager
     * @returns {Promise<number>} Expected price
     */
    async getExpectedPrice(productData) {
        try {
            // Get current flavor
            const currentFlavor = await this.getSelectedFlavor() || 'classic';
            
            // Get current purchase type
            const purchaseType = await this.getSelectedPurchaseOption();
            const isSubscription = purchaseType.includes('Subscribe');
            
            // Get current quantity
            const quantity = parseInt(await this.page.inputValue(this.selectors.quantity.input));
            
            // Get quantity tier
            let quantityTier;
            if (quantity === productData.options.quantities.minimum.numberOfItems) {
                quantityTier = 'minimum';
            } else if (quantity === productData.options.quantities.medium.numberOfItems) {
                quantityTier = 'medium';
            } else if (quantity === productData.options.quantities.maximum.numberOfItems) {
                quantityTier = 'maximum';
            } else {
                throw new Error(`Invalid quantity: ${quantity}`);
            }
            
            // Get price from product data
            const priceType = isSubscription ? 'subscription' : 'oneTime';
            const flavorPrices = productData.flavors[currentFlavor].prices[priceType];
            
            if (!flavorPrices || !flavorPrices[quantityTier]) {
                throw new Error(`Price not found for flavor: ${currentFlavor}, type: ${priceType}, tier: ${quantityTier}`);
            }
            
            return flavorPrices[quantityTier];
        } catch (error) {
            throw new Error(`Failed to get expected price: ${error.message}`);
        }
    }

    /**
     * Add to cart
     */
    async addToCart() {
        try {
            // Wait for button to be clickable
            await this.page.waitForSelector(this.selectors.addToCart, {
                state: 'visible',
                timeout: 5000
            });
            
            // Click add to cart
            await this.page.click(this.selectors.addToCart);
            
            // After clicking Add to Cart, the page navigates to cart page
            // Look for cart page indicators instead of the add to cart button
            
            // Try multiple cart page indicators
            const cartPageIndicators = [
                // Cart form container
                'form[name="sylius_cart"]',
                // Cart title or heading
                '.cart-title h1', 'h1:has-text("Your shopping cart")',
                // Cart table
                '.cart-table',
                // Success message
                '.alert-success', '[data-test="cart-success-message"]', '.item-has-been-added-message'
            ];
            
            console.log('Waiting for cart page to load after adding product...');
            
            // Try each indicator and wait for the first one found
            let cartPageDetected = false;
            for (const indicator of cartPageIndicators) {
                if (await this.page.locator(indicator).count() > 0) {
                    console.log(`Cart page indicator found: ${indicator}`);
                    try {
                        await this.page.locator(indicator).waitFor({
                            state: 'visible',
                            timeout: 10000
                        });
                        cartPageDetected = true;
                        break;
                    } catch (waitError) {
                        console.warn(`Indicator ${indicator} found but waiting failed: ${waitError.message}`);
                    }
                }
            }
            
            // If no indicators found, check if URL contains cart
            if (!cartPageDetected) {
                const currentUrl = this.page.url();
                if (currentUrl.includes('/cart') || currentUrl.includes('/checkout')) {
                    console.log(`Cart page detected via URL: ${currentUrl}`);
                    cartPageDetected = true;
                }
            }
            
            // Verify navigation to cart page
            if (!cartPageDetected) {
                console.warn('Cart page indicators not found, taking screenshot for debugging');
                await this.page.screenshot({ path: `cart-navigation-failed-${Date.now()}.png` });
                throw new Error('Failed to verify navigation to cart page');
            }
            
            console.log('Successfully added to cart and detected cart page');
        } catch (error) {
            throw new Error(`Failed to add to cart: ${error.message}`);
        }
    }

    /**
     * Get flavor option details
     * @param {string} code The flavor code to get details for
     * @returns {Promise<{name: string, description: string}>}
     */
    async getFlavorOption(code) {
        try {
            const option = this.page.locator(this.selectors.variant.flavorOption(code));
            if (await option.count() === 0) {
                throw new Error(`Flavor option ${code} not found`);
            }

            return {
                name: await option.locator(this.selectors.variant.flavorName).textContent(),
                description: await option.locator(this.selectors.variant.flavorDescription).textContent()
            };
        } catch (error) {
            throw new Error(`Failed to get flavor option ${code}: ${error.message}`);
        }
    }

    /**
     * Select a flavor variant
     * @param {string} flavorCode The flavor code to select (e.g., 'classic', 'lemon', 'truffle')
     */
    async selectFlavor(flavorCode) {
        try {
            console.log(`Attempting to select flavor: ${flavorCode}`);
            
            // First check if flavor options exist
            const flavorContainer = this.page.locator(this.selectors.variant.flavorContainer);
            const hasFlavorOptions = await flavorContainer.count() > 0;
            
            if (!hasFlavorOptions) {
                console.log('No flavor options found on the page');
                return;
            }
            
            // Wait for flavor container to be visible
            await flavorContainer.waitFor({
                state: 'visible',
                timeout: 10000
            });
            
            // Get all available flavors for logging
            const availableFlavors = await this.page.locator('.flavor-option')
                .evaluateAll(elements => elements.map(el => el.getAttribute('data-flavor')));
            console.log('Available flavors:', availableFlavors);
            
            // Use the flavor option selector
            const flavorSelector = this.selectors.variant.flavorOption(flavorCode);
            const flavorOption = this.page.locator(flavorSelector);
            
            // Check if the flavor exists
            if (await flavorOption.count() === 0) {
                throw new Error(`Flavor ${flavorCode} not found. Available flavors: ${availableFlavors.join(', ')}`);
            }
            
            // Wait for the specific flavor option to be visible
            await flavorOption.waitFor({
                state: 'visible',
                timeout: 10000
            });
            
            // Click the flavor option
            await flavorOption.click();
            
            // Wait for any dynamic updates
            await this.page.waitForTimeout(1000);
            
            // Verify selection
            const selectedFlavor = await this.page.locator(this.selectors.variant.selected)
                .getAttribute('data-flavor');
            
            if (selectedFlavor !== flavorCode) {
                throw new Error(`Failed to verify flavor selection. Expected: ${flavorCode}, Got: ${selectedFlavor}`);
            }
            
            console.log(`Successfully selected flavor: ${flavorCode}`);
        } catch (error) {
            console.error('Error in selectFlavor:', error);
            throw new Error(`Failed to select flavor ${flavorCode}: ${error.message}`);
        }
    }

    /**
     * Gets the currently selected flavor code
     * @returns {Promise<string|null>} The selected flavor code or null if no flavor selection
     */
    async getSelectedFlavor() {
        try {
            // First check for flavor options in the set-flavor elements
            const flavorOptions = this.page.locator('.choose-item.set-flavor.active');
            if (await flavorOptions.count() > 0) {
                const flavorCode = await flavorOptions.getAttribute('data-option-value-code');
                if (flavorCode) {
                    return flavorCode;
                }
            }
            
            // Try to get flavor from active purchase option as a fallback
            const activeOption = await this.page.locator(this.selectors.purchaseType.active);
            if (await activeOption.count() > 0) {
                const flavorCode = await activeOption.getAttribute('data-variant-option-flavor');
                if (flavorCode) {
                    return flavorCode;
                }
            }

            // If no active option found, try to get from selected flavor element
            const selectedFlavor = await this.page.locator(this.selectors.variant.selected);
            if (await selectedFlavor.count() > 0) {
                return await selectedFlavor.getAttribute('data-variant-option-flavor');
            }
            
            // If we couldn't determine a flavor, look at any visible purchase option
            const visibleOptions = await this.page.locator('.purchase-option:not([style*="display: none"])');
            if (await visibleOptions.count() > 0) {
                const firstVisible = visibleOptions.first();
                const flavorCode = await firstVisible.getAttribute('data-variant-option-flavor');
                if (flavorCode) {
                    return flavorCode;
                }
            }
            
            return null; // Return null if no flavor could be determined
        } catch (error) {
            console.warn(`Failed to get selected flavor: ${error.message}`);
            return null; // Return null on error
        }
    }

    /**
     * Get available subscription frequencies
     * @returns {Promise<string[]>} Array of available frequencies
     */
    async getAvailableSubscriptionFrequencies() {
        try {
            const options = this.page.locator(this.selectors.subscription.frequencyOptions);
            return await options.evaluateAll(elements => 
                elements.map(el => el.value).filter(Boolean)
            );
        } catch (error) {
            throw new Error(`Failed to get subscription frequencies: ${error.message}`);
        }
    }

    /**
     * Select subscription frequency
     * @param {string} frequency The frequency to select (e.g., '1 month', '2 months' or '30', '60')
     */
    async selectSubscriptionFrequency(frequency) {
        try {
            // Wait for subscription frequency container
            await this.page.waitForSelector(this.selectors.subscription.container);
            
            // Get all available options and their text/values
            const options = await this.page.locator(`${this.selectors.subscription.select} option`).all();
            let optionValue = frequency;
            let optionText = frequency;
            let isTextInput = isNaN(parseInt(frequency));
            
            // If frequency looks like text (e.g., "2 months"), find matching option value
            if (isTextInput) {
                console.log(`Input appears to be text: "${frequency}". Looking for matching option value...`);
                for (const option of options) {
                    const text = await option.textContent();
                    if (text.trim().toLowerCase() === frequency.toLowerCase()) {
                        optionValue = await option.getAttribute('value');
                        console.log(`Found matching value '${optionValue}' for frequency text '${frequency}'`);
                        break;
                    }
                }
            } else {
                // If it's a value, find the corresponding text for verification
                console.log(`Input appears to be a value: "${frequency}". Looking for display text...`);
                for (const option of options) {
                    const value = await option.getAttribute('value');
                    if (value === frequency) {
                        optionText = await option.textContent();
                        optionText = optionText.trim();
                        console.log(`Found display text '${optionText}' for value '${frequency}'`);
                        break;
                    }
                }
            }
            
            // Select by value
            console.log(`Selecting frequency option with value: ${optionValue}`);
            await this.page.selectOption(this.selectors.subscription.select, optionValue);
            
            // Wait for any price updates
            await this.page.waitForTimeout(1000);
            
            // Get both the selected value and text for verification
            const selectedData = await this.page.evaluate((selector) => {
                const select = document.querySelector(selector);
                if (!select) return { value: null, text: null };
                
                const selectedIndex = select.selectedIndex;
                const option = select.options[selectedIndex];
                
                return { 
                    value: select.value, 
                    text: option ? option.textContent.trim() : null 
                };
            }, this.selectors.subscription.select);
            
            console.log(`Selected option data - Value: "${selectedData.value}", Text: "${selectedData.text}"`);
            
            // Verify selection based on input type
            if (isTextInput) {
                // If input was text, verify the selected text matches
                if (selectedData.text.toLowerCase() !== frequency.toLowerCase()) {
                    console.error(`Text verification failed! Expected: "${frequency}", Got: "${selectedData.text}"`);
                    throw new Error(`Expected option text '${frequency}', but got '${selectedData.text}'`);
                }
            } else {
                // If input was value, verify the selected value matches
                if (selectedData.value !== frequency) {
                    console.error(`Value verification failed! Expected: "${frequency}", Got: "${selectedData.value}"`);
                    throw new Error(`Expected value '${frequency}', but got '${selectedData.value}'`);
                }
            }
            
            console.log(`Successfully selected frequency: ${isTextInput ? frequency : optionText} (value: ${optionValue})`);
        } catch (error) {
            console.error(`Error in selectSubscriptionFrequency:`, error);
            // Take a screenshot for debugging
            await this.page.screenshot({ path: 'subscription-frequency-error.png' });
            
            // Log available options for debugging
            try {
                const availableOptions = await this.page.evaluate((selector) => {
                    const select = document.querySelector(selector);
                    if (!select) return 'Select element not found';
                    
                    return Array.from(select.options).map(opt => ({
                        value: opt.value,
                        text: opt.textContent.trim()
                    }));
                }, this.selectors.subscription.select);
                
                console.log('Available frequency options:', JSON.stringify(availableOptions, null, 2));
            } catch (e) {
                console.error('Could not log available options:', e);
            }
            
            throw new Error(`Failed to select subscription frequency ${frequency}: ${error.message}`);
        }
    }
    
    /**
     * Get current subscription frequency
     * @returns {Promise<string>} The current subscription frequency
     */
    async getSubscriptionFrequency() {
        try {
            const select = this.page.locator(this.selectors.subscription.select);
            await select.waitFor({ state: 'visible' });
            return await select.evaluate(el => el.value);
        } catch (error) {
            throw new Error(`Failed to get subscription frequency: ${error.message}`);
        }
    }

    /**
     * Get product restrictions
     * @returns {Promise<{dietary?: string[], ageLimit?: string, medical?: string[], warnings?: string[]}>}
     */
    async getProductRestrictions() {
        try {
            const container = this.page.locator(this.selectors.restrictions.container);
            if (await container.count() === 0) return {};

            const restrictions = {};

            // Get dietary restrictions
            const dietary = this.page.locator(this.selectors.restrictions.dietary);
            if (await dietary.count() > 0) {
                restrictions.dietary = await dietary.evaluateAll(
                    elements => elements.map(el => el.textContent.trim())
                );
            }

            // Get age limit
            const ageLimit = this.page.locator(this.selectors.restrictions.ageLimit);
            if (await ageLimit.count() > 0) {
                restrictions.ageLimit = await ageLimit.textContent();
            }

            // Get medical restrictions
            const medical = this.page.locator(this.selectors.restrictions.medical);
            if (await medical.count() > 0) {
                restrictions.medical = await medical.evaluateAll(
                    elements => elements.map(el => el.textContent.trim())
                );
            }

            // Get warnings
            const warnings = this.page.locator(this.selectors.restrictions.warnings);
            if (await warnings.count() > 0) {
                restrictions.warnings = await warnings.evaluateAll(
                    elements => elements.map(el => el.textContent.trim())
                );
            }

            return restrictions;
        } catch (error) {
            console.warn('Error getting product restrictions:', error);
            return {};
        }
    }

    /**
     * Get trust badges
     * @returns {Promise<Array<{text: string, icon: string}>>}
     */
    async getTrustBadges() {
        try {
            const badges = this.page.locator(this.selectors.trustBadges.items);
            return await badges.evaluateAll(elements => 
                elements.map(el => ({
                    text: el.querySelector('.badge-text')?.textContent?.trim() || '',
                    icon: el.querySelector('.badge-icon')?.getAttribute('src') || ''
                }))
            );
        } catch (error) {
            console.warn('Error getting trust badges:', error);
            return [];
        }
    }
}

module.exports = { ProductPage };