/**
 * SessionManager - Singleton class to track and manage browser sessions across tests
 * 
 * This class helps prevent duplicate browser contexts by tracking all active sessions
 * and providing a centralized way to manage them.
 */
class SessionManager {
    static instance = null;
    
    constructor() {
        this.activeSessions = new Map();
        this.platformType = process.env.PLATFORM || 'desktop';
        this.isAndroidPlatform = this.platformType.includes('android');
        this.isBrowserStack = !!process.env.BROWSERSTACK_USERNAME;
    }
    
    /**
     * Get the singleton instance of SessionManager
     * @returns {SessionManager} The singleton instance
     */
    static getInstance() {
        if (!SessionManager.instance) {
            SessionManager.instance = new SessionManager();
        }
        return SessionManager.instance;
    }
    
    /**
     * Register a new browser session
     * @param {string} sessionId - Unique identifier for the session
     * @param {object} browserContext - The browser context to track
     * @param {object} options - Additional options
     * @param {boolean} options.force - Force registration even if session exists
     * @param {object} options.metadata - Additional metadata to store with session
     * @returns {object} The session data
     */
    async registerSession(sessionId, browserContext, options = {}) {
        const { force = false, metadata = {} } = options;
        
        // If we already have a session for this test
        if (this.activeSessions.has(sessionId) && !force) {
            console.log(`Session ${sessionId} already registered`);
            return this.activeSessions.get(sessionId);
        }
        
        // Store the new session
        this.activeSessions.set(sessionId, {
            id: sessionId,
            context: browserContext,
            startTime: Date.now(),
            metadata,
            screenshots: []
        });
        
        console.log(`Registered session ${sessionId}`);
        return this.activeSessions.get(sessionId);
    }
    
    /**
     * Track a screenshot URL for a session
     * @param {string} sessionId - Session identifier
     * @param {string} url - URL of the screenshot
     * @param {object} metadata - Additional metadata about the screenshot
     * @returns {Array} All screenshots for the session
     */
    async trackScreenshot(sessionId, url, metadata = {}) {
        if (!this.activeSessions.has(sessionId)) {
            console.warn(`Cannot track screenshot for unknown session ${sessionId}`);
            return [];
        }
        
        const session = this.activeSessions.get(sessionId);
        session.screenshots.push({
            url,
            timestamp: Date.now(),
            ...metadata
        });
        
        return session.screenshots;
    }
    
    /**
     * Close a browser session and clean up resources
     * @param {string} sessionId - Session identifier to close
     * @returns {boolean} Success status
     */
    async closeSession(sessionId) {
        if (!this.activeSessions.has(sessionId)) {
            console.warn(`Session ${sessionId} not found, nothing to close`);
            return false;
        }
        
        const session = this.activeSessions.get(sessionId);
        
        try {
            // Close browser context if it exists
            if (session.context && typeof session.context.close === 'function') {
                await session.context.close();
            }
            
            // Remove from tracked sessions
            this.activeSessions.delete(sessionId);
            console.log(`Closed session ${sessionId}`);
            return true;
        } catch (error) {
            console.error(`Error closing session ${sessionId}:`, error);
            return false;
        }
    }
    
    /**
     * Get session data for a given session ID
     * @param {string} sessionId - Session identifier
     * @returns {object|null} Session data or null if not found
     */
    async getSessionData(sessionId) {
        return this.activeSessions.get(sessionId) || null;
    }
    
    /**
     * Get all screenshots for a session
     * @param {string} sessionId - Session identifier
     * @returns {Array} Screenshot data array
     */
    async getScreenshots(sessionId) {
        if (!this.activeSessions.has(sessionId)) {
            return [];
        }
        
        return this.activeSessions.get(sessionId).screenshots;
    }
    
    /**
     * Clean up all active sessions
     * @returns {boolean} Success status
     */
    async cleanupAllSessions() {
        const sessionIds = Array.from(this.activeSessions.keys());
        console.log(`Cleaning up ${sessionIds.length} active sessions`);
        
        for (const sessionId of sessionIds) {
            await this.closeSession(sessionId);
        }
        
        return true;
    }
}

module.exports = { SessionManager };