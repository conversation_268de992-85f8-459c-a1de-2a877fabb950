/**
 * @fileoverview Email Verification Helper
 *
 * Centralized helper for email verification that eliminates repetitive
 * email verification patterns while maintaining compatibility with existing
 * Mailtrap integration and brand-specific email templates.
 * 
 * Features:
 * - Standardized email verification patterns
 * - Brand-specific email template handling
 * - Enhanced retry logic and error handling
 * - Support for different email types (order confirmation, abandoned cart, etc.)
 * - Graceful degradation for email verification failures
 */

/**
 * Email Verification Helper with enhanced patterns
 */
class EmailVerificationHelper {
    constructor(mailtrapHelper, testDataManager) {
        this.mailtrapHelper = mailtrapHelper;
        this.testDataManager = testDataManager;
        this.brand = testDataManager.brand;
        
        console.log(`[EmailVerificationHelper] Initialized for brand: ${this.brand}`);
    }

    /**
     * Wait for order confirmation email with brand-specific handling
     * @param {string} customerEmail - Customer email address
     * @param {Object} options - Verification options
     * @returns {Promise<Object|null>} Email object or null if not found
     */
    async waitForOrderConfirmationEmail(customerEmail, options = {}) {
        console.log(`[EmailVerificationHelper] Waiting for order confirmation email for: ${customerEmail}`);
        
        const defaultOptions = {
            timeout: 60000, // 1 minute
            attempts: 6,
            interval: 10000, // 10 seconds
            containsOrderNumber: false
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        try {
            // Get brand-specific email subject
            const expectedSubject = this.getBrandOrderConfirmationSubject();
            
            const email = await this.mailtrapHelper.waitForEmail(
                customerEmail,
                expectedSubject,
                finalOptions.timeout / 1000, // Convert to seconds
                finalOptions.attempts,
                finalOptions.interval / 1000 // Convert to seconds
            );
            
            if (email) {
                console.log(`[EmailVerificationHelper] Order confirmation email found: ${email.id}`);
                
                // Additional validation if order number is expected
                if (finalOptions.containsOrderNumber && finalOptions.orderNumber) {
                    const content = await this.mailtrapHelper.getHtmlContent(
                        this.mailtrapHelper.inboxId,
                        email.id
                    );
                    
                    if (!content.includes(finalOptions.orderNumber)) {
                        console.warn(`[EmailVerificationHelper] Email found but doesn't contain order number: ${finalOptions.orderNumber}`);
                        return null;
                    }
                }
                
                return email;
            }
            
            return null;
        } catch (error) {
            console.error(`[EmailVerificationHelper] Order confirmation email verification failed: ${error.message}`);
            return null;
        }
    }

    /**
     * Wait for abandoned cart email with enhanced retry logic
     * @param {string} customerEmail - Customer email address
     * @param {Object} options - Verification options
     * @returns {Promise<Object|null>} Email object or null if not found
     */
    async waitForAbandonedCartEmail(customerEmail, options = {}) {
        console.log(`[EmailVerificationHelper] Waiting for abandoned cart email for: ${customerEmail}`);
        
        const defaultOptions = {
            timeout: 120000, // 2 minutes (abandoned cart emails may take longer)
            attempts: 12,
            interval: 10000,
            searchAfter: new Date(Date.now() - 10 * 60 * 1000).toISOString() // Last 10 minutes
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        try {
            const expectedSubject = this.getBrandAbandonedCartSubject();
            
            const email = await this.mailtrapHelper.waitForEmail({
                recipient: customerEmail,
                subject: expectedSubject,
                search_after: finalOptions.searchAfter
            }, finalOptions.attempts, finalOptions.interval);
            
            if (email) {
                console.log(`[EmailVerificationHelper] Abandoned cart email found: ${email.id}`);
                return email;
            }
            
            return null;
        } catch (error) {
            console.error(`[EmailVerificationHelper] Abandoned cart email verification failed: ${error.message}`);
            return null;
        }
    }

    /**
     * Wait for welcome email with brand-specific handling
     * @param {string} customerEmail - Customer email address
     * @param {Object} options - Verification options
     * @returns {Promise<Object|null>} Email object or null if not found
     */
    async waitForWelcomeEmail(customerEmail, options = {}) {
        console.log(`[EmailVerificationHelper] Waiting for welcome email for: ${customerEmail}`);
        
        const defaultOptions = {
            timeout: 60000,
            attempts: 6,
            interval: 10000
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        try {
            const expectedSubject = this.getBrandWelcomeSubject();
            
            const email = await this.mailtrapHelper.waitForEmail(
                customerEmail,
                expectedSubject,
                finalOptions.timeout / 1000,
                finalOptions.attempts,
                finalOptions.interval / 1000
            );
            
            if (email) {
                console.log(`[EmailVerificationHelper] Welcome email found: ${email.id}`);
                return email;
            }
            
            return null;
        } catch (error) {
            console.error(`[EmailVerificationHelper] Welcome email verification failed: ${error.message}`);
            return null;
        }
    }

    /**
     * Verify email content contains expected elements
     * @param {string} emailId - Email ID
     * @param {string[]} expectedElements - Array of expected content elements
     * @returns {Promise<boolean>} True if all elements found
     */
    async verifyEmailContent(emailId, expectedElements = []) {
        console.log(`[EmailVerificationHelper] Verifying email content for: ${emailId}`);
        
        try {
            const content = await this.mailtrapHelper.getHtmlContent(
                this.mailtrapHelper.inboxId,
                emailId
            );
            
            const missingElements = [];
            
            for (const element of expectedElements) {
                if (!content.includes(element)) {
                    missingElements.push(element);
                }
            }
            
            if (missingElements.length > 0) {
                console.warn(`[EmailVerificationHelper] Missing email content elements: ${missingElements.join(', ')}`);
                return false;
            }
            
            console.log(`[EmailVerificationHelper] All expected email content elements found`);
            return true;
        } catch (error) {
            console.error(`[EmailVerificationHelper] Email content verification failed: ${error.message}`);
            return false;
        }
    }

    /**
     * Verify email with graceful degradation
     * @param {string} emailType - Email type (order_confirmation, abandoned_cart, welcome)
     * @param {string} customerEmail - Customer email address
     * @param {Object} options - Verification options
     * @returns {Promise<boolean>} True if verification successful, false if failed gracefully
     */
    async verifyEmailWithGracefulDegradation(emailType, customerEmail, options = {}) {
        console.log(`[EmailVerificationHelper] Verifying ${emailType} email with graceful degradation`);
        
        try {
            let email = null;
            
            switch (emailType) {
                case 'order_confirmation':
                    email = await this.waitForOrderConfirmationEmail(customerEmail, options);
                    break;
                case 'abandoned_cart':
                    email = await this.waitForAbandonedCartEmail(customerEmail, options);
                    break;
                case 'welcome':
                    email = await this.waitForWelcomeEmail(customerEmail, options);
                    break;
                default:
                    console.warn(`[EmailVerificationHelper] Unknown email type: ${emailType}`);
                    return false;
            }
            
            if (email) {
                // Additional content verification if specified
                if (options.expectedContent && options.expectedContent.length > 0) {
                    return await this.verifyEmailContent(email.id, options.expectedContent);
                }
                return true;
            }
            
            console.warn(`[EmailVerificationHelper] ${emailType} email not found, but continuing gracefully`);
            return false;
        } catch (error) {
            console.error(`[EmailVerificationHelper] Email verification failed: ${error.message}`);
            console.log(`[EmailVerificationHelper] Continuing test execution despite email verification failure`);
            return false;
        }
    }

    /**
     * Get brand-specific order confirmation email subject
     * @returns {string} Expected email subject
     */
    getBrandOrderConfirmationSubject() {
        try {
            return this.testDataManager.getOrderConfirmationEmailSubject();
        } catch (error) {
            // Fallback to brand-specific defaults
            const subjects = {
                'aeons': 'AEONS Order Confirmation',
                'dss': 'Your Dr. Sister Skincare Order Confirmation',
                'ypn': 'Your Pet Nutrition Order Confirmation'
            };
            
            return subjects[this.brand] || `${this.brand.toUpperCase()} Order Confirmation`;
        }
    }

    /**
     * Get brand-specific abandoned cart email subject
     * @returns {string} Expected email subject
     */
    getBrandAbandonedCartSubject() {
        const subjects = {
            'aeons': 'Complete your purchase',
            'dss': 'You left something behind | Dr. Sister Skincare',
            'ypn': 'Your cart is waiting | Your Pet Nutrition'
        };
        
        return subjects[this.brand] || 'Complete your purchase';
    }

    /**
     * Get brand-specific welcome email subject
     * @returns {string} Expected email subject
     */
    getBrandWelcomeSubject() {
        const subjects = {
            'aeons': 'Welcome To AEONS!',
            'dss': 'Welcome To Dr. Sister Skincare!',
            'ypn': 'Welcome To Your Pet Nutrition!'
        };
        
        return subjects[this.brand] || `Welcome To ${this.brand.toUpperCase()}!`;
    }

    /**
     * Get brand-specific expected email content elements
     * @param {string} emailType - Email type
     * @returns {string[]} Array of expected content elements
     */
    getBrandExpectedContent(emailType) {
        const contentMap = {
            'aeons': {
                'order_confirmation': ['Order Number', 'Total', 'Shipping Address'],
                'abandoned_cart': ['items in your cart', 'Complete Purchase'],
                'welcome': ['account login credentials', 'getting started']
            },
            'dss': {
                'order_confirmation': ['Order Number', 'Total', 'Shipping Address'],
                'abandoned_cart': ['Dark Spot Vanish', 'Complete Purchase'],
                'welcome': ['account login credentials', 'skincare routine']
            },
            'ypn': {
                'order_confirmation': ['Order Number', 'Total', 'Shipping Address'],
                'abandoned_cart': ['pet nutrition', 'Complete Purchase'],
                'welcome': ['account login credentials', 'pet care tips']
            }
        };
        
        return contentMap[this.brand]?.[emailType] || [];
    }

    /**
     * Generate test email address for the current brand
     * @param {string} prefix - Email prefix
     * @returns {string} Generated test email
     */
    generateTestEmail(prefix = 'test') {
        const timestamp = Date.now();
        return `${prefix}-${this.brand}-${timestamp}@malaberg.com`;
    }

    /**
     * Clean up test emails (if supported by email service)
     * @param {string[]} emailIds - Array of email IDs to clean up
     * @returns {Promise<void>}
     */
    async cleanupTestEmails(emailIds = []) {
        console.log(`[EmailVerificationHelper] Cleaning up ${emailIds.length} test emails`);
        
        try {
            // Implementation depends on Mailtrap API capabilities
            // This is a placeholder for future cleanup functionality
            for (const emailId of emailIds) {
                console.log(`[EmailVerificationHelper] Would clean up email: ${emailId}`);
            }
        } catch (error) {
            console.warn(`[EmailVerificationHelper] Email cleanup failed: ${error.message}`);
        }
    }
}

module.exports = { EmailVerificationHelper };
