/**
 * @fileoverview Sales Funnel Flow Helper
 *
 * Centralized helper for sales funnel operations that eliminates
 * repetitive sales funnel workflow code across different test files.
 *
 * Features:
 * - Complete sales funnel flow automation
 * - Admin panel funnel management
 * - Support for upsell/downsell flows
 * - Backend API integration for order completion
 * - Mobile-optimized interactions
 * - Error handling and retry logic
 * - Email verification integration
 */

class SalesFunnelFlowHelper {
    constructor(pageObjectFactory, testDataHelper, emailHelper, browserStackHelper, deviceHelper) {
        this.pageObjectFactory = pageObjectFactory;
        this.testDataHelper = testDataHelper;
        this.emailHelper = emailHelper;
        this.browserStackHelper = browserStackHelper;
        this.deviceHelper = deviceHelper;
    }

    /**
     * Complete sales funnel flow with upsell
     * @param {Object} options - Flow configuration options
     * @returns {Promise<Object>} Flow results
     */
    async completeSalesFunnelWithUpsell(options = {}) {
        const {
            product = 'default',
            customer = 'default',
            paymentMethod = 'stripe_valid',
            acceptUpsell = true,
            skipEmailVerification = false
        } = options;

        console.log('[SalesFunnelFlowHelper] Starting sales funnel flow with upsell');

        const results = {
            initialOrderId: null,
            upsellOrderId: null,
            totalAmount: 0,
            emailVerified: false,
            flowCompleted: false
        };

        try {
            // Step 1: Navigate to initial checkout
            const initialCheckoutPage = this.pageObjectFactory.getSalesFunnelInitialCheckoutPage();

            // Use brand default product if 'default' is specified
            const actualProductKey = product === 'default' ? this.testDataHelper.getBrandDefaultProduct() : product;
            const productData = this.testDataHelper.testDataManager.getProduct(actualProductKey);
            const customerData = this.testDataHelper.testDataManager.getUser(customer);

            await initialCheckoutPage.navigateToProduct(productData.urlPath);
            await initialCheckoutPage.fillCustomerDetails(customerData);

            // Step 2: Complete initial payment
            const paymentPage = this.pageObjectFactory.getSalesFunnelPaymentPage();
            const paymentData = this.testDataHelper.testDataManager.getPaymentMethod(paymentMethod);

            await paymentPage.fillPaymentDetails(paymentData);
            await paymentPage.submitPayment();

            results.initialOrderId = await paymentPage.getOrderId();
            console.log(`[SalesFunnelFlowHelper] Initial order completed: ${results.initialOrderId}`);

            // Step 3: Handle upsell
            if (acceptUpsell) {
                const upsellPage = this.pageObjectFactory.getSalesFunnelUpsellPage();
                await upsellPage.waitForUpsellOffer();
                await upsellPage.acceptUpsell();

                results.upsellOrderId = await upsellPage.getUpsellOrderId();
                console.log(`[SalesFunnelFlowHelper] Upsell completed: ${results.upsellOrderId}`);
            }

            // Step 4: Verify confirmation
            const confirmationPage = this.pageObjectFactory.getSalesFunnelConfirmationPage();
            await confirmationPage.waitForConfirmation();
            results.totalAmount = await confirmationPage.getTotalAmount();

            // Step 5: Email verification (if enabled)
            if (!skipEmailVerification) {
                results.emailVerified = await this.verifyFunnelEmails(customerData.email, results);
            }

            results.flowCompleted = true;
            console.log('[SalesFunnelFlowHelper] Sales funnel flow completed successfully');

            return results;

        } catch (error) {
            console.error('[SalesFunnelFlowHelper] Sales funnel flow failed:', error);
            throw error;
        }
    }

    /**
     * Complete abandoned cart flow
     * @param {Object} options - Flow configuration options
     * @returns {Promise<Object>} Flow results
     */
    async completeAbandonedCartFlow(options = {}) {
        const {
            product = 'default',
            customer = 'default',
            abandonAtStep = 'payment',
            waitForEmail = true
        } = options;

        console.log('[SalesFunnelFlowHelper] Starting abandoned cart flow');

        const results = {
            abandonedAt: abandonAtStep,
            emailReceived: false,
            recoveryLinkClicked: false
        };

        try {
            // Step 1: Start checkout process
            const initialCheckoutPage = this.pageObjectFactory.getSalesFunnelInitialCheckoutPage();

            // Use brand default product if 'default' is specified
            const actualProductKey = product === 'default' ? this.testDataHelper.getBrandDefaultProduct() : product;
            const productData = this.testDataHelper.testDataManager.getProduct(actualProductKey);
            const customerData = this.testDataHelper.testDataManager.getUser(customer);

            await initialCheckoutPage.navigateToProduct(productData.urlPath);
            await initialCheckoutPage.fillCustomerDetails(customerData);

            // Step 2: Abandon at specified step
            if (abandonAtStep === 'payment') {
                const paymentPage = this.pageObjectFactory.getSalesFunnelPaymentPage();
                await paymentPage.navigateToPayment();
                // Abandon by closing or navigating away
                await paymentPage.page.close();
            }

            // Step 3: Wait for abandoned cart email
            if (waitForEmail) {
                results.emailReceived = await this.waitForAbandonedCartEmail(customerData.email);
            }

            console.log('[SalesFunnelFlowHelper] Abandoned cart flow completed');
            return results;

        } catch (error) {
            console.error('[SalesFunnelFlowHelper] Abandoned cart flow failed:', error);
            throw error;
        }
    }

    /**
     * Force complete order via backend API
     * @param {string} orderId - Order ID to complete
     * @param {Object} api - API instance (optional, for dependency injection)
     * @returns {Promise<Object>} Completion results
     */
    async forceCompleteOrderViaBackend(orderId, api = null) {
        console.log(`[SalesFunnelFlowHelper] Force completing order via backend: ${orderId}`);

        try {
            if (api && api.forceCompleteOrder) {
                // Use provided API instance
                const result = await api.forceCompleteOrder(orderId);
                console.log('[SalesFunnelFlowHelper] Order force completed successfully');
                return result;
            } else {
                // Try to load the real API
                const SalesFunnelApi = require('../../../src/api/endpoints/SalesFunnelApi');
                const apiInstance = new SalesFunnelApi();

                const result = await apiInstance.forceCompleteOrder(orderId);
                console.log('[SalesFunnelFlowHelper] Order force completed successfully');
                return result;
            }

        } catch (error) {
            console.error('[SalesFunnelFlowHelper] Failed to force complete order:', error);
            throw error;
        }
    }

    /**
     * Verify funnel-related emails
     * @param {string} email - Customer email
     * @param {Object} orderData - Order data for verification
     * @returns {Promise<boolean>} Verification result
     */
    async verifyFunnelEmails(email, orderData) {
        console.log(`[SalesFunnelFlowHelper] Verifying funnel emails for: ${email}`);

        try {
            // Wait for confirmation email
            const confirmationEmail = await this.emailHelper.waitForEmail(email, 'order_confirmation', 60000);

            if (confirmationEmail) {
                // Verify email content contains order details
                const emailContent = confirmationEmail.text || confirmationEmail.html;
                const containsOrderId = orderData.initialOrderId &&
                    emailContent.includes(orderData.initialOrderId);

                console.log('[SalesFunnelFlowHelper] Funnel email verification completed');
                return containsOrderId;
            }

            return false;

        } catch (error) {
            console.warn('[SalesFunnelFlowHelper] Email verification failed:', error);
            return false;
        }
    }

    /**
     * Wait for abandoned cart email
     * @param {string} email - Customer email
     * @returns {Promise<boolean>} Email received status
     */
    async waitForAbandonedCartEmail(email) {
        console.log(`[SalesFunnelFlowHelper] Waiting for abandoned cart email: ${email}`);

        try {
            const abandonedCartEmail = await this.emailHelper.waitForEmail(email, 'abandoned_cart', 120000);
            return !!abandonedCartEmail;

        } catch (error) {
            console.warn('[SalesFunnelFlowHelper] Abandoned cart email not received:', error);
            return false;
        }
    }

    /**
     * Setup admin funnel configuration
     * @param {Object} funnelConfig - Funnel configuration
     * @returns {Promise<void>}
     */
    async setupAdminFunnelConfig(funnelConfig) {
        console.log('[SalesFunnelFlowHelper] Setting up admin funnel configuration');

        try {
            const adminLoginPage = this.pageObjectFactory.getAdminLoginPage();
            const adminSalesFunnelPage = this.pageObjectFactory.getAdminSalesFunnelPage();

            // Login to admin
            const adminCredentials = this.testDataHelper.getAdminCredentials();
            await adminLoginPage.login(adminCredentials);

            // Configure funnel
            await adminSalesFunnelPage.configureFunnel(funnelConfig);

            console.log('[SalesFunnelFlowHelper] Admin funnel configuration completed');

        } catch (error) {
            console.error('[SalesFunnelFlowHelper] Admin funnel configuration failed:', error);
            throw error;
        }
    }
}

module.exports = { SalesFunnelFlowHelper };
