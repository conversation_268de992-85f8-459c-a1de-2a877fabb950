/**
 * @fileoverview Test implementation for TC-034: Subscription Reorder Basic Flow
 * @tags @regression @subscription @renewal
 */
const { test, expect } = require('../fixtures/enhanced-unified-fixture');
const { AdminLoginPage } = require('../../src/pages/admin/AdminLoginPage.js');
const { AdminOrdersPage } = require('../../src/pages/admin/AdminOrdersPage.js');
const SubscriptionApi = require('../../src/api/endpoints/SubscriptionApi.js');

test.describe('Critical Flow: Subscription Renewal', () => {
    test('TC-034: Manually trigger a subscription renewal using API', async ({
        page, dbUtils, emailUtils, testData
    }) => {
        // Initialize API client
        const subscriptionApi = new SubscriptionApi();

        // Original subscription order number from test case
        const originalOrderNumber = '0000003089';
        const customerEmail = testData.customer.email.replace('test', '0917-t1-sub');

        // 1. Login to admin panel
        await test.step('Login to admin panel', async () => {
            const adminLoginPage = new AdminLoginPage(page);
            await adminLoginPage.login(process.env.ADMIN_USER, process.env.ADMIN_PASSWORD);
        });

        // 2. View original order details
        await test.step('View original subscription order', async () => {
            await page.goto(`/admin/orders/${originalOrderNumber}`);

            // Verify we're on the order page
            await page.waitForSelector('.order-show');
            const orderTitle = await page.textContent('.content-header h1');
            expect(orderTitle).toContain(`Order #${originalOrderNumber}`);

            // Verify order state and payment state
            const orderDetails = await new AdminOrdersPage(page).getOrderDetails(originalOrderNumber);
            expect(orderDetails.state).toBe('completed');
            expect(orderDetails.paymentState).toBe('paid');
        });

        // 3. Trigger subscription renewal via API
        await test.step('Trigger subscription renewal via API', async () => {
            // Note: The API command triggers renewal for subscriptions last ordered before a certain time.
            // We assume the original order was placed before '-1second' for this test.
            const result = await subscriptionApi.triggerRenewal('DSS', '-1second');

            expect(result.success).toBeTruthy();
            expect(result.message).toContain('reordered');

            // Extract the new order number from the message
            const newOrderNumberMatch = result.message.match(/order (\d+)/);
            testData.newOrderNumber = newOrderNumberMatch ? newOrderNumberMatch[1] : null;
            expect(testData.newOrderNumber).toBeTruthy();

            console.log(`Created new order #${testData.newOrderNumber} for subscription renewal`);
        });

        // 4. Verify new order in database
        await test.step('Verify new order in database', async () => {
            const newOrder = await dbUtils.getOrderByNumber(testData.newOrderNumber);
            expect(newOrder).toBeTruthy();
            expect(newOrder.state).toBe('new');
            expect(newOrder.payment_state).toBe('paid');
            expect(newOrder.shipping_state).toBe('ready');

            // Verify order total (assuming same product and price as original)
            // Need to fetch original order items to compare total accurately
            const originalOrder = await dbUtils.getOrderByNumber(originalOrderNumber);
            expect(parseFloat(newOrder.total)).toBeCloseTo(parseFloat(originalOrder.total), 1);
        });

        // 5. Verify confirmation email for the new order
        await test.step('Verify confirmation email for renewal order', async () => {
            const email = await emailUtils.waitForEmail({
                recipient: customerEmail,
                subject: `Order #${testData.newOrderNumber} Is Confirmed`,
                search_after: new Date(Date.now() - 10 * 60 * 1000).toISOString()
            });

            expect(email).toBeTruthy();

            const emailContent = await emailUtils.getEmailContent(email.id);
            expect(emailContent).toContain(`Order #${testData.newOrderNumber}`);
            // Add more assertions based on expected email content for renewal
        });
    });
});
