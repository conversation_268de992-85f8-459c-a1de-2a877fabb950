/**
 * @fileoverview Test Data Helper
 *
 * Standardized helper for test data preparation that eliminates repetitive
 * test data setup patterns while maintaining full compatibility with the
 * existing TestDataManager and centralized YAML-based data structure.
 *
 * Features:
 * - Standard test data patterns for common scenarios
 * - Brand-specific data helpers
 * - Environment-agnostic configuration
 * - Full TestDataManager compatibility
 * - Support for all existing data types (products, users, payments, etc.)
 */

/**
 * Test Data Helper with standardized patterns
 */
class TestDataHelper {
    constructor(testDataManager) {
        this.testDataManager = testDataManager;

        console.log(`[TestDataHelper] Initialized for brand: ${testDataManager.brand}, environment: ${testDataManager.environment}`);
    }

    /**
     * Get standard purchase test data
     * @param {string} productKey - Product key from YAML data
     * @param {string} userType - User type (default: 'default')
     * @param {string} paymentType - Payment method type (default: 'stripe_valid')
     * @returns {Object} Complete test data for purchase flows
     */
    getPurchaseTestData(productKey = 'default', userType = 'default', paymentType = 'stripe_valid') {
        console.log(`[TestDataHelper] Preparing purchase test data for product: ${productKey}`);

        // Use brand-specific default product if 'default' is specified
        const actualProductKey = productKey === 'default' ? this.getBrandDefaultProduct() : productKey;

        try {
            const testData = {
                product: this.testDataManager.getProduct(actualProductKey),
                user: this.testDataManager.getUser(userType),
                paymentMethod: this.testDataManager.getPaymentMethod(paymentType),
                shippingAddressOption: 'same',
                expectedShippingMethodValue: this.getDefaultShippingMethod()
            };

            // Add computed properties for convenience
            testData.baseUrl = this.testDataManager.getBaseUrl();
            testData.brand = this.testDataManager.brand;
            testData.environment = this.testDataManager.environment;

            console.log(`[TestDataHelper] Purchase test data prepared for ${testData.product.name}`);
            return testData;
        } catch (error) {
            console.error(`[TestDataHelper] Error preparing purchase test data: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get subscription test data with subscription-specific defaults
     * @param {string} productKey - Product key from YAML data
     * @param {string} userType - User type (default: 'default')
     * @param {string} frequency - Subscription frequency (default: 'monthly')
     * @returns {Object} Complete test data for subscription flows
     */
    getSubscriptionTestData(productKey = 'default', userType = 'default', frequency = 'monthly') {
        console.log(`[TestDataHelper] Preparing subscription test data for product: ${productKey}`);

        const baseData = this.getPurchaseTestData(productKey, userType, 'stripe_valid');

        return {
            ...baseData,
            subscriptionFrequency: frequency,
            purchaseType: 'subscription',
            expectedShippingMethodValue: 'subscription_tracked'
        };
    }

    /**
     * Get PayPal test data with PayPal-specific configuration
     * @param {string} productKey - Product key from YAML data
     * @param {string} userType - User type (default: 'paypal_test')
     * @returns {Object} Complete test data for PayPal flows
     */
    getPayPalTestData(productKey = 'default', userType = 'paypal_test') {
        console.log(`[TestDataHelper] Preparing PayPal test data for product: ${productKey}`);

        // Try to get PayPal-specific user, fallback to default if not available
        let actualUserType = userType;
        try {
            this.testDataManager.getUser(userType);
        } catch (error) {
            console.warn(`[TestDataHelper] PayPal user type '${userType}' not found, using 'default'`);
            actualUserType = 'default';
        }

        // Use the correct PayPal payment method name from test data
        const paypalPaymentMethod = this.getAvailablePayPalPaymentMethod();
        const baseData = this.getPurchaseTestData(productKey, actualUserType, paypalPaymentMethod);

        return {
            ...baseData,
            paymentFlow: 'paypal',
            expectedPaymentMethod: 'paypal'
        };
    }

    /**
     * Get sales funnel test data with funnel-specific configuration
     * @param {string} funnelKey - Funnel configuration key
     * @param {string} userType - User type (default: 'default')
     * @returns {Object} Complete test data for sales funnel flows
     */
    getSalesFunnelTestData(funnelKey, userType = 'default') {
        console.log(`[TestDataHelper] Preparing sales funnel test data for funnel: ${funnelKey}`);

        try {
            const funnelConfig = this.testDataManager.getFunnelConfig(funnelKey);
            const baseData = this.getPurchaseTestData(funnelConfig.product || 'default', userType);

            return {
                ...baseData,
                funnelConfig,
                funnelKey,
                upsellProducts: funnelConfig.upsells || [],
                entryUrl: funnelConfig.entry?.url,
                upsellUrl: funnelConfig.upsell?.url
            };
        } catch (error) {
            console.warn(`[TestDataHelper] Funnel config '${funnelKey}' not found, using default product data`);
            return this.getPurchaseTestData('default', userType);
        }
    }

    /**
     * Get email verification test data
     * @param {string} emailType - Email type (order_confirmation, abandoned_cart, etc.)
     * @param {string} customerEmail - Customer email address
     * @returns {Object} Email verification test data
     */
    getEmailVerificationTestData(emailType, customerEmail = null) {
        console.log(`[TestDataHelper] Preparing email verification data for type: ${emailType}`);

        const emailData = {
            emailType,
            customerEmail: customerEmail || this.generateTestEmail(),
            expectedSubject: this.getExpectedEmailSubject(emailType),
            senderEmail: this.testDataManager.getBrandSenderEmail(),
            brand: this.testDataManager.brand
        };

        return emailData;
    }

    /**
     * Get brand-specific data and configuration
     * @param {string} brand - Brand name (optional, uses current brand if not specified)
     * @returns {Object} Brand-specific configuration and data
     */
    getBrandSpecificData(brand = null) {
        const currentBrand = brand || this.testDataManager.brand;

        return {
            brand: currentBrand,
            baseUrl: this.testDataManager.getBaseUrl(),
            emailDomain: this.getBrandEmailDomain(currentBrand),
            defaultProducts: this.getBrandDefaultProducts(currentBrand),
            senderEmail: this.testDataManager.getBrandSenderEmail(),
            environment: this.testDataManager.environment
        };
    }

    /**
     * Get test data for abandoned cart scenarios
     * @param {string} productKey - Product key
     * @param {string} userType - User type
     * @returns {Object} Abandoned cart test data
     */
    getAbandonedCartTestData(productKey = 'default', userType = 'default') {
        console.log(`[TestDataHelper] Preparing abandoned cart test data`);

        const baseData = this.getPurchaseTestData(productKey, userType);
        const emailData = this.getEmailVerificationTestData('abandoned_cart', baseData.user.email);

        return {
            ...baseData,
            ...emailData,
            abandonmentStage: 'payment_selection', // Default abandonment stage
            expectedEmailDelay: 300000 // 5 minutes default delay
        };
    }

    /**
     * Get mobile-optimized test data
     * @param {string} productKey - Product key
     * @param {string} userType - User type
     * @returns {Object} Mobile test data with extended timeouts
     */
    getMobileTestData(productKey = 'default', userType = 'default') {
        console.log(`[TestDataHelper] Preparing mobile test data`);

        const baseData = this.getPurchaseTestData(productKey, userType);

        return {
            ...baseData,
            isMobile: true,
            extendedTimeouts: true,
            mobileOptimized: true,
            expectedTimeout: 300000 // 5 minutes for mobile
        };
    }

    /**
     * Get brand default product key
     * @returns {string} Default product key for current brand
     */
    getBrandDefaultProduct() {
        const defaults = {
            'aeons': 'ancient_roots_olive_oil',
            'dss': 'dark_spot_vanish',
            'ypn': 'premium_dog_food'
        };

        return defaults[this.testDataManager.brand] || 'ancient_roots_olive_oil';
    }

    /**
     * Get brand default products list
     * @param {string} brand - Brand name
     * @returns {string[]} Array of default product keys
     */
    getBrandDefaultProducts(brand) {
        const defaults = {
            'aeons': ['ancient_roots_olive_oil', 'total_harmony', 'natures_gift_bone_broth'],
            'dss': ['dark_spot_vanish', 'younger_you_skin_cream', 'relax_restore'],
            'ypn': ['premium_dog_food', 'cat_wellness', 'pet_supplements']
        };

        return defaults[brand] || defaults['aeons'];
    }

    /**
     * Get brand email domain
     * @param {string} brand - Brand name
     * @returns {string} Email domain for the brand
     */
    getBrandEmailDomain(brand) {
        const domains = {
            'aeons': 'aeons.co',
            'dss': 'drsisterskincare.com',
            'ypn': 'yourpetnutrition.com'
        };

        return domains[brand] || 'example.com';
    }

    /**
     * Get default shipping method for current brand
     * @returns {string} Default shipping method
     */
    getDefaultShippingMethod() {
        // Use existing TestDataManager logic for shipping methods
        try {
            const shippingMethod = this.testDataManager.getShippingMethod('UK');
            return shippingMethod.method || 'domestic_tracked';
        } catch (error) {
            return 'domestic_tracked';
        }
    }

    /**
     * Generate a test email address
     * @param {string} prefix - Email prefix (optional)
     * @returns {string} Generated test email
     */
    generateTestEmail(prefix = 'test') {
        const timestamp = Date.now();
        const brand = this.testDataManager.brand;
        return `${prefix}-${brand}-${timestamp}@malaberg.com`;
    }

    /**
     * Get expected email subject for email type
     * @param {string} emailType - Email type
     * @returns {string} Expected email subject
     */
    getExpectedEmailSubject(emailType) {
        try {
            return this.testDataManager.getOrderConfirmationEmailSubject();
        } catch (error) {
            const subjects = {
                'order_confirmation': `${this.testDataManager.brand.toUpperCase()} Order Confirmation`,
                'abandoned_cart': 'You left something behind',
                'welcome': `Welcome To ${this.testDataManager.brand.toUpperCase()}!`
            };

            return subjects[emailType] || 'Test Email';
        }
    }

    /**
     * Get available PayPal payment method from test data
     * @returns {string} Available PayPal payment method key
     */
    getAvailablePayPalPaymentMethod() {
        // Try common PayPal payment method names
        const paypalMethods = ['paypal_sandbox', 'paypal_valid', 'paypal', 'paypal_test'];

        for (const method of paypalMethods) {
            try {
                this.testDataManager.getPaymentMethod(method);
                return method;
            } catch (error) {
                // Continue to next method
            }
        }

        // Fallback to stripe if no PayPal method found
        console.warn('[TestDataHelper] No PayPal payment method found, falling back to stripe_valid');
        return 'stripe_valid';
    }

    /**
     * Get API configuration for backend operations
     * @returns {Object} API configuration
     */
    getApiConfig() {
        return {
            baseUrl: this.testDataManager.getBaseUrl(),
            brand: this.testDataManager.brand,
            environment: this.testDataManager.environment,
            timeout: 30000
        };
    }

    /**
     * Get database configuration for database operations
     * @returns {Object} Database configuration
     */
    getDatabaseConfig() {
        // Return database configuration based on environment
        return {
            environment: this.testDataManager.environment,
            brand: this.testDataManager.brand,
            useSSHTunnel: true // Based on memory that SSH tunneling is required
        };
    }

    /**
     * Get funnel configuration
     * @param {string} funnelKey - Funnel configuration key
     * @returns {Object} Funnel configuration
     */
    getFunnelConfig(funnelKey = 'default') {
        try {
            return this.testDataManager.getFunnelConfig(funnelKey);
        } catch (error) {
            console.warn(`[TestDataHelper] Funnel config '${funnelKey}' not found, using default`);
            return {
                product: this.getBrandDefaultProduct(),
                upsells: [],
                entry: { url: '/checkout' },
                upsell: { url: '/upsell' }
            };
        }
    }

    /**
     * Get admin credentials for admin panel access
     * @returns {Object} Admin credentials
     */
    getAdminCredentials() {
        try {
            return this.testDataManager.getAdminCredentials();
        } catch (error) {
            console.warn('[TestDataHelper] Admin credentials not found in test data');
            return {
                username: process.env.ADMIN_USERNAME || 'admin',
                password: process.env.ADMIN_PASSWORD || 'password'
            };
        }
    }

    /**
     * Validate test data completeness
     * @param {Object} testData - Test data to validate
     * @returns {boolean} True if valid
     * @throws {Error} If validation fails
     */
    validateTestData(testData) {
        const required = ['product', 'user', 'paymentMethod'];

        for (const field of required) {
            if (!testData[field]) {
                throw new Error(`Missing required test data field: ${field}`);
            }
        }

        console.log('[TestDataHelper] Test data validation passed');
        return true;
    }
}

module.exports = { TestDataHelper };
