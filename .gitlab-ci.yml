# GitLab CI/CD configuration for Browserstack Playwright tests

stages:
  - test
  - report

variables:
  NODE_VERSION: "16"
  TEST_ENV: "stage"
  BROWSER: "chromium"
  DEVICE: "desktop"
  COMMIT_SHA: $CI_COMMIT_SHA
  TIMESTAMP: $CI_COMMIT_TIMESTAMP
  # Add Mailtrap variables
  MAILTRAP_TOKEN: $MAILTRAP_TOKEN
  MAILTRAP_AEONS_INBOX_ID: $MAILTRAP_AEONS_INBOX_ID
  MAILTRAP_ACCOUNT_ID: $MAILTRAP_ACCOUNT_ID

.test_job_template: &test_job_definition
  image: node:${NODE_VERSION}
  stage: test
  before_script:
    - npm ci
  artifacts:
    paths:
      - test-results/
      - playwright-report/
    reports:
      junit: test-results/junit-report.xml
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "pipeline"
      when: always

run_tests:
  <<: *test_job_definition
  script:
    - echo "Running tests for environment $TEST_ENV"
    - npm run ci:test
    - node ./ci/run-env-tests.js --test-file=tests/regression/dss/

generate_report:
  image: node:${NODE_VERSION}
  stage: report
  script:
    - echo "Generating test report"
    - npm ci
    - npm run report
  artifacts:
    paths:
      - playwright-report/
    expire_in: 1 week
  needs:
    - run_tests
  rules:
    - if: $CI_PIPELINE_SOURCE == "pipeline"
      when: always

manual_test:
  stage: test
  image: mcr.microsoft.com/playwright:v1.40.0-focal
  before_script:
    - npm ci
    - npx playwright install --with-deps
  script:
    - echo "Running tests for ${TEST_ENV} environment with ${BROWSER} browser on ${DEVICE} device"
    - echo "Commit SHA - ${COMMIT_SHA}, Timestamp - ${TIMESTAMP}"
    - |
      # Set BUILD_NAME environment variable
      export BUILD_NAME="trigger_${TEST_ENV}_${COMMIT_SHA:0:8}_${TIMESTAMP}"
      
      # Determine appropriate test script based on environment and device
      if [ "$TEST_ENV" = "stage" ]; then
        if [ "$DEVICE" = "mobile" ]; then
          echo "Running staging tests on Samsung Galaxy S23"
          npm run ci:test:stage:android
        else
          echo "Running staging tests on desktop ${BROWSER}"
          npm run ci:test:stage:${BROWSER}
        fi
      elif [ "$TEST_ENV" = "dev" ]; then
        if [ "$BROWSER" = "chromium" ]; then
          BROWSER_SCRIPT="chrome"
        else
          BROWSER_SCRIPT="$BROWSER"
        fi
        if [ "$DEVICE" = "mobile" ]; then
          echo "Running dev tests on Samsung Galaxy S23"
          npm run ci:test:dev:android
        else
          echo "Running dev tests on desktop ${BROWSER_SCRIPT}"
          npm run ci:test:dev:${BROWSER_SCRIPT}
        fi
      else
        echo "Unknown environment: ${TEST_ENV}"
        exit 1
      fi
  artifacts:
    when: always
    paths:
      - test-results/
      - playwright-report/
    reports:
      junit: test-results/results.xml
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "trigger"
      when: always
    - if: $CI_PIPELINE_SOURCE == "web"
      when: always 