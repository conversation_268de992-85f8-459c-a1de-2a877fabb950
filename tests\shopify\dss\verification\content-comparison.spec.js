/**
 * @fileoverview Content comparison tests between baseline and Shopify versions
 */

const { test, expect } = require('@playwright/test');
const { ProductPage } = require('../../../../tests/regression/pages/product.page');
const { TestHelper } = require('../../../common/helpers/test-helper');
const { PRODUCT_CONTENT_RULES } = require('../../../common/fixtures/content-rules');

test.describe('Content Comparison Tests @shopify', () => {
    let baselinePage;
    let shopifyPage;
    let testHelper;

    test.beforeEach(async ({ browser }, testInfo) => {
        // Create separate contexts for baseline and Shopify
        const baselineContext = await browser.newContext();
        const shopifyContext = await browser.newContext();

        baselinePage = new ProductPage(await baselineContext.newPage());
        shopifyPage = new ProductPage(await shopifyContext.newPage());
        
        testHelper = new TestHelper();
        await Promise.all([
            testHelper.initTest(baselinePage.page, testInfo),
            testHelper.initTest(shopifyPage.page, testInfo)
        ]);
    });

    test.afterEach(async ({ }, testInfo) => {
        if (process.env.BROWSERSTACK_SESSION_ID) {
            await testHelper.browserStack.downloadArtifacts(process.env.BROWSERSTACK_SESSION_ID);
        }
    });

    for (const [productHandle, rules] of Object.entries(PRODUCT_CONTENT_RULES)) {
        test(`Content comparison - ${productHandle}`, async ({ }, testInfo) => {
            // Navigate to both versions
            await Promise.all([
                baselinePage.navigateToProduct(productHandle),
                shopifyPage.navigateToProduct(productHandle)
            ]);

            // Initial full page comparison
            await testHelper.comparePages(
                baselinePage.page,
                shopifyPage.page,
                `${productHandle}-full-page`,
                {
                    testName: testInfo.title,
                    type: 'content',
                    section: 'full-page'
                }
            );

            // Compare basic product information
            const [baselineInfo, shopifyInfo] = await Promise.all([
                baselinePage.getProductInfo(),
                shopifyPage.getProductInfo()
            ]);

            expect(shopifyInfo.title, 'Product titles should match')
                .toBe(baselineInfo.title);
            expect(shopifyInfo.price, 'Product prices should match')
                .toBe(baselineInfo.price);
            expect(shopifyInfo.description, 'Product descriptions should match')
                .toBe(baselineInfo.description);

            // Compare product images
            const [baselineImages, shopifyImages] = await Promise.all([
                baselinePage.getProductImages(),
                shopifyPage.getProductImages()
            ]);

            expect(shopifyImages.length, 'Should have same number of images')
                .toBe(baselineImages.length);

            // Compare image galleries
            await testHelper.comparePages(
                baselinePage.page,
                shopifyPage.page,
                `${productHandle}-gallery`,
                {
                    testName: testInfo.title,
                    type: 'content',
                    section: 'gallery'
                }
            );

            // Compare required sections
            for (const section of rules.requiredSections) {
                // Verify section existence
                const [baselineHasSection, shopifyHasSection] = await Promise.all([
                    baselinePage.hasSection(section),
                    shopifyPage.hasSection(section)
                ]);

                expect(shopifyHasSection, `${section} section should exist in Shopify version`)
                    .toBe(baselineHasSection);

                if (baselineHasSection && shopifyHasSection) {
                    // Scroll to sections for comparison
                    await Promise.all([
                        baselinePage.scrollToSection(section),
                        shopifyPage.scrollToSection(section)
                    ]);

                    // Compare section content
                    const [baselineContent, shopifyContent] = await Promise.all([
                        baselinePage.getSectionContent(section),
                        shopifyPage.getSectionContent(section)
                    ]);

                    // Visual comparison of the section
                    await testHelper.comparePages(
                        baselinePage.page,
                        shopifyPage.page,
                        `${productHandle}-${section}`,
                        {
                            testName: testInfo.title,
                            type: 'content',
                            section
                        }
                    );

                    // Content rules verification
                    const rule = rules.contentRules.find(r => r.section === section);
                    if (rule) {
                        // Length checks
                        if (rule.minLength) {
                            expect(shopifyContent.length, `${section} content should meet minimum length`)
                                .toBeGreaterThanOrEqual(rule.minLength);
                        }
                        if (rule.maxLength) {
                            expect(shopifyContent.length, `${section} content should not exceed maximum length`)
                                .toBeLessThanOrEqual(rule.maxLength);
                        }

                        // Required content checks
                        const normalizedShopifyContent = shopifyContent.trim().toLowerCase();
                        for (const word of rule.mustInclude || []) {
                            expect(normalizedShopifyContent, `${section} should include "${word}"`)
                                .toContain(word.toLowerCase());
                        }
                        for (const word of rule.cannotInclude || []) {
                            expect(normalizedShopifyContent, `${section} should not include "${word}"`)
                                .not.toContain(word.toLowerCase());
                        }
                    }
                }
            }

            // Compare meta tags
            const [baselineMeta, shopifyMeta] = await Promise.all([
                baselinePage.getMetaTags(),
                shopifyPage.getMetaTags()
            ]);

            for (const [tag, content] of Object.entries(baselineMeta)) {
                expect(shopifyMeta[tag], `Meta tag ${tag} should exist in Shopify version`)
                    .toBeTruthy();
                expect(shopifyMeta[tag], `Meta tag ${tag} should match baseline`)
                    .toBe(content);

                if (rules.metaTags?.[tag]?.minLength) {
                    expect(shopifyMeta[tag].length, `Meta tag ${tag} should meet minimum length`)
                        .toBeGreaterThanOrEqual(rules.metaTags[tag].minLength);
                }
            }
        });

        test(`Text quality comparison - ${productHandle}`, async ({ }, testInfo) => {
            // Navigate to both versions
            await Promise.all([
                baselinePage.navigateToProduct(productHandle),
                shopifyPage.navigateToProduct(productHandle)
            ]);

            for (const rule of rules.textQualityRules || []) {
                // Scroll to sections for comparison
                await Promise.all([
                    baselinePage.scrollToSection(rule.section),
                    shopifyPage.scrollToSection(rule.section)
                ]);

                // Compare section content
                const [baselineContent, shopifyContent] = await Promise.all([
                    baselinePage.getSectionContent(rule.section),
                    shopifyPage.getSectionContent(rule.section)
                ]);

                // Visual comparison with text quality focus
                await testHelper.comparePages(
                    baselinePage.page,
                    shopifyPage.page,
                    `${productHandle}-${rule.section}-quality`,
                    {
                        testName: testInfo.title,
                        type: 'text-quality',
                        section: rule.section
                    }
                );

                // Text quality checks
                if (rule.sentenceCount) {
                    const shopifySentences = shopifyContent.split(/[.!?]+/).filter(s => s.trim().length > 0);
                    expect(shopifySentences.length, `${rule.section} should have appropriate number of sentences`)
                        .toBeGreaterThanOrEqual(rule.sentenceCount.min || 0);
                    if (rule.sentenceCount.max) {
                        expect(shopifySentences.length).toBeLessThanOrEqual(rule.sentenceCount.max);
                    }
                }

                if (rule.paragraphCount) {
                    const shopifyParagraphs = shopifyContent.split(/\n\n+/).filter(p => p.trim().length > 0);
                    expect(shopifyParagraphs.length, `${rule.section} should have appropriate number of paragraphs`)
                        .toBeGreaterThanOrEqual(rule.paragraphCount.min || 0);
                    if (rule.paragraphCount.max) {
                        expect(shopifyParagraphs.length).toBeLessThanOrEqual(rule.paragraphCount.max);
                    }
                }

                const normalizedShopifyContent = shopifyContent.trim().toLowerCase();

                // Check for prohibited phrases
                for (const phrase of rule.prohibitedPhrases || []) {
                    expect(normalizedShopifyContent, `${rule.section} should not contain "${phrase}"`)
                        .not.toContain(phrase.toLowerCase());
                }

                // Check for required phrases
                for (const phrase of rule.requiredPhrases || []) {
                    expect(normalizedShopifyContent, `${rule.section} should contain "${phrase}"`)
                        .toContain(phrase.toLowerCase());
                }
            }
        });
    }
}); 
