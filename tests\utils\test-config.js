/**
 * @fileoverview Test configuration utilities
 */

const path = require('path');
const yaml = require('js-yaml');
const fs = require('fs');

class PageStabilityHelper {
    /**
     * Wait for page stability
     * @param {import('@playwright/test').Page} page
     */
    static async waitForPageStability(page) {
        // Wait for network idle
        await page.waitForLoadState('networkidle');
        // Wait for any animations to complete
        await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 500)));
    }
}

class BrowserStackConfig {
    constructor() {
        this.config = this.loadConfig();
    }

    loadConfig() {
        try {
            const configPath = path.join(process.cwd(), 'browserstack.yml');
            return yaml.load(fs.readFileSync(configPath, 'utf8'));
        } catch (error) {
            console.error('Error loading browserstack.yml:', error);
            return {};
        }
    }

    /**
     * Get platform configuration
     * @param {string} platformName
     * @returns {Object} Platform configuration
     */
    getPlatformConfig(platformName) {
        return this.config.platforms.find(p => p.name === platformName) || {};
    }

    /**
     * Get visual testing configuration
     * @returns {Object} Visual testing configuration
     */
    getVisualConfig() {
        return this.config.visualConfig || {};
    }
}

module.exports = {
    PageStabilityHelper,
    BrowserStackConfig
}; 