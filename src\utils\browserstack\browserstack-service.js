/**
 * @fileoverview Unified BrowserStack Service
 * Provides a single interface for all BrowserStack operations
 * Integrates with session management and includes support for screenshots and Gemini AI analysis
 */

const fs = require('fs').promises;
const path = require('path');
const { SessionManager } = require('../session-manager');

// Fix ES Module loading issue by using dynamic import
let fetchModule;
async function getFetch() {
    if (!fetchModule) {
        try {
            fetchModule = require('node-fetch');
        } catch (e) {
            const module = await import('node-fetch');
            fetchModule = module.default;
        }
    }
    return fetchModule;
}

/**
 * BrowserStack Service - Single source of truth for BrowserStack integration
 * Combines all functionality from previous helper classes into a single service
 */
class BrowserStackService {
    /**
     * Singleton instance
     * @type {BrowserStackService}
     * @private
     */
    static #instance;

    /**
     * Get singleton instance
     * @returns {BrowserStackService}
     */
    static getInstance() {
        if (!BrowserStackService.#instance) {
            BrowserStackService.#instance = new BrowserStackService();
        }
        return BrowserStackService.#instance;
    }

    /**
     * Constructor
     * @private
     */
    constructor() {
        this.browserStackApiUrl = 'https://api.browserstack.com/automate/';
        this.auth = Buffer.from(
            `${process.env.BROWSERSTACK_USERNAME || ''}:${process.env.BROWSERSTACK_ACCESS_KEY || ''}`
        ).toString('base64');

        // Session tracking
        this.sessionManager = SessionManager.getInstance();
        this.activeSessionId = null;
        this.testId = process.env.TEST_ID || `test_${Date.now()}`;

        // Platform detection
        this.platformName = process.env.PLATFORM || 'windows-chrome';
        this.isAndroid = this.platformName.includes('android') || this.platformName.includes('galaxy');
        this.isIOS = this.platformName.includes('ios') || this.platformName.includes('iphone');
        this.isMobile = this.isAndroid || this.isIOS || process.env.IS_MOBILE === 'true';

        // BrowserStack SDK mode detection
        this.isSdkMode = process.env.BROWSERSTACK_SDK_ENABLED === 'true';

        // Device information
        this.deviceInfo = {
            name: this.getPlatformDisplayName(),
            isMobile: this.isMobile
        };

        // Only log BrowserStack initialization if credentials are present
        if (process.env.BROWSERSTACK_USERNAME && process.env.BROWSERSTACK_ACCESS_KEY) {
            console.log(`BrowserStack Service initialized for platform: ${this.platformName}`);
            console.log(`BrowserStack SDK mode: ${this.isSdkMode ? 'ENABLED' : 'DISABLED'}`);
            console.log(`Device type: ${this.isMobile ? 'Mobile' : 'Desktop'}`);
        } else {
            console.log(`BrowserStack Service initialized in local mode (no BrowserStack credentials)`);
        }
    }

    /**
     * Get a user-friendly display name for the platform
     * @returns {string} The display name for the platform
     * @private
     */
    getPlatformDisplayName() {
        switch (this.platformName) {
            case 'windows-chrome':
                return 'Chrome on Windows';
            case 'mac-safari':
                return 'Safari on Mac';
            case 'samsung-galaxy-s23':
                return 'Samsung Galaxy S23';
            case 'iphone-14':
                return 'iPhone 14';
            default:
                return this.platformName;
        }
    }

    /**
     * Fetch wrapper with authentication
     * @param {string} url - API URL
     * @param {Object} options - Fetch options
     * @returns {Promise<Response>}
     * @private
     */
    async _fetch(url, options) {
        const fetch = await getFetch();
        return fetch(url, {
            ...options,
            headers: {
                'Authorization': `Basic ${this.auth}`,
                'Content-Type': 'application/json',
                ...(options.headers || {})
            }
        });
    }

    /**
     * Register a session with BrowserStack
     * @param {string} sessionId - BrowserStack session ID
     * @param {import('@playwright/test').BrowserContext} [context] - Browser context
     * @returns {Promise<void>}
     */
    async registerSession(sessionId, context = null) {
        if (!sessionId) {
            console.warn('No BrowserStack session ID provided for registration');
            return;
        }

        console.log(`Registering BrowserStack session: ${sessionId}`);

        // Skip custom session management when in SDK mode
        if (this.isSdkMode) {
            console.log('Running in BrowserStack SDK mode - session will be managed by SDK');
            this.activeSessionId = sessionId;
            return;
        }

        // If we already have an active session and it's different, close the previous session
        if (this.activeSessionId && this.activeSessionId !== sessionId) {
            console.warn(`Another session is active (${this.activeSessionId}). Will be replaced with ${sessionId}.`);
            try {
                await this.closeSession(this.activeSessionId);
            } catch (error) {
                console.error('Error closing previous session:', error);
            }
        }

        this.activeSessionId = sessionId;

        // Register with session manager if context is provided
        if (context) {
            await this.sessionManager.registerSession(
                this.testId,
                context,
                {
                    force: true,
                    metadata: {
                        browserstack: true,
                        browserstackSessionId: sessionId,
                        platform: this.platformName
                    }
                }
            );
        }
    }

    /**
     * Close a BrowserStack session
     * @param {string} sessionId - BrowserStack session ID
     * @returns {Promise<boolean>} - Success status
     */
    async closeSession(sessionId) {
        if (!sessionId) return false;

        // Skip custom session management when in SDK mode
        if (this.isSdkMode) {
            console.log('Running in BrowserStack SDK mode - session will be managed by SDK');
            if (sessionId === this.activeSessionId) {
                this.activeSessionId = null;
            }
            return true;
        }

        try {
            console.log(`Closing BrowserStack session: ${sessionId}`);
            const response = await this._fetch(
                `${this.browserStackApiUrl}sessions/${sessionId}.json`,
                {
                    method: 'PUT',
                    body: JSON.stringify({
                        status: 'completed'
                    })
                }
            );

            const result = await response.json();

            // Clean up session manager
            if (sessionId === this.activeSessionId) {
                this.activeSessionId = null;
                await this.sessionManager.closeSession(this.testId);
            }

            return result.status === 'completed';
        } catch (error) {
            console.error('Error closing BrowserStack session:', error);
            return false;
        }
    }

    /**
     * Set the status of a test in BrowserStack
     * @param {string} status - 'passed' or 'failed'
     * @param {string} reason - Reason for the status
     * @returns {Promise<boolean>} - Success status
     */
    async setTestStatus(status, reason) {
        if (!this.activeSessionId) {
            console.warn('No active BrowserStack session found, cannot set test status');
            return false;
        }

        // In SDK mode, we don't need to manually set test status
        if (this.isSdkMode) {
            console.log(`SDK mode enabled - test status (${status}) will be managed by SDK`);
            return true;
        }

        try {
            console.log(`Setting BrowserStack session ${this.activeSessionId} status to ${status}: ${reason}`);
            const response = await this._fetch(
                `${this.browserStackApiUrl}sessions/${this.activeSessionId}.json`,
                {
                    method: 'PUT',
                    body: JSON.stringify({
                        status,
                        reason
                    })
                }
            );

            const result = await response.json();
            return result.status === status;
        } catch (error) {
            console.error('Error setting test status:', error);
            return false;
        }
    }

    /**
     * Take a screenshot using VisualAnalysisHelper
     * @param {import('@playwright/test').Page} page - Playwright page
     * @param {string} name - Screenshot name
     * @param {Object} options - Screenshot options
     * @returns {Promise<Object>} - Screenshot metadata including Cloudinary URL
     */
    async takeScreenshot(page, name, options = {}) {
        const { VisualAnalysisHelper } = require('../visual-analisys-helper');

        const testName = options.testName || this.testId;

        // Add platform info to options
        const enhancedOptions = {
            ...options,
            cdpMeta: {
                _requestedPlatform: this.platformName,
                _actualPlatform: this.platformName,
                _requestedDeviceType: this.deviceInfo.name,
                browserStackSession: this.activeSessionId
            }
        };

        // Use the correct screenshot method
        return await VisualAnalysisHelper.captureAndUploadScreenshot(
            page,
            testName,
            name,
            enhancedOptions
        );
    }

    /**
     * Wait for the page to be visually stable
     * @param {import('@playwright/test').Page} page - Playwright page
     * @param {Object} options - Stability options
     * @returns {Promise<void>}
     */
    async waitForVisualStability(page, options = {}) {
        const defaultOptions = {
            timeout: 10000,
            maxAttempts: 3,
            stabilityThreshold: 500
        };

        const config = { ...defaultOptions, ...options };
        let lastSnapshot = '';
        let stableCount = 0;
        let attempts = 0;

        while (attempts < config.maxAttempts) {
            // Wait for network and animations
            await page.waitForLoadState('networkidle');
            await page.evaluate(() => document.fonts.ready);

            // Get current DOM snapshot
            const snapshot = await page.evaluate(() => document.documentElement.outerHTML);

            if (snapshot === lastSnapshot) {
                stableCount++;
                if (stableCount >= 2) break;
            } else {
                stableCount = 0;
            }

            lastSnapshot = snapshot;
            attempts++;
            await page.waitForTimeout(config.stabilityThreshold);
        }
    }

    /**
     * Get all session screenshots for analysis
     * @returns {Promise<Array>} - Array of screenshot data
     */
    async getSessionScreenshots() {
        return this.sessionManager.getScreenshots(this.testId);
    }

    /**
     * Get the platform capabilities for the current platform
     * @returns {Object} - Platform capabilities
     */
    getPlatformCapabilities() {
        // This function will extract the correct capabilities from browserstack.yml
        // based on the PLATFORM environment variable
        const defaultCapabilities = {
            'browserstack.debug': true,
            'browserstack.console': 'info',
            'browserstack.networkLogs': true,
            project: process.env.PROJECT_NAME || 'Browserstack-Playwright',
            build: process.env.BUILD_NAME || `Test_Run_${new Date().toISOString().replace(/[:.]/g, '_')}`
        };

        // Select capabilities based on platform
        if (this.isAndroid) {
            return {
                ...defaultCapabilities,
                deviceName: 'Samsung Galaxy S23',
                os_version: '13.0',
                browserName: 'chrome',
                browser_version: 'latest',
                real_mobile: true
            };
        } else if (this.isIOS) {
            return {
                ...defaultCapabilities,
                deviceName: 'iPhone 14',
                os_version: '16.0',
                browserName: 'safari',
                browser_version: '16.0',
                real_mobile: true
            };
        } else if (this.platformName.includes('safari')) {
            return {
                ...defaultCapabilities,
                os: 'OS X',
                os_version: 'Sonoma',
                browser: 'safari',
                browser_version: 'latest'
            };
        } else {
            // Default to Chrome on Windows
            return {
                ...defaultCapabilities,
                os: 'Windows',
                os_version: '11',
                browser: 'chrome',
                browser_version: 'latest'
            };
        }
    }

    /**
     * Check if the current environment is using BrowserStack
     * @returns {boolean}
     */
    isBrowserStackEnabled() {
        return !!process.env.BROWSERSTACK_USERNAME && !!process.env.BROWSERSTACK_ACCESS_KEY;
    }

    /**
     * Get device capabilities for BrowserStack
     * @returns {Object} BrowserStack capabilities
     */
    getDeviceCapabilities() {
        return this.getPlatformCapabilities();
    }
}

module.exports = { BrowserStackService };
