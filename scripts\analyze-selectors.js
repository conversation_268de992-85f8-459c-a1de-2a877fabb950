const playwright = require('playwright');

(async () => {
  const browser = await playwright.chromium.launch();
  const page = await browser.newPage();
  
  try {
    await page.goto('https://aeonstest.info/products/aeons-ancient-roots-olive-oil');
    await page.waitForLoadState('networkidle');
    
    // Get the page content
    const content = await page.content();
    
    // Save to file
    const fs = require('fs');
    fs.writeFileSync('page-structure.html', content);
    
    console.log('Page structure saved to page-structure.html');
    
    // More thorough element analysis
    const analysis = {
      title: {
        selectors: [
          '.title',
          '.product-title',
          'h1',
          '.main p.title'
        ],
        results: {}
      },
      form: {
        selectors: ['#sylius-product-adding-to-cart'],
        results: {}
      },
      price: {
        selectors: [
          '#product-price',
          '.product-price .current',
          '.product-price .amount'
        ],
        results: {}
      },
      purchaseType: {
        selectors: [
          '.purchase-option',
          '.product-variant-label-info',
          '[data-variant-option-subscription]'
        ],
        results: {}
      },
      quantity: {
        selectors: [
          '.set-quantity',
          '[data-value]',
          '#sylius_add_to_cart_cartItem_quantity'
        ],
        results: {}
      },
      flavor: {
        selectors: [
          '.flavor-options',
          '.flavor',
          '[role="radiogroup"]',
          '[data-variant-code]'
        ],
        results: {}
      }
    };

    // Test each selector and get details
    for (const [category, data] of Object.entries(analysis)) {
      for (const selector of data.selectors) {
        const elements = await page.$$(selector);
        if (elements.length > 0) {
          const details = await Promise.all(elements.map(async (el) => {
            const text = await el.textContent().catch(() => '');
            const classes = await el.evaluate(node => Array.from(node.classList)).catch(() => []);
            const attributes = await el.evaluate(node => {
              const attrs = {};
              for (const attr of node.attributes) {
                attrs[attr.name] = attr.value;
              }
              return attrs;
            }).catch(() => ({}));
            
            return {
              text: text?.trim(),
              classes,
              attributes,
              exists: true
            };
          }));
          
          data.results[selector] = details;
        }
      }
    }
    
    console.log('Detailed analysis:', JSON.stringify(analysis, null, 2));
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await browser.close();
  }
})(); 