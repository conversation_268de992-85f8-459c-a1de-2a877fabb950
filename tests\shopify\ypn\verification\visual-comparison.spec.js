/**
 * @fileoverview Visual comparison tests between YourPetNutrition Shopify site versions
 */

const { test, expect } = require('@playwright/test');
const { BrowserStackHelper } = require('../../../../src/utils/browserstack/browserstack-helper');
const { TestHelper } = require('../../../common/helpers/test-helper');
const TestDataManager = require('../../../../tests/data/test-data-manager');

test.describe('YourPetNutrition Visual Comparison Tests @shopify @ypn @visual', () => {
    let page;
    let bsHelper;
    let testHelper;
    let testDataManager;
    let shopUrl;
    let shopPassword;

    test.beforeEach(async ({ browser }, testInfo) => {
        // Initialize test data manager
        testDataManager = new TestDataManager();
        testDataManager.initialize('default', 'ypn', process.env.TEST_ENV || 'stage');
        
        // Get configuration from YAML
        const config = testDataManager.getBrandConfig();
        shopUrl = config.url;
        shopPassword = config.password;
        
        // Create a new context and page for each test
        const context = await browser.newContext();
        page = await context.newPage();
        
        // Initialize helpers
        bsHelper = new BrowserStackHelper();
        testHelper = new TestHelper();
        
        await testHelper.initTest(page, testInfo);
        
        // Navigate to the password-protected store
        await page.goto(shopUrl);
        await page.waitForLoadState('networkidle');
        
        // Enter store password
        await page.fill('input[type="password"]', shopPassword);
        await page.click('button:has-text("Enter")');
        
        // Wait for the store to load
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
    });

    test.afterEach(async ({ }, testInfo) => {
        if (process.env.BROWSERSTACK_SESSION_ID) {
            await bsHelper.downloadArtifacts(process.env.BROWSERSTACK_SESSION_ID);
        }
    });

    test('Homepage Visual Test', async ({ }, testInfo) => {
        // Take a screenshot of the homepage
        await bsHelper.takeScreenshot(page, 'ypn-homepage-visual', {
            testName: testInfo.title,
            fullPage: true,
            type: 'visual'
        });
        
        // Verify key elements are visible
        await expect(page.locator('.header')).toBeVisible();
        await expect(page.locator('text=Your Pet\'s Nutrition Is Our Mission')).toBeVisible();
        
        // Check for trust badges
        await expect(page.locator('text=FREE SHIPPING')).toBeVisible();
        await expect(page.locator('text=FORMULATED FOR RESULTS')).toBeVisible();
        await expect(page.locator('text=EXTRAORDINARY SUPPORT')).toBeVisible();
    });

    test('Shop Dogs Page Visual Test', async ({ }, testInfo) => {
        // Navigate to Shop Dogs page
        await page.click('text=Shop Dogs');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Take a screenshot of the Shop Dogs page
        await bsHelper.takeScreenshot(page, 'ypn-shop-dogs-visual', {
            testName: testInfo.title,
            fullPage: true,
            type: 'visual'
        });
        
        // Verify page title
        await expect(page.locator('h1:has-text("Dogs")')).toBeVisible();
        
        // Verify all dog products are displayed
        await expect(page.locator('text=Canine Prime')).toBeVisible();
        await expect(page.locator('text=Denta Soft')).toBeVisible();
        await expect(page.locator('text=Relax + Restore')).toBeVisible();
        await expect(page.locator('text=Flexi Protect')).toBeVisible();
    });

    test('Shop Cats Page Visual Test', async ({ }, testInfo) => {
        // Navigate to Shop Cats page
        await page.click('text=Shop Cats');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Take a screenshot of the Shop Cats page
        await bsHelper.takeScreenshot(page, 'ypn-shop-cats-visual', {
            testName: testInfo.title,
            fullPage: true,
            type: 'visual'
        });
        
        // Verify page title
        await expect(page.locator('h1:has-text("Cats")')).toBeVisible();
        
        // Verify all cat products are displayed
        await expect(page.locator('text=Feline 40')).toBeVisible();
        await expect(page.locator('text=Relax + Restore Cats')).toBeVisible();
    });

    test('Product Detail Page Visual Test - Canine Prime', async ({ }, testInfo) => {
        // Navigate to Shop Dogs page
        await page.click('text=Shop Dogs');
        await page.waitForLoadState('networkidle');
        
        // Click on Canine Prime product
        await page.click('text=Canine Prime');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Take a screenshot of the Canine Prime product page
        await bsHelper.takeScreenshot(page, 'ypn-canine-prime-visual', {
            testName: testInfo.title,
            fullPage: true,
            type: 'visual'
        });
        
        // Verify product title
        await expect(page.locator('h1:has-text("Canine Prime")')).toBeVisible();
        
        // Verify product image
        await expect(page.locator('.product__media-wrapper img')).toBeVisible();
        
        // Verify purchase options
        await expect(page.locator('text=Purchase Options')).toBeVisible();
        await expect(page.locator('text=ONE TIME PURCHASE')).toBeVisible();
        await expect(page.locator('text=SUBSCRIBE AND SAVE')).toBeVisible();
        
        // Verify price
        await expect(page.locator('.price')).toBeVisible();
        
        // Verify add to cart button
        await expect(page.locator('button:has-text("Add to cart")')).toBeVisible();
        
        // Verify description tab
        await expect(page.locator('button:has-text("Description")')).toBeVisible();
    });

    test('Responsive Design Test', async ({ }, testInfo) => {
        const viewports = [
            { width: 1920, height: 1080, name: 'desktop' },
            { width: 1024, height: 768, name: 'tablet' },
            { width: 375, height: 812, name: 'mobile' }
        ];
        
        for (const viewport of viewports) {
            // Set viewport size
            await page.setViewportSize(viewport);
            
            // Take screenshot of homepage
            await bsHelper.takeScreenshot(page, `ypn-homepage-${viewport.name}`, {
                testName: testInfo.title,
                fullPage: true,
                type: 'responsive',
                viewport: viewport.name
            });
            
            // Navigate to Shop Dogs page
            await page.click('text=Shop Dogs');
            await page.waitForLoadState('networkidle');
            await bsHelper.waitForVisualStability(page);
            
            // Take screenshot of Shop Dogs page
            await bsHelper.takeScreenshot(page, `ypn-shop-dogs-${viewport.name}`, {
                testName: testInfo.title,
                fullPage: true,
                type: 'responsive',
                viewport: viewport.name
            });
            
            // Click on Canine Prime product
            await page.click('text=Canine Prime');
            await page.waitForLoadState('networkidle');
            await bsHelper.waitForVisualStability(page);
            
            // Take screenshot of product page
            await bsHelper.takeScreenshot(page, `ypn-product-${viewport.name}`, {
                testName: testInfo.title,
                fullPage: true,
                type: 'responsive',
                viewport: viewport.name
            });
            
            // Go back to homepage
            await page.goto(shopUrl);
            await page.waitForLoadState('networkidle');
            
            // Enter store password if needed
            if (await page.locator('input[type="password"]').isVisible()) {
                await page.fill('input[type="password"]', shopPassword);
                await page.click('button:has-text("Enter")');
                await page.waitForLoadState('networkidle');
            }
        }
    });

    test('Cart Page Visual Test', async ({ }, testInfo) => {
        // Navigate to Shop Dogs page
        await page.click('text=Shop Dogs');
        await page.waitForLoadState('networkidle');
        
        // Click on Denta Soft product (since we know it works)
        await page.click('text=Denta Soft');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Click Add to Cart button
        await page.click('button:has-text("Add to cart")');
        await page.waitForTimeout(2000);
        
        // Click on cart icon
        await page.click('.header__icon--cart');
        await page.waitForLoadState('networkidle');
        await bsHelper.waitForVisualStability(page);
        
        // Take a screenshot of the cart page
        await bsHelper.takeScreenshot(page, 'ypn-cart-visual', {
            testName: testInfo.title,
            fullPage: true,
            type: 'visual'
        });
        
        // Verify cart title
        await expect(page.locator('h1:has-text("Your cart")')).toBeVisible();
        
        // Verify product in cart
        await expect(page.locator('text=Denta Soft')).toBeVisible();
        
        // Verify quantity selector
        await expect(page.locator('input[type="number"]')).toBeVisible();
        
        // Verify checkout button
        await expect(page.locator('button:has-text("CHECK OUT")')).toBeVisible();
    });
});
