<testsuites id="" name="" tests="4" failures="2" skipped="2" errors="0" time="-4217.316884">
<testsuite name="regression\aeons\regression\copy-verification.spec.js" timestamp="2025-03-10T17:02:47.851Z" hostname="chromium" tests="4" failures="2" skipped="2" time="63.952" errors="0">
<testcase name="Aeons Website Content Verification › Text Content Verification Tests @text › Verify Total Harmony updated copy" classname="regression\aeons\regression\copy-verification.spec.js" time="51.168">
<failure message="copy-verification.spec.js:50:9 Verify Total Harmony updated copy" type="FAILURE">
<![CDATA[  [chromium] › regression\aeons\regression\copy-verification.spec.js:50:9 › Aeons Website Content Verification › Text Content Verification Tests @text › Verify Total Harmony updated copy › Navigate to Total Harmony product page 

    TimeoutError: page.goto: Timeout 45000ms exceeded.
    Call log:
      - navigating to "https://aeonstest.info/products/aeons-total-harmony", waiting until "load"


      50 |         testWithBrowserStack('Verify Total Harmony updated copy', async ({ page }) => {
      51 |             await testWithBrowserStack.step('Navigate to Total Harmony product page', async () => {
    > 52 |                 await page.goto('https://aeonstest.info/products/aeons-total-harmony');
         |                            ^
      53 |                 await page.waitForLoadState('networkidle');
      54 |             });
      55 |             
        at c:\development\Malaberg\Projects\browserstack-playwright\tests\regression\aeons\regression\copy-verification.spec.js:52:28
        at c:\development\Malaberg\Projects\browserstack-playwright\tests\regression\aeons\regression\copy-verification.spec.js:51:40

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\regression-aeons-regressio-046df--Total-Harmony-updated-copy-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\regression-aeons-regressio-046df--Total-Harmony-updated-copy-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results\regression-aeons-regressio-046df--Total-Harmony-updated-copy-chromium\trace.zip
    Usage:

        npx playwright show-trace test-results\regression-aeons-regressio-046df--Total-Harmony-updated-copy-chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[Registered session test_1741626165768

[[ATTACHMENT|regression-aeons-regressio-046df--Total-Harmony-updated-copy-chromium\test-failed-1.png]]

[[ATTACHMENT|regression-aeons-regressio-046df--Total-Harmony-updated-copy-chromium\video.webm]]

[[ATTACHMENT|regression-aeons-regressio-046df--Total-Harmony-updated-copy-chromium\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="Aeons Website Content Verification › Text Content Verification Tests @text › Verify updated product titles for Olive Oil and Bone Broth" classname="regression\aeons\regression\copy-verification.spec.js" time="12.784">
<failure message="copy-verification.spec.js:120:9 Verify updated product titles for Olive Oil and Bone Broth" type="FAILURE">
<![CDATA[  [chromium] › regression\aeons\regression\copy-verification.spec.js:120:9 › Aeons Website Content Verification › Text Content Verification Tests @text › Verify updated product titles for Olive Oil and Bone Broth › Verify Ancient Roots Olive Oil title 

    Error: expect(received).toContain(expected) // indexOf

    Expected substring: "Ancient Roots Olive Oil"
    Received string:    "The Original Superfood: Upgraded"

      129 |                 // Also verify meta title if accessible
      130 |                 const pageTitle = await page.title();
    > 131 |                 expect(pageTitle).toContain('Ancient Roots Olive Oil');
          |                                   ^
      132 |             });
      133 |             
      134 |             await testWithBrowserStack.step('Verify Nature\'s Gift Bone Broth title', async () => {
        at c:\development\Malaberg\Projects\browserstack-playwright\tests\regression\aeons\regression\copy-verification.spec.js:131:35
        at c:\development\Malaberg\Projects\browserstack-playwright\tests\regression\aeons\regression\copy-verification.spec.js:121:13

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\regression-aeons-regressio-10701-or-Olive-Oil-and-Bone-Broth-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\regression-aeons-regressio-10701-or-Olive-Oil-and-Bone-Broth-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results\regression-aeons-regressio-10701-or-Olive-Oil-and-Bone-Broth-chromium\trace.zip
    Usage:

        npx playwright show-trace test-results\regression-aeons-regressio-10701-or-Olive-Oil-and-Bone-Broth-chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[Registered session test_1741626165768

[[ATTACHMENT|regression-aeons-regressio-10701-or-Olive-Oil-and-Bone-Broth-chromium\test-failed-1.png]]

[[ATTACHMENT|regression-aeons-regressio-10701-or-Olive-Oil-and-Bone-Broth-chromium\video.webm]]

[[ATTACHMENT|regression-aeons-regressio-10701-or-Olive-Oil-and-Bone-Broth-chromium\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="Aeons Website Content Verification › Text Content Verification Tests @text › Verify Sunrise product page copy updates" classname="regression\aeons\regression\copy-verification.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Aeons Website Content Verification › Text Content Verification Tests @text › Verify Sunrise category page copy updates" classname="regression\aeons\regression\copy-verification.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>