/**
 * @fileoverview DashboardPage page object for account dashboard functionality
 */
class DashboardPage {
  /**
   * Initialize the page object
   * @param {import('@playwright/test').Page} page 
   */
  constructor(page) {
    this.page = page;
    this.selectors = {
      logoutLink: 'a.logout',
      dashboardHeader: '.dashboard-header',
      accountMenu: '.account-menu',
      orderHistory: '.order-history',
      orderItems: '.order-item',
      accountDetails: '.account-details',
      subscriptions: '.subscriptions',
      addresses: '.addresses'
    };
  }

  /**
   * Navigate to the dashboard page
   * @param {string} [baseUrl] Optional base URL, defaults to environment variable or fallback
   */
  async navigate(baseUrl) {
    const url = baseUrl || process.env.BASE_URL || 'https://aeonstest.info';
    await this.page.goto(`${url}/account/dashboard`);
    await this.waitForLoad();
  }

  /**
   * Click the logout link
   */
  async clickLogout() {
    await this.page.click(this.selectors.logoutLink);
    await this.page.waitForNavigation({ waitUntil: 'networkidle' });
  }

  /**
   * Check if user is logged in by verifying dashboard elements are visible
   * @returns {Promise<boolean>}
   */
  async isLoggedIn() {
    return await this.page.isVisible(this.selectors.dashboardHeader);
  }

  /**
   * Wait for dashboard page to load completely
   */
  async waitForLoad() {
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForSelector(this.selectors.dashboardHeader, { state: 'visible' });
  }

  /**
   * Get order history items
   * @returns {Promise<Array<{orderNumber: string, date: string, status: string, total: string}>>}
   */
  async getOrderHistory() {
    const orderItems = this.page.locator(this.selectors.orderItems);
    const count = await orderItems.count();
    
    const orders = [];
    for (let i = 0; i < count; i++) {
      const item = orderItems.nth(i);
      orders.push({
        orderNumber: await item.locator('.order-number').textContent(),
        date: await item.locator('.order-date').textContent(),
        status: await item.locator('.order-status').textContent(),
        total: await item.locator('.order-total').textContent()
      });
    }
    
    return orders;
  }

  /**
   * Navigate to a specific section of the account dashboard
   * @param {string} section - The section to navigate to (e.g., 'orders', 'addresses', 'subscriptions')
   */
  async navigateToSection(section) {
    const sectionMap = {
      'orders': '.nav-orders',
      'addresses': '.nav-addresses',
      'subscriptions': '.nav-subscriptions',
      'details': '.nav-account-details'
    };

    const selector = sectionMap[section.toLowerCase()];
    if (!selector) {
      throw new Error(`Unknown section: ${section}`);
    }

    await this.page.click(selector);
    await this.page.waitForLoadState('networkidle');
  }
}

module.exports = { DashboardPage };
