/**
 * SSH Tunnel utility for database connections
 * Based on the examples in docs/Database folder
 */
const tunnelSsh = require('tunnel-ssh');
const tunnel = tunnelSsh.default || tunnelSsh;
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Ensure environment variables are loaded
dotenv.config();

class SSHTunnelUtils {
    constructor() {
        this.tunnel = null;
        this.isConnected = false;
        this.connectionAttempts = 0;
        this.maxConnectionAttempts = 3;
    }

    /**
     * Creates an SSH tunnel to the database server
     * @param {Object} config - SSH tunnel configuration
     * @returns {Promise<Object>} - SSH tunnel
     */
    async createTunnel(config = null) {
        try {
            // Force reload environment variables to ensure they're available
            dotenv.config();

            console.log('[SSHTunnelUtils] Creating SSH tunnel...');

            // Get SSH configuration from environment variables or config
            const sshConfig = config || {
                host: process.env.SSH_HOST || '**************',
                port: parseInt(process.env.SSH_PORT || '22', 10),
                username: process.env.SSH_USER || 'ec2-user',
                privateKey: this.getPrivateKey(),
                dstHost: process.env.DB_HOST || '*************',
                dstPort: parseInt(process.env.DB_PORT || '3306', 10),
                localHost: '127.0.0.1',
                localPort: parseInt(process.env.LOCAL_DB_PORT || '3307', 10)
            };

            console.log(`[SSHTunnelUtils] Connecting to SSH server at ${sshConfig.host}:${sshConfig.port}...`);
            console.log(`[SSHTunnelUtils] Tunneling to database at ${sshConfig.dstHost}:${sshConfig.dstPort}...`);

            // Create tunnel configuration
            const tunnelConfig = {
                host: sshConfig.host,
                port: sshConfig.port,
                username: sshConfig.username,
                privateKey: sshConfig.privateKey,
                dstHost: sshConfig.dstHost,
                dstPort: sshConfig.dstPort,
                localHost: sshConfig.localHost,
                localPort: sshConfig.localPort,
                keepAlive: true
            };

            // Create tunnel
            this.tunnel = await this.createTunnelPromise(tunnelConfig);

            console.log('[SSHTunnelUtils] SSH tunnel established successfully!');
            console.log(`[SSHTunnelUtils] Local database connection available at ${sshConfig.localHost}:${sshConfig.localPort}`);

            this.isConnected = true;
            this.connectionAttempts = 0;

            return {
                host: sshConfig.localHost,
                port: sshConfig.localPort
            };
        } catch (error) {
            this.connectionAttempts++;
            console.error(`[SSHTunnelUtils] SSH tunnel error (attempt ${this.connectionAttempts}/${this.maxConnectionAttempts}): ${error.message}`);

            // If we've reached the maximum number of attempts, throw the error
            if (this.connectionAttempts >= this.maxConnectionAttempts) {
                console.error('[SSHTunnelUtils] Maximum connection attempts reached. Giving up.');
                throw error;
            }

            // Wait 2 seconds before retrying
            console.log('[SSHTunnelUtils] Retrying connection in 2 seconds...');
            await new Promise(resolve => setTimeout(resolve, 2000));
            return this.createTunnel(config);
        }
    }

    /**
     * Creates a promise-based tunnel
     * @param {Object} config - Tunnel configuration
     * @returns {Promise<Object>} - SSH tunnel
     */
    createTunnelPromise(config) {
        return new Promise((resolve, reject) => {
            const server = tunnel(config, (error, server) => {
                if (error) {
                    return reject(error);
                }
                resolve(server);
            });

            server.on('error', (error) => {
                console.error(`[SSHTunnelUtils] Tunnel error: ${error.message}`);
                reject(error);
            });
        });
    }

    /**
     * Gets the private key from the environment or file
     * @returns {Buffer} - Private key buffer
     */
    getPrivateKey() {
        // Try to get private key from environment variable
        if (process.env.SSH_PRIVATE_KEY) {
            console.log('[SSHTunnelUtils] Using SSH private key from environment variable');
            return Buffer.from(process.env.SSH_PRIVATE_KEY, 'utf8');
        }

        // Try to get private key from file
        const keyPath = process.env.SSH_KEY_PATH || path.join(process.cwd(), 'docs', 'Database', 'ssh', 'id_rsa');

        if (fs.existsSync(keyPath)) {
            console.log(`[SSHTunnelUtils] Using SSH private key from file: ${keyPath}`);
            return fs.readFileSync(keyPath);
        }

        // Use the hardcoded key from the example as a last resort
        console.log('[SSHTunnelUtils] Using hardcoded SSH private key from example');
        return Buffer.from(`**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`, 'utf8');
    }

    /**
     * Closes the SSH tunnel
     * @returns {Promise<void>}
     */
    async closeTunnel() {
        try {
            if (this.tunnel) {
                console.log('[SSHTunnelUtils] Closing SSH tunnel...');
                this.tunnel.close();
                this.tunnel = null;
                this.isConnected = false;
                console.log('[SSHTunnelUtils] SSH tunnel closed successfully');
            } else {
                console.log('[SSHTunnelUtils] No active SSH tunnel to close');
            }
        } catch (error) {
            console.error(`[SSHTunnelUtils] Error closing SSH tunnel: ${error.message}`);
            // Reset connection state even if there was an error
            this.tunnel = null;
            this.isConnected = false;
        }
    }
}

module.exports = new SSHTunnelUtils();
