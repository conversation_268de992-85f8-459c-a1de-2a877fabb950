/**
 * @fileoverview HTML templates and styles for visual test reports
 */

const REPORT_TEMPLATES = {
    styles: `
        <style>
            :root {
                --primary-color: #1976d2;
                --error-color: #d32f2f;
                --success-color: #2e7d32;
                --warning-color: #ed6c02;
                --text-primary: #333;
                --text-secondary: #666;
                --border-color: #ccc;
                --background-light: #f5f5f5;
            }

            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                line-height: 1.6;
                color: var(--text-primary);
                margin: 0;
                padding: 2rem;
                background-color: #fff;
            }

            h1, h2, h3, h4, h5 {
                color: var(--text-primary);
                margin-top: 0;
            }

            h1 {
                font-size: 2rem;
                margin-bottom: 2rem;
                padding-bottom: 0.5rem;
                border-bottom: 2px solid var(--primary-color);
            }

            h2 {
                font-size: 1.5rem;
                margin-bottom: 1rem;
            }

            h3 {
                font-size: 1.25rem;
                color: var(--primary-color);
            }

            .section {
                margin: 2rem 0;
                padding: 1.5rem;
                border: 1px solid var(--border-color);
                border-radius: 8px;
                background-color: #fff;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .subsection {
                margin: 1.5rem 0;
                padding: 1rem;
                border-left: 4px solid var(--primary-color);
                background-color: var(--background-light);
                border-radius: 0 4px 4px 0;
            }

            .metadata {
                color: var(--text-secondary);
                font-size: 0.9rem;
                margin-bottom: 1rem;
            }

            pre {
                background-color: var(--background-light);
                padding: 1rem;
                border-radius: 4px;
                overflow-x: auto;
                font-family: 'Fira Code', 'Consolas', monospace;
                font-size: 0.9rem;
                line-height: 1.4;
            }

            .issues {
                color: var(--error-color);
            }

            .improvements {
                color: var(--primary-color);
            }

            .screenshot {
                margin: 1rem 0;
            }

            .screenshot img {
                max-width: 100%;
                height: auto;
                border: 1px solid var(--border-color);
                border-radius: 4px;
            }

            .tabs {
                display: flex;
                gap: 1rem;
                margin-bottom: 1rem;
            }

            .tab {
                padding: 0.5rem 1rem;
                cursor: pointer;
                border: 1px solid var(--border-color);
                border-radius: 4px;
                background-color: #fff;
                transition: all 0.2s ease;
            }

            .tab:hover {
                background-color: var(--background-light);
            }

            .tab.active {
                background-color: var(--primary-color);
                color: #fff;
                border-color: var(--primary-color);
            }

            .status {
                display: inline-block;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                font-size: 0.875rem;
                font-weight: 500;
            }

            .status-error {
                background-color: var(--error-color);
                color: #fff;
            }

            .status-warning {
                background-color: var(--warning-color);
                color: #fff;
            }

            .status-success {
                background-color: var(--success-color);
                color: #fff;
            }

            @media (max-width: 768px) {
                body {
                    padding: 1rem;
                }

                .section {
                    padding: 1rem;
                }

                .subsection {
                    padding: 0.75rem;
                }

                pre {
                    font-size: 0.8rem;
                }
            }
        </style>
    `
};

module.exports = { REPORT_TEMPLATES }; 