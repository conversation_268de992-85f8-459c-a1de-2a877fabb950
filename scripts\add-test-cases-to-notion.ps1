# PowerShell script to add test cases to Notion
# Usage: .\add-test-cases-to-notion.ps1 -NotionApi<PERSON>ey "your_notion_api_key"

param (
    [Parameter(Mandatory=$true)]
    [string]$NotionApiKey
)

# Notion API configuration
$TEST_CASE_DATABASE_ID = "1e5a8ba3-b0b5-81a4-bfa2-e3b89d6b21d6"
$NOTION_API_URL = "https://api.notion.com/v1/pages"

# Read the test cases from the JSON file
$testCasesFilePath = Join-Path -Path $PSScriptRoot -ChildPath "..\docs\apex-shopify-migration-notion-import.json"
$testCasesData = Get-Content -Path $testCasesFilePath -Raw | ConvertFrom-Json

# Headers for Notion API
$headers = @{
    "Authorization" = "Bearer $NotionApiKey"
    "Content-Type" = "application/json"
    "Notion-Version" = "2022-06-28"
}

# Function to add a test case to Notion
function Add-TestCaseToNotion {
    param (
        [Parameter(Mandatory=$true)]
        [PSCustomObject]$TestCase
    )

    $testCaseId = $TestCase.properties.ID.title[0].text.content
    Write-Host "Adding test case: $testCaseId"

    $body = @{
        parent = @{
            database_id = $TEST_CASE_DATABASE_ID
        }
        properties = $TestCase.properties
    } | ConvertTo-Json -Depth 10

    try {
        $response = Invoke-RestMethod -Uri $NOTION_API_URL -Method Post -Headers $headers -Body $body
        Write-Host "Successfully added test case: $testCaseId" -ForegroundColor Green
        return $response
    }
    catch {
        Write-Host "Error adding test case: $testCaseId" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Red
        return $null
    }
}

# Main function to add all test cases
function Add-AllTestCases {
    Write-Host "Starting to add test cases to Notion..." -ForegroundColor Cyan
    
    foreach ($testCase in $testCasesData.test_cases) {
        Add-TestCaseToNotion -TestCase $testCase
        # Add a small delay between requests to avoid rate limiting
        Start-Sleep -Milliseconds 500
    }
    
    Write-Host "Finished adding test cases to Notion." -ForegroundColor Cyan
}

# Execute the main function
Add-AllTestCases
